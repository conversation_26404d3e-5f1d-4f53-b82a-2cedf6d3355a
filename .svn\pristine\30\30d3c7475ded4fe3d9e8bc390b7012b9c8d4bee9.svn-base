﻿#pragma once
#include <vector>
#include <quicklist/QuickList.h>
#include <DataDef.h>

// DlgInstrumentView 对话框
const int factorlistCols = 18;

class DlgInstrumentView : public CDialogEx
{
	DECLARE_DYNAMIC(DlgInstrumentView)

	struct ListText
	{
		char  txt[factorlistCols + 1][64];
		ListText()
		{
			memset(this, 0, sizeof(ListText));
		}
	};


public:
	DlgInstrumentView(CWnd* pParent = nullptr);   // 标准构造函数
	virtual ~DlgInstrumentView();
	void InitFilterResultList();
	void FillFilterResultList(BlockDataPtr blk_ptr = BlockDataPtr());
	void SortColumn();
	std::string GetPrevLabel();
	std::string GetNextLabel();

	std::vector<ListText> _listText;
	static char* _szHeaders[factorlistCols];
	static char* _szMarkStar[6];
	static COLORREF _szMarkColor[6];
	CQuickList m_listFilterResult;
	static int nCurCol;
	static bool bFlag;
	std::vector<std::string> _whis;

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_INSTRUMENT };
#endif

protected:
	BlockDataPtr _blk_ptr;
	int m_nCurSel;
	int _labels_sel;

	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg LRESULT OnGetListItem(WPARAM wParam, LPARAM lParam);
	afx_msg void OnLvnColumnclickListSec(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNMClickListFilterResult(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNMDblclkListFilterResult(NMHDR* pNMHDR, LRESULT* pResult);
};
