#include "Engine.h"
#include "Factor.h"
#include "Utils.h"
#include <stdexcept>
#include <algorithm>

namespace Spectre {

// ============================================================================
// OHLCV???????????
// ============================================================================

std::shared_ptr<BaseFactor> OHLCV::open;
std::shared_ptr<BaseFactor> OHLCV::high;
std::shared_ptr<BaseFactor> OHLCV::low;
std::shared_ptr<BaseFactor> OHLCV::close;
std::shared_ptr<BaseFactor> OHLCV::volume;

void OHLCV::initialize() {
    open = std::static_pointer_cast<BaseFactor>(std::make_shared<DataFactor>("open"));
    high = std::static_pointer_cast<BaseFactor>(std::make_shared<DataFactor>("high"));
    low = std::static_pointer_cast<BaseFactor>(std::make_shared<DataFactor>("low"));
    close = std::static_pointer_cast<BaseFactor>(std::make_shared<DataFactor>("close"));
    volume = std::static_pointer_cast<BaseFactor>(std::make_shared<DataFactor>("volume"));
}

// ============================================================================
// DataFactor???
// ============================================================================

DataFactor::DataFactor(const std::string& column_name) : m_column_name(column_name) {
}

torch::Tensor DataFactor::compute(const std::vector<torch::Tensor>& inputs) {
    // DataFactor?????????????????????????????
    throw std::runtime_error("DataFactor::compute should not be called directly. Use FactorEngine::run instead.");
}

// ============================================================================
// FactorEngine???
// ============================================================================

FactorEngine::FactorEngine() : m_device(torch::kCPU) {
    // ?????OHLCV
    OHLCV::initialize();
}

FactorEngine::~FactorEngine() {
}

void FactorEngine::set_data(const torch::Tensor& data, const std::vector<std::string>& column_names) {
    m_data = data.to(m_device);
    m_column_names = column_names;
    build_column_index_map();
}

void FactorEngine::build_column_index_map() {
    m_column_index_map.clear();
    for (size_t i = 0; i < m_column_names.size(); ++i) {
        m_column_index_map[m_column_names[i]] = static_cast<int>(i);
    }
}

torch::Tensor FactorEngine::get_data(const std::string& column_name) const {
    auto it = m_column_index_map.find(column_name);
    if (it == m_column_index_map.end()) {
        throw std::runtime_error("Column '" + column_name + "' not found in data");
    }
    
    // ??????????? [time, asset, feature]
    return m_data.select(2, it->second);
}

torch::Tensor FactorEngine::run(std::shared_ptr<BaseFactor> factor, 
                                const std::string& start_date, 
                                const std::string& end_date) {
    if (!factor) {
        throw std::runtime_error("Factor is null");
    }
    
    // ???????
    factor->pre_compute(this);
    
    // ???????????
    std::vector<torch::Tensor> inputs;
    
    // ?????DataFactor??????????????????
    if (auto data_factor = std::dynamic_pointer_cast<DataFactor>(factor)) {
        return get_data(data_factor->get_column_name());
    }
    
    // ????CustomFactor???????????????
    if (auto custom_factor = std::dynamic_pointer_cast<CustomFactor>(factor)) {
        // ?????????????????????????????????
        // ????????
    }
    
    // ???????
    torch::Tensor result = factor->compute(inputs);
    
    // ????
    factor->clean_up();
    
    return result;
}

void FactorEngine::column_to_parallel_groupby(const std::string& groupby) {
    // ?????????
}

torch::Tensor FactorEngine::group_by(const torch::Tensor& data, const std::string& groupby) {
    // ?????????
    return data;
}

torch::Tensor FactorEngine::revert(const torch::Tensor& data, const std::string& groupby, const std::string& factor_name) {
    // ????????????
    return data;
}

torch::Tensor FactorEngine::revert_to_series(const torch::Tensor& data, const std::string& groupby, const std::string& factor_name) {
    // ????????????????
    return data;
}

torch::Tensor FactorEngine::get_group_padding_mask(const std::string& groupby) {
    // ??????????????
    return torch::ones_like(m_data.select(2, 0));
}

std::shared_ptr<BaseFactor> FactorEngine::get_filter() {
    return m_universe_filter;
}



} // namespace Spectre
