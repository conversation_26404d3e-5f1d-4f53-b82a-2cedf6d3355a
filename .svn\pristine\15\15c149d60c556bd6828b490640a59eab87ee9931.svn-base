#include "stdafx.h"
#include "WebClient.h"

WebClient::WebClient()
{
}


WebClient::~WebClient()
{
}

void WebClient::OnAfterCreated(CefRefPtr<CefBrowser> browser)
{
	m_Browser = browser;
}

ClientHandler::<PERSON><PERSON><PERSON><PERSON><PERSON>(Delegate* delegate)
	: m_delegate(delegate)
{
}

void ClientHandler::DetachDelegate()
{
	m_delegate = nullptr;
}

void ClientHandler::CreateBrowser(CefWindowInfo const & info, CefBrowserSettings const & settings, CefString const & url)
{
	CefBrowserHost::CreateBrowser(info, this, url, settings, nullptr);
}

void ClientHandler::OnAddressChange(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, const CefString& url)
{
	//CEF_REQUIRE_UI_THREAD();

	// Only update the address for the main (top-level) frame.
	if (frame->IsMain())
	{
		if (m_delegate != nullptr)
			m_delegate->OnSetAddress(url);
	}
}

void ClientHandler::OnTitleChange(CefRefPtr<CefBrowser> browser, const CefString& title)
{
	//CEF_REQUIRE_UI_THREAD();

	if (m_delegate != nullptr)
		m_delegate->OnSetTitle(title);
}

void ClientHandler::OnFullscreenModeChange(CefRefPtr<CefBrowser> browser, bool fullscreen)
{
	//CEF_REQUIRE_UI_THREAD();

	if (m_delegate != nullptr)
		m_delegate->OnSetFullscreen(fullscreen);
}

void ClientHandler::OnAfterCreated(CefRefPtr<CefBrowser> browser)
{
	//CEF_REQUIRE_UI_THREAD();

	if (m_delegate != nullptr)
		m_delegate->OnBrowserCreated(browser);
}

bool ClientHandler::DoClose(CefRefPtr<CefBrowser> browser)
{
	//CEF_REQUIRE_UI_THREAD();

	if (m_delegate != nullptr)
		m_delegate->OnBrowserClosing(browser);

	return false;
}

void ClientHandler::OnBeforeClose(CefRefPtr<CefBrowser> browser)
{
	//CEF_REQUIRE_UI_THREAD();

	if (m_delegate != nullptr)
		m_delegate->OnBrowserClosed(browser);
}

void ClientHandler::OnLoadError(CefRefPtr<CefBrowser> browser,
	CefRefPtr<CefFrame> frame,
	ErrorCode errorCode,
	const CefString& errorText,
	const CefString& failedUrl)
{
	//CEF_REQUIRE_UI_THREAD();

	// Don't display an error for downloaded files.
	if (errorCode == ERR_ABORTED)
		return;

	// Display a load error message.
	std::stringstream ss;
	ss << "<html><body bgcolor=\"white\">"
		"<h2>Failed to load URL " << std::string(failedUrl) <<
		" with error " << std::string(errorText) << " (" << errorCode <<
		").</h2></body></html>";
	frame->LoadString(ss.str(), failedUrl);
}

void ClientHandler::OnLoadingStateChange(CefRefPtr<CefBrowser> browser, bool isLoading, bool canGoBack, bool canGoForward)
{
	//CEF_REQUIRE_UI_THREAD();

	if (m_delegate != nullptr)
		m_delegate->OnSetLoadingState(isLoading, canGoBack, canGoForward);
}