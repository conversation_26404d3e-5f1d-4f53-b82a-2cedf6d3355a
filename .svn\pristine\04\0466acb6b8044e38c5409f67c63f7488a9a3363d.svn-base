#include "stdafx.h"
#include "HdFingerprint.h"
#include "sha.h"
#include <comutil.h>
//#include <common.hpp>
#include <sysinfo.hpp>
#include <fmt/format.h>

HdFingerprint::HdFingerprint(void)
{

}


HdFingerprint::~HdFingerprint(void)
{
}

std::string HdFingerprint::GetHdFpString()
{
	SystemInfo si;
	if (!GetSysInfo(si)) {
		//utils::log_out("generate hardware fingerprint data failed.");
		return "";
	}

	CryptoPP::SHA512 hash;
	BYTE cbHash[CryptoPP::SHA512::DIGESTSIZE] = { 0 };

	//bios
	hash.Update((byte*)si.bios.data(), si.bios.length());
	hash.Final(cbHash);
	byte hv1 = cbHash[0] & 0x3F;
	//processor
	hash.Update((byte*)si.processor.data(), si.processor.length());
	hash.Final(cbHash);
	byte hv2 = cbHash[0] & 0x3F;
	//mac_address
	hash.Update((byte*)si.mac_address.data(), si.mac_address.length());
	hash.Final(cbHash);
	byte hv3 = cbHash[0] & 0x3F;
	//memory
	hash.Update((byte*)si.memory.data(), si.memory.length());
	hash.Final(cbHash);
	byte hv4 = cbHash[0] & 0x3F;
	//network_adapter
	hash.Update((byte*)si.network_adapter.data(), si.network_adapter.length());
	hash.Final(cbHash);
	byte hv5 = cbHash[0] & 0x3F;

	std::string hds = fmt::format("{:02X}{:02X}{:02X}{:02X}{:02X}", hv1, hv2, hv3, hv4, hv5);
	//utils::log_out("hdfingerprint: %s", hds.c_str());

	return hds;
}
