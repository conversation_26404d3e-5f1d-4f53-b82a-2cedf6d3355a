#ifndef C10_UTIL_TYPE_H_
#define C10_UTIL_TYPE_H_

#include <cstddef>
#include <string>
#include <typeinfo>

#include "Macros.h"

namespace c100 {

	/// Utility to demangle a C++ symbol name.
	std::string demangle(const char* name);

	/// Returns the printable name of the type.
	template <typename T>
	inline const char* demangle_type() {
#ifdef __GXX_RTTI
		static const std::string name = demangle(typeid(T).name());
		return name.c_str();
#else // __GXX_RTTI
		return "(RTTI disabled, cannot show name)";
#endif // __GXX_RTTI
	}

} // namespace c100

#endif // C10_UTIL_TYPE_H_
