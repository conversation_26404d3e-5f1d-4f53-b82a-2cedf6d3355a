/* ------------------------------------------------------------------------ */
/* Binary Coded Decimal and Densely Packed Decimal conversion lookup tables */
/* [Automatically generated -- do not edit.  2008.06.21]                    */
/* ------------------------------------------------------------------------ */
/* Copyright (c) IBM Corporation, 2000, 2008. All rights reserved.          */
/* ------------------------------------------------------------------------ */
/* For details, see DPDecimal.html on the General Decimal Arithmetic page.  */
/*                                                                          */
/* This include file defines several DPD and BCD conversion tables:         */
/*                                                                          */
/*   uint16_t BCD2DPD[2458];     -- BCD -> DPD (0x999 => 2457)              */
/*   uint16_t BIN2DPD[1000];     -- Bin -> DPD (999 => 2457)                */
/*   uint8_t  BIN2CHAR[4001];    -- Bin -> CHAR (999 => '\3' '9' '9' '9')   */
/*   uint8_t  BIN2BCD8[4000];    -- Bin -> bytes (999 => 9 9 9 3)           */
/*   uint16_t DPD2BCD[1024];     -- DPD -> BCD (0x3FF => 0x999)             */
/*   uint16_t DPD2BIN[1024];     -- DPD -> BIN (0x3FF => 999)               */
/*   uint32_t DPD2BINK[1024];    -- DPD -> BIN * 1000 (0x3FF => 999000)     */
/*   uint32_t DPD2BINM[1024];    -- DPD -> BIN * 1E+6 (0x3FF => 999000000)  */
/*   uint8_t  DPD2BCD8[4096];    -- DPD -> bytes (x3FF => 9 9 9 3)          */
/*                                                                          */
/* In all cases the result (10 bits or 12 bits, or binary) is right-aligned */
/* in the table entry.  BIN2CHAR entries are a single byte length (0 for    */
/* value 0) followed by three digit characters; a trailing terminator is    */
/* included to allow 4-char moves always.  BIN2BCD8 and DPD2BCD8 entries    */
/* are similar with the three BCD8 digits followed by a one-byte length     */
/* (again, length=0 for value 0).                                           */
/*                                                                          */
/* To use a table, its name, prefixed with DEC_, must be defined with a     */
/* value of 1 before this header file is included.  For example:            */
/*    #define DEC_BCD2DPD 1                                                 */
/* This mechanism allows software to only include tables that are needed.   */
/* ------------------------------------------------------------------------ */
 
#if defined(DEC_BCD2DPD) && DEC_BCD2DPD==1 && !defined(DECBCD2DPD)
#define DECBCD2DPD
 
const uint16_t BCD2DPD[2458]={    0,    1,    2,    3,    4,    5,    6,    7, 
    8,    9,    0,    0,    0,    0,    0,    0,   16,   17,   18,   19,   20, 
   21,   22,   23,   24,   25,    0,    0,    0,    0,    0,    0,   32,   33, 
   34,   35,   36,   37,   38,   39,   40,   41,    0,    0,    0,    0,    0, 
    0,   48,   49,   50,   51,   52,   53,   54,   55,   56,   57,    0,    0, 
    0,    0,    0,    0,   64,   65,   66,   67,   68,   69,   70,   71,   72, 
   73,    0,    0,    0,    0,    0,    0,   80,   81,   82,   83,   84,   85, 
   86,   87,   88,   89,    0,    0,    0,    0,    0,    0,   96,   97,   98, 
   99,  100,  101,  102,  103,  104,  105,    0,    0,    0,    0,    0,    0, 
  112,  113,  114,  115,  116,  117,  118,  119,  120,  121,    0,    0,    0, 
    0,    0,    0,   10,   11,   42,   43,   74,   75,  106,  107,   78,   79, 
    0,    0,    0,    0,    0,    0,   26,   27,   58,   59,   90,   91,  122, 
  123,   94,   95,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,  128,  129,  130,  131,  132,  133,  134,  135,  136,  137,    0,    0, 
    0,    0,    0,    0,  144,  145,  146,  147,  148,  149,  150,  151,  152, 
  153,    0,    0,    0,    0,    0,    0,  160,  161,  162,  163,  164,  165, 
  166,  167,  168,  169,    0,    0,    0,    0,    0,    0,  176,  177,  178, 
  179,  180,  181,  182,  183,  184,  185,    0,    0,    0,    0,    0,    0, 
  192,  193,  194,  195,  196,  197,  198,  199,  200,  201,    0,    0,    0, 
    0,    0,    0,  208,  209,  210,  211,  212,  213,  214,  215,  216,  217, 
    0,    0,    0,    0,    0,    0,  224,  225,  226,  227,  228,  229,  230, 
  231,  232,  233,    0,    0,    0,    0,    0,    0,  240,  241,  242,  243, 
  244,  245,  246,  247,  248,  249,    0,    0,    0,    0,    0,    0,  138, 
  139,  170,  171,  202,  203,  234,  235,  206,  207,    0,    0,    0,    0, 
    0,    0,  154,  155,  186,  187,  218,  219,  250,  251,  222,  223,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,  256,  257,  258, 
  259,  260,  261,  262,  263,  264,  265,    0,    0,    0,    0,    0,    0, 
  272,  273,  274,  275,  276,  277,  278,  279,  280,  281,    0,    0,    0, 
    0,    0,    0,  288,  289,  290,  291,  292,  293,  294,  295,  296,  297, 
    0,    0,    0,    0,    0,    0,  304,  305,  306,  307,  308,  309,  310, 
  311,  312,  313,    0,    0,    0,    0,    0,    0,  320,  321,  322,  323, 
  324,  325,  326,  327,  328,  329,    0,    0,    0,    0,    0,    0,  336, 
  337,  338,  339,  340,  341,  342,  343,  344,  345,    0,    0,    0,    0, 
    0,    0,  352,  353,  354,  355,  356,  357,  358,  359,  360,  361,    0, 
    0,    0,    0,    0,    0,  368,  369,  370,  371,  372,  373,  374,  375, 
  376,  377,    0,    0,    0,    0,    0,    0,  266,  267,  298,  299,  330, 
  331,  362,  363,  334,  335,    0,    0,    0,    0,    0,    0,  282,  283, 
  314,  315,  346,  347,  378,  379,  350,  351,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,  384,  385,  386,  387,  388,  389,  390, 
  391,  392,  393,    0,    0,    0,    0,    0,    0,  400,  401,  402,  403, 
  404,  405,  406,  407,  408,  409,    0,    0,    0,    0,    0,    0,  416, 
  417,  418,  419,  420,  421,  422,  423,  424,  425,    0,    0,    0,    0, 
    0,    0,  432,  433,  434,  435,  436,  437,  438,  439,  440,  441,    0, 
    0,    0,    0,    0,    0,  448,  449,  450,  451,  452,  453,  454,  455, 
  456,  457,    0,    0,    0,    0,    0,    0,  464,  465,  466,  467,  468, 
  469,  470,  471,  472,  473,    0,    0,    0,    0,    0,    0,  480,  481, 
  482,  483,  484,  485,  486,  487,  488,  489,    0,    0,    0,    0,    0, 
    0,  496,  497,  498,  499,  500,  501,  502,  503,  504,  505,    0,    0, 
    0,    0,    0,    0,  394,  395,  426,  427,  458,  459,  490,  491,  462, 
  463,    0,    0,    0,    0,    0,    0,  410,  411,  442,  443,  474,  475, 
  506,  507,  478,  479,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,  512,  513,  514,  515,  516,  517,  518,  519,  520,  521,    0, 
    0,    0,    0,    0,    0,  528,  529,  530,  531,  532,  533,  534,  535, 
  536,  537,    0,    0,    0,    0,    0,    0,  544,  545,  546,  547,  548, 
  549,  550,  551,  552,  553,    0,    0,    0,    0,    0,    0,  560,  561, 
  562,  563,  564,  565,  566,  567,  568,  569,    0,    0,    0,    0,    0, 
    0,  576,  577,  578,  579,  580,  581,  582,  583,  584,  585,    0,    0, 
    0,    0,    0,    0,  592,  593,  594,  595,  596,  597,  598,  599,  600, 
  601,    0,    0,    0,    0,    0,    0,  608,  609,  610,  611,  612,  613, 
  614,  615,  616,  617,    0,    0,    0,    0,    0,    0,  624,  625,  626, 
  627,  628,  629,  630,  631,  632,  633,    0,    0,    0,    0,    0,    0, 
  522,  523,  554,  555,  586,  587,  618,  619,  590,  591,    0,    0,    0, 
    0,    0,    0,  538,  539,  570,  571,  602,  603,  634,  635,  606,  607, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,  640,  641, 
  642,  643,  644,  645,  646,  647,  648,  649,    0,    0,    0,    0,    0, 
    0,  656,  657,  658,  659,  660,  661,  662,  663,  664,  665,    0,    0, 
    0,    0,    0,    0,  672,  673,  674,  675,  676,  677,  678,  679,  680, 
  681,    0,    0,    0,    0,    0,    0,  688,  689,  690,  691,  692,  693, 
  694,  695,  696,  697,    0,    0,    0,    0,    0,    0,  704,  705,  706, 
  707,  708,  709,  710,  711,  712,  713,    0,    0,    0,    0,    0,    0, 
  720,  721,  722,  723,  724,  725,  726,  727,  728,  729,    0,    0,    0, 
    0,    0,    0,  736,  737,  738,  739,  740,  741,  742,  743,  744,  745, 
    0,    0,    0,    0,    0,    0,  752,  753,  754,  755,  756,  757,  758, 
  759,  760,  761,    0,    0,    0,    0,    0,    0,  650,  651,  682,  683, 
  714,  715,  746,  747,  718,  719,    0,    0,    0,    0,    0,    0,  666, 
  667,  698,  699,  730,  731,  762,  763,  734,  735,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,  768,  769,  770,  771,  772,  773, 
  774,  775,  776,  777,    0,    0,    0,    0,    0,    0,  784,  785,  786, 
  787,  788,  789,  790,  791,  792,  793,    0,    0,    0,    0,    0,    0, 
  800,  801,  802,  803,  804,  805,  806,  807,  808,  809,    0,    0,    0, 
    0,    0,    0,  816,  817,  818,  819,  820,  821,  822,  823,  824,  825, 
    0,    0,    0,    0,    0,    0,  832,  833,  834,  835,  836,  837,  838, 
  839,  840,  841,    0,    0,    0,    0,    0,    0,  848,  849,  850,  851, 
  852,  853,  854,  855,  856,  857,    0,    0,    0,    0,    0,    0,  864, 
  865,  866,  867,  868,  869,  870,  871,  872,  873,    0,    0,    0,    0, 
    0,    0,  880,  881,  882,  883,  884,  885,  886,  887,  888,  889,    0, 
    0,    0,    0,    0,    0,  778,  779,  810,  811,  842,  843,  874,  875, 
  846,  847,    0,    0,    0,    0,    0,    0,  794,  795,  826,  827,  858, 
  859,  890,  891,  862,  863,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,  896,  897,  898,  899,  900,  901,  902,  903,  904,  905, 
    0,    0,    0,    0,    0,    0,  912,  913,  914,  915,  916,  917,  918, 
  919,  920,  921,    0,    0,    0,    0,    0,    0,  928,  929,  930,  931, 
  932,  933,  934,  935,  936,  937,    0,    0,    0,    0,    0,    0,  944, 
  945,  946,  947,  948,  949,  950,  951,  952,  953,    0,    0,    0,    0, 
    0,    0,  960,  961,  962,  963,  964,  965,  966,  967,  968,  969,    0, 
    0,    0,    0,    0,    0,  976,  977,  978,  979,  980,  981,  982,  983, 
  984,  985,    0,    0,    0,    0,    0,    0,  992,  993,  994,  995,  996, 
  997,  998,  999, 1000, 1001,    0,    0,    0,    0,    0,    0, 1008, 1009, 
 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017,    0,    0,    0,    0,    0, 
    0,  906,  907,  938,  939,  970,  971, 1002, 1003,  974,  975,    0,    0, 
    0,    0,    0,    0,  922,  923,  954,  955,  986,  987, 1018, 1019,  990, 
  991,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,   12, 
   13,  268,  269,  524,  525,  780,  781,   46,   47,    0,    0,    0,    0, 
    0,    0,   28,   29,  284,  285,  540,  541,  796,  797,   62,   63,    0, 
    0,    0,    0,    0,    0,   44,   45,  300,  301,  556,  557,  812,  813, 
  302,  303,    0,    0,    0,    0,    0,    0,   60,   61,  316,  317,  572, 
  573,  828,  829,  318,  319,    0,    0,    0,    0,    0,    0,   76,   77, 
  332,  333,  588,  589,  844,  845,  558,  559,    0,    0,    0,    0,    0, 
    0,   92,   93,  348,  349,  604,  605,  860,  861,  574,  575,    0,    0, 
    0,    0,    0,    0,  108,  109,  364,  365,  620,  621,  876,  877,  814, 
  815,    0,    0,    0,    0,    0,    0,  124,  125,  380,  381,  636,  637, 
  892,  893,  830,  831,    0,    0,    0,    0,    0,    0,   14,   15,  270, 
  271,  526,  527,  782,  783,  110,  111,    0,    0,    0,    0,    0,    0, 
   30,   31,  286,  287,  542,  543,  798,  799,  126,  127,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0, 
    0,    0,    0,    0,    0,    0,    0,    0,  140,  141,  396,  397,  652, 
  653,  908,  909,  174,  175,    0,    0,    0,    0,    0,    0,  156,  157, 
  412,  413,  668,  669,  924,  925,  190,  191,    0,    0,    0,    0,    0, 
    0,  172,  173,  428,  429,  684,  685,  940,  941,  430,  431,    0,    0, 
    0,    0,    0,    0,  188,  189,  444,  445,  700,  701,  956,  957,  446, 
  447,    0,    0,    0,    0,    0,    0,  204,  205,  460,  461,  716,  717, 
  972,  973,  686,  687,    0,    0,    0,    0,    0,    0,  220,  221,  476, 
  477,  732,  733,  988,  989,  702,  703,    0,    0,    0,    0,    0,    0, 
  236,  237,  492,  493,  748,  749, 1004, 1005,  942,  943,    0,    0,    0, 
    0,    0,    0,  252,  253,  508,  509,  764,  765, 1020, 1021,  958,  959, 
    0,    0,    0,    0,    0,    0,  142,  143,  398,  399,  654,  655,  910, 
  911,  238,  239,    0,    0,    0,    0,    0,    0,  158,  159,  414,  415, 
  670,  671,  926,  927,  254,  255};
#endif
 
#if defined(DEC_DPD2BCD) && DEC_DPD2BCD==1 && !defined(DECDPD2BCD)
#define DECDPD2BCD
 
const uint16_t DPD2BCD[1024]={    0,    1,    2,    3,    4,    5,    6,    7, 
    8,    9,  128,  129, 2048, 2049, 2176, 2177,   16,   17,   18,   19,   20, 
   21,   22,   23,   24,   25,  144,  145, 2064, 2065, 2192, 2193,   32,   33, 
   34,   35,   36,   37,   38,   39,   40,   41,  130,  131, 2080, 2081, 2056, 
 2057,   48,   49,   50,   51,   52,   53,   54,   55,   56,   57,  146,  147, 
 2096, 2097, 2072, 2073,   64,   65,   66,   67,   68,   69,   70,   71,   72, 
   73,  132,  133, 2112, 2113,  136,  137,   80,   81,   82,   83,   84,   85, 
   86,   87,   88,   89,  148,  149, 2128, 2129,  152,  153,   96,   97,   98, 
   99,  100,  101,  102,  103,  104,  105,  134,  135, 2144, 2145, 2184, 2185, 
  112,  113,  114,  115,  116,  117,  118,  119,  120,  121,  150,  151, 2160, 
 2161, 2200, 2201,  256,  257,  258,  259,  260,  261,  262,  263,  264,  265, 
  384,  385, 2304, 2305, 2432, 2433,  272,  273,  274,  275,  276,  277,  278, 
  279,  280,  281,  400,  401, 2320, 2321, 2448, 2449,  288,  289,  290,  291, 
  292,  293,  294,  295,  296,  297,  386,  387, 2336, 2337, 2312, 2313,  304, 
  305,  306,  307,  308,  309,  310,  311,  312,  313,  402,  403, 2352, 2353, 
 2328, 2329,  320,  321,  322,  323,  324,  325,  326,  327,  328,  329,  388, 
  389, 2368, 2369,  392,  393,  336,  337,  338,  339,  340,  341,  342,  343, 
  344,  345,  404,  405, 2384, 2385,  408,  409,  352,  353,  354,  355,  356, 
  357,  358,  359,  360,  361,  390,  391, 2400, 2401, 2440, 2441,  368,  369, 
  370,  371,  372,  373,  374,  375,  376,  377,  406,  407, 2416, 2417, 2456, 
 2457,  512,  513,  514,  515,  516,  517,  518,  519,  520,  521,  640,  641, 
 2050, 2051, 2178, 2179,  528,  529,  530,  531,  532,  533,  534,  535,  536, 
  537,  656,  657, 2066, 2067, 2194, 2195,  544,  545,  546,  547,  548,  549, 
  550,  551,  552,  553,  642,  643, 2082, 2083, 2088, 2089,  560,  561,  562, 
  563,  564,  565,  566,  567,  568,  569,  658,  659, 2098, 2099, 2104, 2105, 
  576,  577,  578,  579,  580,  581,  582,  583,  584,  585,  644,  645, 2114, 
 2115,  648,  649,  592,  593,  594,  595,  596,  597,  598,  599,  600,  601, 
  660,  661, 2130, 2131,  664,  665,  608,  609,  610,  611,  612,  613,  614, 
  615,  616,  617,  646,  647, 2146, 2147, 2184, 2185,  624,  625,  626,  627, 
  628,  629,  630,  631,  632,  633,  662,  663, 2162, 2163, 2200, 2201,  768, 
  769,  770,  771,  772,  773,  774,  775,  776,  777,  896,  897, 2306, 2307, 
 2434, 2435,  784,  785,  786,  787,  788,  789,  790,  791,  792,  793,  912, 
  913, 2322, 2323, 2450, 2451,  800,  801,  802,  803,  804,  805,  806,  807, 
  808,  809,  898,  899, 2338, 2339, 2344, 2345,  816,  817,  818,  819,  820, 
  821,  822,  823,  824,  825,  914,  915, 2354, 2355, 2360, 2361,  832,  833, 
  834,  835,  836,  837,  838,  839,  840,  841,  900,  901, 2370, 2371,  904, 
  905,  848,  849,  850,  851,  852,  853,  854,  855,  856,  857,  916,  917, 
 2386, 2387,  920,  921,  864,  865,  866,  867,  868,  869,  870,  871,  872, 
  873,  902,  903, 2402, 2403, 2440, 2441,  880,  881,  882,  883,  884,  885, 
  886,  887,  888,  889,  918,  919, 2418, 2419, 2456, 2457, 1024, 1025, 1026, 
 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1152, 1153, 2052, 2053, 2180, 2181, 
 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1168, 1169, 2068, 
 2069, 2196, 2197, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 
 1154, 1155, 2084, 2085, 2120, 2121, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 
 1079, 1080, 1081, 1170, 1171, 2100, 2101, 2136, 2137, 1088, 1089, 1090, 1091, 
 1092, 1093, 1094, 1095, 1096, 1097, 1156, 1157, 2116, 2117, 1160, 1161, 1104, 
 1105, 1106, 1107, 1108, 1109, 1110, 1111, 1112, 1113, 1172, 1173, 2132, 2133, 
 1176, 1177, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1158, 
 1159, 2148, 2149, 2184, 2185, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 
 1144, 1145, 1174, 1175, 2164, 2165, 2200, 2201, 1280, 1281, 1282, 1283, 1284, 
 1285, 1286, 1287, 1288, 1289, 1408, 1409, 2308, 2309, 2436, 2437, 1296, 1297, 
 1298, 1299, 1300, 1301, 1302, 1303, 1304, 1305, 1424, 1425, 2324, 2325, 2452, 
 2453, 1312, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1320, 1321, 1410, 1411, 
 2340, 2341, 2376, 2377, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 
 1337, 1426, 1427, 2356, 2357, 2392, 2393, 1344, 1345, 1346, 1347, 1348, 1349, 
 1350, 1351, 1352, 1353, 1412, 1413, 2372, 2373, 1416, 1417, 1360, 1361, 1362, 
 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1428, 1429, 2388, 2389, 1432, 1433, 
 1376, 1377, 1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1414, 1415, 2404, 
 2405, 2440, 2441, 1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, 1400, 1401, 
 1430, 1431, 2420, 2421, 2456, 2457, 1536, 1537, 1538, 1539, 1540, 1541, 1542, 
 1543, 1544, 1545, 1664, 1665, 2054, 2055, 2182, 2183, 1552, 1553, 1554, 1555, 
 1556, 1557, 1558, 1559, 1560, 1561, 1680, 1681, 2070, 2071, 2198, 2199, 1568, 
 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1666, 1667, 2086, 2087, 
 2152, 2153, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1682, 
 1683, 2102, 2103, 2168, 2169, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 
 1608, 1609, 1668, 1669, 2118, 2119, 1672, 1673, 1616, 1617, 1618, 1619, 1620, 
 1621, 1622, 1623, 1624, 1625, 1684, 1685, 2134, 2135, 1688, 1689, 1632, 1633, 
 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1670, 1671, 2150, 2151, 2184, 
 2185, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1686, 1687, 
 2166, 2167, 2200, 2201, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 1800, 
 1801, 1920, 1921, 2310, 2311, 2438, 2439, 1808, 1809, 1810, 1811, 1812, 1813, 
 1814, 1815, 1816, 1817, 1936, 1937, 2326, 2327, 2454, 2455, 1824, 1825, 1826, 
 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1922, 1923, 2342, 2343, 2408, 2409, 
 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1938, 1939, 2358, 
 2359, 2424, 2425, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 
 1924, 1925, 2374, 2375, 1928, 1929, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 
 1879, 1880, 1881, 1940, 1941, 2390, 2391, 1944, 1945, 1888, 1889, 1890, 1891, 
 1892, 1893, 1894, 1895, 1896, 1897, 1926, 1927, 2406, 2407, 2440, 2441, 1904, 
 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1942, 1943, 2422, 2423, 
 2456, 2457};
#endif
 
#if defined(DEC_BIN2DPD) && DEC_BIN2DPD==1 && !defined(DECBIN2DPD)
#define DECBIN2DPD
 
const uint16_t BIN2DPD[1000]={    0,    1,    2,    3,    4,    5,    6,    7, 
    8,    9,   16,   17,   18,   19,   20,   21,   22,   23,   24,   25,   32, 
   33,   34,   35,   36,   37,   38,   39,   40,   41,   48,   49,   50,   51, 
   52,   53,   54,   55,   56,   57,   64,   65,   66,   67,   68,   69,   70, 
   71,   72,   73,   80,   81,   82,   83,   84,   85,   86,   87,   88,   89, 
   96,   97,   98,   99,  100,  101,  102,  103,  104,  105,  112,  113,  114, 
  115,  116,  117,  118,  119,  120,  121,   10,   11,   42,   43,   74,   75, 
  106,  107,   78,   79,   26,   27,   58,   59,   90,   91,  122,  123,   94, 
   95,  128,  129,  130,  131,  132,  133,  134,  135,  136,  137,  144,  145, 
  146,  147,  148,  149,  150,  151,  152,  153,  160,  161,  162,  163,  164, 
  165,  166,  167,  168,  169,  176,  177,  178,  179,  180,  181,  182,  183, 
  184,  185,  192,  193,  194,  195,  196,  197,  198,  199,  200,  201,  208, 
  209,  210,  211,  212,  213,  214,  215,  216,  217,  224,  225,  226,  227, 
  228,  229,  230,  231,  232,  233,  240,  241,  242,  243,  244,  245,  246, 
  247,  248,  249,  138,  139,  170,  171,  202,  203,  234,  235,  206,  207, 
  154,  155,  186,  187,  218,  219,  250,  251,  222,  223,  256,  257,  258, 
  259,  260,  261,  262,  263,  264,  265,  272,  273,  274,  275,  276,  277, 
  278,  279,  280,  281,  288,  289,  290,  291,  292,  293,  294,  295,  296, 
  297,  304,  305,  306,  307,  308,  309,  310,  311,  312,  313,  320,  321, 
  322,  323,  324,  325,  326,  327,  328,  329,  336,  337,  338,  339,  340, 
  341,  342,  343,  344,  345,  352,  353,  354,  355,  356,  357,  358,  359, 
  360,  361,  368,  369,  370,  371,  372,  373,  374,  375,  376,  377,  266, 
  267,  298,  299,  330,  331,  362,  363,  334,  335,  282,  283,  314,  315, 
  346,  347,  378,  379,  350,  351,  384,  385,  386,  387,  388,  389,  390, 
  391,  392,  393,  400,  401,  402,  403,  404,  405,  406,  407,  408,  409, 
  416,  417,  418,  419,  420,  421,  422,  423,  424,  425,  432,  433,  434, 
  435,  436,  437,  438,  439,  440,  441,  448,  449,  450,  451,  452,  453, 
  454,  455,  456,  457,  464,  465,  466,  467,  468,  469,  470,  471,  472, 
  473,  480,  481,  482,  483,  484,  485,  486,  487,  488,  489,  496,  497, 
  498,  499,  500,  501,  502,  503,  504,  505,  394,  395,  426,  427,  458, 
  459,  490,  491,  462,  463,  410,  411,  442,  443,  474,  475,  506,  507, 
  478,  479,  512,  513,  514,  515,  516,  517,  518,  519,  520,  521,  528, 
  529,  530,  531,  532,  533,  534,  535,  536,  537,  544,  545,  546,  547, 
  548,  549,  550,  551,  552,  553,  560,  561,  562,  563,  564,  565,  566, 
  567,  568,  569,  576,  577,  578,  579,  580,  581,  582,  583,  584,  585, 
  592,  593,  594,  595,  596,  597,  598,  599,  600,  601,  608,  609,  610, 
  611,  612,  613,  614,  615,  616,  617,  624,  625,  626,  627,  628,  629, 
  630,  631,  632,  633,  522,  523,  554,  555,  586,  587,  618,  619,  590, 
  591,  538,  539,  570,  571,  602,  603,  634,  635,  606,  607,  640,  641, 
  642,  643,  644,  645,  646,  647,  648,  649,  656,  657,  658,  659,  660, 
  661,  662,  663,  664,  665,  672,  673,  674,  675,  676,  677,  678,  679, 
  680,  681,  688,  689,  690,  691,  692,  693,  694,  695,  696,  697,  704, 
  705,  706,  707,  708,  709,  710,  711,  712,  713,  720,  721,  722,  723, 
  724,  725,  726,  727,  728,  729,  736,  737,  738,  739,  740,  741,  742, 
  743,  744,  745,  752,  753,  754,  755,  756,  757,  758,  759,  760,  761, 
  650,  651,  682,  683,  714,  715,  746,  747,  718,  719,  666,  667,  698, 
  699,  730,  731,  762,  763,  734,  735,  768,  769,  770,  771,  772,  773, 
  774,  775,  776,  777,  784,  785,  786,  787,  788,  789,  790,  791,  792, 
  793,  800,  801,  802,  803,  804,  805,  806,  807,  808,  809,  816,  817, 
  818,  819,  820,  821,  822,  823,  824,  825,  832,  833,  834,  835,  836, 
  837,  838,  839,  840,  841,  848,  849,  850,  851,  852,  853,  854,  855, 
  856,  857,  864,  865,  866,  867,  868,  869,  870,  871,  872,  873,  880, 
  881,  882,  883,  884,  885,  886,  887,  888,  889,  778,  779,  810,  811, 
  842,  843,  874,  875,  846,  847,  794,  795,  826,  827,  858,  859,  890, 
  891,  862,  863,  896,  897,  898,  899,  900,  901,  902,  903,  904,  905, 
  912,  913,  914,  915,  916,  917,  918,  919,  920,  921,  928,  929,  930, 
  931,  932,  933,  934,  935,  936,  937,  944,  945,  946,  947,  948,  949, 
  950,  951,  952,  953,  960,  961,  962,  963,  964,  965,  966,  967,  968, 
  969,  976,  977,  978,  979,  980,  981,  982,  983,  984,  985,  992,  993, 
  994,  995,  996,  997,  998,  999, 1000, 1001, 1008, 1009, 1010, 1011, 1012, 
 1013, 1014, 1015, 1016, 1017,  906,  907,  938,  939,  970,  971, 1002, 1003, 
  974,  975,  922,  923,  954,  955,  986,  987, 1018, 1019,  990,  991,   12, 
   13,  268,  269,  524,  525,  780,  781,   46,   47,   28,   29,  284,  285, 
  540,  541,  796,  797,   62,   63,   44,   45,  300,  301,  556,  557,  812, 
  813,  302,  303,   60,   61,  316,  317,  572,  573,  828,  829,  318,  319, 
   76,   77,  332,  333,  588,  589,  844,  845,  558,  559,   92,   93,  348, 
  349,  604,  605,  860,  861,  574,  575,  108,  109,  364,  365,  620,  621, 
  876,  877,  814,  815,  124,  125,  380,  381,  636,  637,  892,  893,  830, 
  831,   14,   15,  270,  271,  526,  527,  782,  783,  110,  111,   30,   31, 
  286,  287,  542,  543,  798,  799,  126,  127,  140,  141,  396,  397,  652, 
  653,  908,  909,  174,  175,  156,  157,  412,  413,  668,  669,  924,  925, 
  190,  191,  172,  173,  428,  429,  684,  685,  940,  941,  430,  431,  188, 
  189,  444,  445,  700,  701,  956,  957,  446,  447,  204,  205,  460,  461, 
  716,  717,  972,  973,  686,  687,  220,  221,  476,  477,  732,  733,  988, 
  989,  702,  703,  236,  237,  492,  493,  748,  749, 1004, 1005,  942,  943, 
  252,  253,  508,  509,  764,  765, 1020, 1021,  958,  959,  142,  143,  398, 
  399,  654,  655,  910,  911,  238,  239,  158,  159,  414,  415,  670,  671, 
  926,  927,  254,  255};
#endif 
 
#if defined(DEC_DPD2BIN) && DEC_DPD2BIN==1 && !defined(DECDPD2BIN)
#define DECDPD2BIN
 
const uint16_t DPD2BIN[1024]={    0,    1,    2,    3,    4,    5,    6,    7, 
    8,    9,   80,   81,  800,  801,  880,  881,   10,   11,   12,   13,   14, 
   15,   16,   17,   18,   19,   90,   91,  810,  811,  890,  891,   20,   21, 
   22,   23,   24,   25,   26,   27,   28,   29,   82,   83,  820,  821,  808, 
  809,   30,   31,   32,   33,   34,   35,   36,   37,   38,   39,   92,   93, 
  830,  831,  818,  819,   40,   41,   42,   43,   44,   45,   46,   47,   48, 
   49,   84,   85,  840,  841,   88,   89,   50,   51,   52,   53,   54,   55, 
   56,   57,   58,   59,   94,   95,  850,  851,   98,   99,   60,   61,   62, 
   63,   64,   65,   66,   67,   68,   69,   86,   87,  860,  861,  888,  889, 
   70,   71,   72,   73,   74,   75,   76,   77,   78,   79,   96,   97,  870, 
  871,  898,  899,  100,  101,  102,  103,  104,  105,  106,  107,  108,  109, 
  180,  181,  900,  901,  980,  981,  110,  111,  112,  113,  114,  115,  116, 
  117,  118,  119,  190,  191,  910,  911,  990,  991,  120,  121,  122,  123, 
  124,  125,  126,  127,  128,  129,  182,  183,  920,  921,  908,  909,  130, 
  131,  132,  133,  134,  135,  136,  137,  138,  139,  192,  193,  930,  931, 
  918,  919,  140,  141,  142,  143,  144,  145,  146,  147,  148,  149,  184, 
  185,  940,  941,  188,  189,  150,  151,  152,  153,  154,  155,  156,  157, 
  158,  159,  194,  195,  950,  951,  198,  199,  160,  161,  162,  163,  164, 
  165,  166,  167,  168,  169,  186,  187,  960,  961,  988,  989,  170,  171, 
  172,  173,  174,  175,  176,  177,  178,  179,  196,  197,  970,  971,  998, 
  999,  200,  201,  202,  203,  204,  205,  206,  207,  208,  209,  280,  281, 
  802,  803,  882,  883,  210,  211,  212,  213,  214,  215,  216,  217,  218, 
  219,  290,  291,  812,  813,  892,  893,  220,  221,  222,  223,  224,  225, 
  226,  227,  228,  229,  282,  283,  822,  823,  828,  829,  230,  231,  232, 
  233,  234,  235,  236,  237,  238,  239,  292,  293,  832,  833,  838,  839, 
  240,  241,  242,  243,  244,  245,  246,  247,  248,  249,  284,  285,  842, 
  843,  288,  289,  250,  251,  252,  253,  254,  255,  256,  257,  258,  259, 
  294,  295,  852,  853,  298,  299,  260,  261,  262,  263,  264,  265,  266, 
  267,  268,  269,  286,  287,  862,  863,  888,  889,  270,  271,  272,  273, 
  274,  275,  276,  277,  278,  279,  296,  297,  872,  873,  898,  899,  300, 
  301,  302,  303,  304,  305,  306,  307,  308,  309,  380,  381,  902,  903, 
  982,  983,  310,  311,  312,  313,  314,  315,  316,  317,  318,  319,  390, 
  391,  912,  913,  992,  993,  320,  321,  322,  323,  324,  325,  326,  327, 
  328,  329,  382,  383,  922,  923,  928,  929,  330,  331,  332,  333,  334, 
  335,  336,  337,  338,  339,  392,  393,  932,  933,  938,  939,  340,  341, 
  342,  343,  344,  345,  346,  347,  348,  349,  384,  385,  942,  943,  388, 
  389,  350,  351,  352,  353,  354,  355,  356,  357,  358,  359,  394,  395, 
  952,  953,  398,  399,  360,  361,  362,  363,  364,  365,  366,  367,  368, 
  369,  386,  387,  962,  963,  988,  989,  370,  371,  372,  373,  374,  375, 
  376,  377,  378,  379,  396,  397,  972,  973,  998,  999,  400,  401,  402, 
  403,  404,  405,  406,  407,  408,  409,  480,  481,  804,  805,  884,  885, 
  410,  411,  412,  413,  414,  415,  416,  417,  418,  419,  490,  491,  814, 
  815,  894,  895,  420,  421,  422,  423,  424,  425,  426,  427,  428,  429, 
  482,  483,  824,  825,  848,  849,  430,  431,  432,  433,  434,  435,  436, 
  437,  438,  439,  492,  493,  834,  835,  858,  859,  440,  441,  442,  443, 
  444,  445,  446,  447,  448,  449,  484,  485,  844,  845,  488,  489,  450, 
  451,  452,  453,  454,  455,  456,  457,  458,  459,  494,  495,  854,  855, 
  498,  499,  460,  461,  462,  463,  464,  465,  466,  467,  468,  469,  486, 
  487,  864,  865,  888,  889,  470,  471,  472,  473,  474,  475,  476,  477, 
  478,  479,  496,  497,  874,  875,  898,  899,  500,  501,  502,  503,  504, 
  505,  506,  507,  508,  509,  580,  581,  904,  905,  984,  985,  510,  511, 
  512,  513,  514,  515,  516,  517,  518,  519,  590,  591,  914,  915,  994, 
  995,  520,  521,  522,  523,  524,  525,  526,  527,  528,  529,  582,  583, 
  924,  925,  948,  949,  530,  531,  532,  533,  534,  535,  536,  537,  538, 
  539,  592,  593,  934,  935,  958,  959,  540,  541,  542,  543,  544,  545, 
  546,  547,  548,  549,  584,  585,  944,  945,  588,  589,  550,  551,  552, 
  553,  554,  555,  556,  557,  558,  559,  594,  595,  954,  955,  598,  599, 
  560,  561,  562,  563,  564,  565,  566,  567,  568,  569,  586,  587,  964, 
  965,  988,  989,  570,  571,  572,  573,  574,  575,  576,  577,  578,  579, 
  596,  597,  974,  975,  998,  999,  600,  601,  602,  603,  604,  605,  606, 
  607,  608,  609,  680,  681,  806,  807,  886,  887,  610,  611,  612,  613, 
  614,  615,  616,  617,  618,  619,  690,  691,  816,  817,  896,  897,  620, 
  621,  622,  623,  624,  625,  626,  627,  628,  629,  682,  683,  826,  827, 
  868,  869,  630,  631,  632,  633,  634,  635,  636,  637,  638,  639,  692, 
  693,  836,  837,  878,  879,  640,  641,  642,  643,  644,  645,  646,  647, 
  648,  649,  684,  685,  846,  847,  688,  689,  650,  651,  652,  653,  654, 
  655,  656,  657,  658,  659,  694,  695,  856,  857,  698,  699,  660,  661, 
  662,  663,  664,  665,  666,  667,  668,  669,  686,  687,  866,  867,  888, 
  889,  670,  671,  672,  673,  674,  675,  676,  677,  678,  679,  696,  697, 
  876,  877,  898,  899,  700,  701,  702,  703,  704,  705,  706,  707,  708, 
  709,  780,  781,  906,  907,  986,  987,  710,  711,  712,  713,  714,  715, 
  716,  717,  718,  719,  790,  791,  916,  917,  996,  997,  720,  721,  722, 
  723,  724,  725,  726,  727,  728,  729,  782,  783,  926,  927,  968,  969, 
  730,  731,  732,  733,  734,  735,  736,  737,  738,  739,  792,  793,  936, 
  937,  978,  979,  740,  741,  742,  743,  744,  745,  746,  747,  748,  749, 
  784,  785,  946,  947,  788,  789,  750,  751,  752,  753,  754,  755,  756, 
  757,  758,  759,  794,  795,  956,  957,  798,  799,  760,  761,  762,  763, 
  764,  765,  766,  767,  768,  769,  786,  787,  966,  967,  988,  989,  770, 
  771,  772,  773,  774,  775,  776,  777,  778,  779,  796,  797,  976,  977, 
  998,  999};
#endif
 
#if defined(DEC_DPD2BINK) && DEC_DPD2BINK==1 && !defined(DECDPD2BINK)
#define DECDPD2BINK
 
const uint32_t DPD2BINK[1024]={       0,   1000,   2000,   3000,   4000,   5000, 
   6000,   7000,   8000,   9000,  80000,  81000, 800000, 801000, 880000, 881000, 
  10000,  11000,  12000,  13000,  14000,  15000,  16000,  17000,  18000,  19000, 
  90000,  91000, 810000, 811000, 890000, 891000,  20000,  21000,  22000,  23000, 
  24000,  25000,  26000,  27000,  28000,  29000,  82000,  83000, 820000, 821000, 
 808000, 809000,  30000,  31000,  32000,  33000,  34000,  35000,  36000,  37000, 
  38000,  39000,  92000,  93000, 830000, 831000, 818000, 819000,  40000,  41000, 
  42000,  43000,  44000,  45000,  46000,  47000,  48000,  49000,  84000,  85000, 
 840000, 841000,  88000,  89000,  50000,  51000,  52000,  53000,  54000,  55000, 
  56000,  57000,  58000,  59000,  94000,  95000, 850000, 851000,  98000,  99000, 
  60000,  61000,  62000,  63000,  64000,  65000,  66000,  67000,  68000,  69000, 
  86000,  87000, 860000, 861000, 888000, 889000,  70000,  71000,  72000,  73000, 
  74000,  75000,  76000,  77000,  78000,  79000,  96000,  97000, 870000, 871000, 
 898000, 899000, 100000, 101000, 102000, 103000, 104000, 105000, 106000, 107000, 
 108000, 109000, 180000, 181000, 900000, 901000, 980000, 981000, 110000, 111000, 
 112000, 113000, 114000, 115000, 116000, 117000, 118000, 119000, 190000, 191000, 
 910000, 911000, 990000, 991000, 120000, 121000, 122000, 123000, 124000, 125000, 
 126000, 127000, 128000, 129000, 182000, 183000, 920000, 921000, 908000, 909000, 
 130000, 131000, 132000, 133000, 134000, 135000, 136000, 137000, 138000, 139000, 
 192000, 193000, 930000, 931000, 918000, 919000, 140000, 141000, 142000, 143000, 
 144000, 145000, 146000, 147000, 148000, 149000, 184000, 185000, 940000, 941000, 
 188000, 189000, 150000, 151000, 152000, 153000, 154000, 155000, 156000, 157000, 
 158000, 159000, 194000, 195000, 950000, 951000, 198000, 199000, 160000, 161000, 
 162000, 163000, 164000, 165000, 166000, 167000, 168000, 169000, 186000, 187000, 
 960000, 961000, 988000, 989000, 170000, 171000, 172000, 173000, 174000, 175000, 
 176000, 177000, 178000, 179000, 196000, 197000, 970000, 971000, 998000, 999000, 
 200000, 201000, 202000, 203000, 204000, 205000, 206000, 207000, 208000, 209000, 
 280000, 281000, 802000, 803000, 882000, 883000, 210000, 211000, 212000, 213000, 
 214000, 215000, 216000, 217000, 218000, 219000, 290000, 291000, 812000, 813000, 
 892000, 893000, 220000, 221000, 222000, 223000, 224000, 225000, 226000, 227000, 
 228000, 229000, 282000, 283000, 822000, 823000, 828000, 829000, 230000, 231000, 
 232000, 233000, 234000, 235000, 236000, 237000, 238000, 239000, 292000, 293000, 
 832000, 833000, 838000, 839000, 240000, 241000, 242000, 243000, 244000, 245000, 
 246000, 247000, 248000, 249000, 284000, 285000, 842000, 843000, 288000, 289000, 
 250000, 251000, 252000, 253000, 254000, 255000, 256000, 257000, 258000, 259000, 
 294000, 295000, 852000, 853000, 298000, 299000, 260000, 261000, 262000, 263000, 
 264000, 265000, 266000, 267000, 268000, 269000, 286000, 287000, 862000, 863000, 
 888000, 889000, 270000, 271000, 272000, 273000, 274000, 275000, 276000, 277000, 
 278000, 279000, 296000, 297000, 872000, 873000, 898000, 899000, 300000, 301000, 
 302000, 303000, 304000, 305000, 306000, 307000, 308000, 309000, 380000, 381000, 
 902000, 903000, 982000, 983000, 310000, 311000, 312000, 313000, 314000, 315000, 
 316000, 317000, 318000, 319000, 390000, 391000, 912000, 913000, 992000, 993000, 
 320000, 321000, 322000, 323000, 324000, 325000, 326000, 327000, 328000, 329000, 
 382000, 383000, 922000, 923000, 928000, 929000, 330000, 331000, 332000, 333000, 
 334000, 335000, 336000, 337000, 338000, 339000, 392000, 393000, 932000, 933000, 
 938000, 939000, 340000, 341000, 342000, 343000, 344000, 345000, 346000, 347000, 
 348000, 349000, 384000, 385000, 942000, 943000, 388000, 389000, 350000, 351000, 
 352000, 353000, 354000, 355000, 356000, 357000, 358000, 359000, 394000, 395000, 
 952000, 953000, 398000, 399000, 360000, 361000, 362000, 363000, 364000, 365000, 
 366000, 367000, 368000, 369000, 386000, 387000, 962000, 963000, 988000, 989000, 
 370000, 371000, 372000, 373000, 374000, 375000, 376000, 377000, 378000, 379000, 
 396000, 397000, 972000, 973000, 998000, 999000, 400000, 401000, 402000, 403000, 
 404000, 405000, 406000, 407000, 408000, 409000, 480000, 481000, 804000, 805000, 
 884000, 885000, 410000, 411000, 412000, 413000, 414000, 415000, 416000, 417000, 
 418000, 419000, 490000, 491000, 814000, 815000, 894000, 895000, 420000, 421000, 
 422000, 423000, 424000, 425000, 426000, 427000, 428000, 429000, 482000, 483000, 
 824000, 825000, 848000, 849000, 430000, 431000, 432000, 433000, 434000, 435000, 
 436000, 437000, 438000, 439000, 492000, 493000, 834000, 835000, 858000, 859000, 
 440000, 441000, 442000, 443000, 444000, 445000, 446000, 447000, 448000, 449000, 
 484000, 485000, 844000, 845000, 488000, 489000, 450000, 451000, 452000, 453000, 
 454000, 455000, 456000, 457000, 458000, 459000, 494000, 495000, 854000, 855000, 
 498000, 499000, 460000, 461000, 462000, 463000, 464000, 465000, 466000, 467000, 
 468000, 469000, 486000, 487000, 864000, 865000, 888000, 889000, 470000, 471000, 
 472000, 473000, 474000, 475000, 476000, 477000, 478000, 479000, 496000, 497000, 
 874000, 875000, 898000, 899000, 500000, 501000, 502000, 503000, 504000, 505000, 
 506000, 507000, 508000, 509000, 580000, 581000, 904000, 905000, 984000, 985000, 
 510000, 511000, 512000, 513000, 514000, 515000, 516000, 517000, 518000, 519000, 
 590000, 591000, 914000, 915000, 994000, 995000, 520000, 521000, 522000, 523000, 
 524000, 525000, 526000, 527000, 528000, 529000, 582000, 583000, 924000, 925000, 
 948000, 949000, 530000, 531000, 532000, 533000, 534000, 535000, 536000, 537000, 
 538000, 539000, 592000, 593000, 934000, 935000, 958000, 959000, 540000, 541000, 
 542000, 543000, 544000, 545000, 546000, 547000, 548000, 549000, 584000, 585000, 
 944000, 945000, 588000, 589000, 550000, 551000, 552000, 553000, 554000, 555000, 
 556000, 557000, 558000, 559000, 594000, 595000, 954000, 955000, 598000, 599000, 
 560000, 561000, 562000, 563000, 564000, 565000, 566000, 567000, 568000, 569000, 
 586000, 587000, 964000, 965000, 988000, 989000, 570000, 571000, 572000, 573000, 
 574000, 575000, 576000, 577000, 578000, 579000, 596000, 597000, 974000, 975000, 
 998000, 999000, 600000, 601000, 602000, 603000, 604000, 605000, 606000, 607000, 
 608000, 609000, 680000, 681000, 806000, 807000, 886000, 887000, 610000, 611000, 
 612000, 613000, 614000, 615000, 616000, 617000, 618000, 619000, 690000, 691000, 
 816000, 817000, 896000, 897000, 620000, 621000, 622000, 623000, 624000, 625000, 
 626000, 627000, 628000, 629000, 682000, 683000, 826000, 827000, 868000, 869000, 
 630000, 631000, 632000, 633000, 634000, 635000, 636000, 637000, 638000, 639000, 
 692000, 693000, 836000, 837000, 878000, 879000, 640000, 641000, 642000, 643000, 
 644000, 645000, 646000, 647000, 648000, 649000, 684000, 685000, 846000, 847000, 
 688000, 689000, 650000, 651000, 652000, 653000, 654000, 655000, 656000, 657000, 
 658000, 659000, 694000, 695000, 856000, 857000, 698000, 699000, 660000, 661000, 
 662000, 663000, 664000, 665000, 666000, 667000, 668000, 669000, 686000, 687000, 
 866000, 867000, 888000, 889000, 670000, 671000, 672000, 673000, 674000, 675000, 
 676000, 677000, 678000, 679000, 696000, 697000, 876000, 877000, 898000, 899000, 
 700000, 701000, 702000, 703000, 704000, 705000, 706000, 707000, 708000, 709000, 
 780000, 781000, 906000, 907000, 986000, 987000, 710000, 711000, 712000, 713000, 
 714000, 715000, 716000, 717000, 718000, 719000, 790000, 791000, 916000, 917000, 
 996000, 997000, 720000, 721000, 722000, 723000, 724000, 725000, 726000, 727000, 
 728000, 729000, 782000, 783000, 926000, 927000, 968000, 969000, 730000, 731000, 
 732000, 733000, 734000, 735000, 736000, 737000, 738000, 739000, 792000, 793000, 
 936000, 937000, 978000, 979000, 740000, 741000, 742000, 743000, 744000, 745000, 
 746000, 747000, 748000, 749000, 784000, 785000, 946000, 947000, 788000, 789000, 
 750000, 751000, 752000, 753000, 754000, 755000, 756000, 757000, 758000, 759000, 
 794000, 795000, 956000, 957000, 798000, 799000, 760000, 761000, 762000, 763000, 
 764000, 765000, 766000, 767000, 768000, 769000, 786000, 787000, 966000, 967000, 
 988000, 989000, 770000, 771000, 772000, 773000, 774000, 775000, 776000, 777000, 
 778000, 779000, 796000, 797000, 976000, 977000, 998000, 999000};
#endif
 
#if defined(DEC_DPD2BINM) && DEC_DPD2BINM==1 && !defined(DECDPD2BINM)
#define DECDPD2BINM
 
const uint32_t DPD2BINM[1024]={0,   1000000,   2000000,   3000000,   4000000, 
   5000000,   6000000,   7000000,   8000000,   9000000,  80000000,  81000000, 
 800000000, 801000000, 880000000, 881000000,  10000000,  11000000,  12000000, 
  13000000,  14000000,  15000000,  16000000,  17000000,  18000000,  19000000, 
  90000000,  91000000, 810000000, 811000000, 890000000, 891000000,  20000000, 
  21000000,  22000000,  23000000,  24000000,  25000000,  26000000,  27000000, 
  28000000,  29000000,  82000000,  83000000, 820000000, 821000000, 808000000, 
 809000000,  30000000,  31000000,  32000000,  33000000,  34000000,  35000000, 
  36000000,  37000000,  38000000,  39000000,  92000000,  93000000, 830000000, 
 831000000, 818000000, 819000000,  40000000,  41000000,  42000000,  43000000, 
  44000000,  45000000,  46000000,  47000000,  48000000,  49000000,  84000000, 
  85000000, 840000000, 841000000,  88000000,  89000000,  50000000,  51000000, 
  52000000,  53000000,  54000000,  55000000,  56000000,  57000000,  58000000, 
  59000000,  94000000,  95000000, 850000000, 851000000,  98000000,  99000000, 
  60000000,  61000000,  62000000,  63000000,  64000000,  65000000,  66000000, 
  67000000,  68000000,  69000000,  86000000,  87000000, 860000000, 861000000, 
 888000000, 889000000,  70000000,  71000000,  72000000,  73000000,  74000000, 
  75000000,  76000000,  77000000,  78000000,  79000000,  96000000,  97000000, 
 870000000, 871000000, 898000000, 899000000, 100000000, 101000000, 102000000, 
 103000000, 104000000, 105000000, 106000000, 107000000, 108000000, 109000000, 
 180000000, 181000000, 900000000, 901000000, 980000000, 981000000, 110000000, 
 111000000, 112000000, 113000000, 114000000, 115000000, 116000000, 117000000, 
 118000000, 119000000, 190000000, 191000000, 910000000, 911000000, 990000000, 
 991000000, 120000000, 121000000, 122000000, 123000000, 124000000, 125000000, 
 126000000, 127000000, 128000000, 129000000, 182000000, 183000000, 920000000, 
 921000000, 908000000, 909000000, 130000000, 131000000, 132000000, 133000000, 
 134000000, 135000000, 136000000, 137000000, 138000000, 139000000, 192000000, 
 193000000, 930000000, 931000000, 918000000, 919000000, 140000000, 141000000, 
 142000000, 143000000, 144000000, 145000000, 146000000, 147000000, 148000000, 
 149000000, 184000000, 185000000, 940000000, 941000000, 188000000, 189000000, 
 150000000, 151000000, 152000000, 153000000, 154000000, 155000000, 156000000, 
 157000000, 158000000, 159000000, 194000000, 195000000, 950000000, 951000000, 
 198000000, 199000000, 160000000, 161000000, 162000000, 163000000, 164000000, 
 165000000, 166000000, 167000000, 168000000, 169000000, 186000000, 187000000, 
 960000000, 961000000, 988000000, 989000000, 170000000, 171000000, 172000000, 
 173000000, 174000000, 175000000, 176000000, 177000000, 178000000, 179000000, 
 196000000, 197000000, 970000000, 971000000, 998000000, 999000000, 200000000, 
 201000000, 202000000, 203000000, 204000000, 205000000, 206000000, 207000000, 
 208000000, 209000000, 280000000, 281000000, 802000000, 803000000, 882000000, 
 883000000, 210000000, 211000000, 212000000, 213000000, 214000000, 215000000, 
 216000000, 217000000, 218000000, 219000000, 290000000, 291000000, 812000000, 
 813000000, 892000000, 893000000, 220000000, 221000000, 222000000, 223000000, 
 224000000, 225000000, 226000000, 227000000, 228000000, 229000000, 282000000, 
 283000000, 822000000, 823000000, 828000000, 829000000, 230000000, 231000000, 
 232000000, 233000000, 234000000, 235000000, 236000000, 237000000, 238000000, 
 239000000, 292000000, 293000000, 832000000, 833000000, 838000000, 839000000, 
 240000000, 241000000, 242000000, 243000000, 244000000, 245000000, 246000000, 
 247000000, 248000000, 249000000, 284000000, 285000000, 842000000, 843000000, 
 288000000, 289000000, 250000000, 251000000, 252000000, 253000000, 254000000, 
 255000000, 256000000, 257000000, 258000000, 259000000, 294000000, 295000000, 
 852000000, 853000000, 298000000, 299000000, 260000000, 261000000, 262000000, 
 263000000, 264000000, 265000000, 266000000, 267000000, 268000000, 269000000, 
 286000000, 287000000, 862000000, 863000000, 888000000, 889000000, 270000000, 
 271000000, 272000000, 273000000, 274000000, 275000000, 276000000, 277000000, 
 278000000, 279000000, 296000000, 297000000, 872000000, 873000000, 898000000, 
 899000000, 300000000, 301000000, 302000000, 303000000, 304000000, 305000000, 
 306000000, 307000000, 308000000, 309000000, 380000000, 381000000, 902000000, 
 903000000, 982000000, 983000000, 310000000, 311000000, 312000000, 313000000, 
 314000000, 315000000, 316000000, 317000000, 318000000, 319000000, 390000000, 
 391000000, 912000000, 913000000, 992000000, 993000000, 320000000, 321000000, 
 322000000, 323000000, 324000000, 325000000, 326000000, 327000000, 328000000, 
 329000000, 382000000, 383000000, 922000000, 923000000, 928000000, 929000000, 
 330000000, 331000000, 332000000, 333000000, 334000000, 335000000, 336000000, 
 337000000, 338000000, 339000000, 392000000, 393000000, 932000000, 933000000, 
 938000000, 939000000, 340000000, 341000000, 342000000, 343000000, 344000000, 
 345000000, 346000000, 347000000, 348000000, 349000000, 384000000, 385000000, 
 942000000, 943000000, 388000000, 389000000, 350000000, 351000000, 352000000, 
 353000000, 354000000, 355000000, 356000000, 357000000, 358000000, 359000000, 
 394000000, 395000000, 952000000, 953000000, 398000000, 399000000, 360000000, 
 361000000, 362000000, 363000000, 364000000, 365000000, 366000000, 367000000, 
 368000000, 369000000, 386000000, 387000000, 962000000, 963000000, 988000000, 
 989000000, 370000000, 371000000, 372000000, 373000000, 374000000, 375000000, 
 376000000, 377000000, 378000000, 379000000, 396000000, 397000000, 972000000, 
 973000000, 998000000, 999000000, 400000000, 401000000, 402000000, 403000000, 
 404000000, 405000000, 406000000, 407000000, 408000000, 409000000, 480000000, 
 481000000, 804000000, 805000000, 884000000, 885000000, 410000000, 411000000, 
 412000000, 413000000, 414000000, 415000000, 416000000, 417000000, 418000000, 
 419000000, 490000000, 491000000, 814000000, 815000000, 894000000, 895000000, 
 420000000, 421000000, 422000000, 423000000, 424000000, 425000000, 426000000, 
 427000000, 428000000, 429000000, 482000000, 483000000, 824000000, 825000000, 
 848000000, 849000000, 430000000, 431000000, 432000000, 433000000, 434000000, 
 435000000, 436000000, 437000000, 438000000, 439000000, 492000000, 493000000, 
 834000000, 835000000, 858000000, 859000000, 440000000, 441000000, 442000000, 
 443000000, 444000000, 445000000, 446000000, 447000000, 448000000, 449000000, 
 484000000, 485000000, 844000000, 845000000, 488000000, 489000000, 450000000, 
 451000000, 452000000, 453000000, 454000000, 455000000, 456000000, 457000000, 
 458000000, 459000000, 494000000, 495000000, 854000000, 855000000, 498000000, 
 499000000, 460000000, 461000000, 462000000, 463000000, 464000000, 465000000, 
 466000000, 467000000, 468000000, 469000000, 486000000, 487000000, 864000000, 
 865000000, 888000000, 889000000, 470000000, 471000000, 472000000, 473000000, 
 474000000, 475000000, 476000000, 477000000, 478000000, 479000000, 496000000, 
 497000000, 874000000, 875000000, 898000000, 899000000, 500000000, 501000000, 
 502000000, 503000000, 504000000, 505000000, 506000000, 507000000, 508000000, 
 509000000, 580000000, 581000000, 904000000, 905000000, 984000000, 985000000, 
 510000000, 511000000, 512000000, 513000000, 514000000, 515000000, 516000000, 
 517000000, 518000000, 519000000, 590000000, 591000000, 914000000, 915000000, 
 994000000, 995000000, 520000000, 521000000, 522000000, 523000000, 524000000, 
 525000000, 526000000, 527000000, 528000000, 529000000, 582000000, 583000000, 
 924000000, 925000000, 948000000, 949000000, 530000000, 531000000, 532000000, 
 533000000, 534000000, 535000000, 536000000, 537000000, 538000000, 539000000, 
 592000000, 593000000, 934000000, 935000000, 958000000, 959000000, 540000000, 
 541000000, 542000000, 543000000, 544000000, 545000000, 546000000, 547000000, 
 548000000, 549000000, 584000000, 585000000, 944000000, 945000000, 588000000, 
 589000000, 550000000, 551000000, 552000000, 553000000, 554000000, 555000000, 
 556000000, 557000000, 558000000, 559000000, 594000000, 595000000, 954000000, 
 955000000, 598000000, 599000000, 560000000, 561000000, 562000000, 563000000, 
 564000000, 565000000, 566000000, 567000000, 568000000, 569000000, 586000000, 
 587000000, 964000000, 965000000, 988000000, 989000000, 570000000, 571000000, 
 572000000, 573000000, 574000000, 575000000, 576000000, 577000000, 578000000, 
 579000000, 596000000, 597000000, 974000000, 975000000, 998000000, 999000000, 
 600000000, 601000000, 602000000, 603000000, 604000000, 605000000, 606000000, 
 607000000, 608000000, 609000000, 680000000, 681000000, 806000000, 807000000, 
 886000000, 887000000, 610000000, 611000000, 612000000, 613000000, 614000000, 
 615000000, 616000000, 617000000, 618000000, 619000000, 690000000, 691000000, 
 816000000, 817000000, 896000000, 897000000, 620000000, 621000000, 622000000, 
 623000000, 624000000, 625000000, 626000000, 627000000, 628000000, 629000000, 
 682000000, 683000000, 826000000, 827000000, 868000000, 869000000, 630000000, 
 631000000, 632000000, 633000000, 634000000, 635000000, 636000000, 637000000, 
 638000000, 639000000, 692000000, 693000000, 836000000, 837000000, 878000000, 
 879000000, 640000000, 641000000, 642000000, 643000000, 644000000, 645000000, 
 646000000, 647000000, 648000000, 649000000, 684000000, 685000000, 846000000, 
 847000000, 688000000, 689000000, 650000000, 651000000, 652000000, 653000000, 
 654000000, 655000000, 656000000, 657000000, 658000000, 659000000, 694000000, 
 695000000, 856000000, 857000000, 698000000, 699000000, 660000000, 661000000, 
 662000000, 663000000, 664000000, 665000000, 666000000, 667000000, 668000000, 
 669000000, 686000000, 687000000, 866000000, 867000000, 888000000, 889000000, 
 670000000, 671000000, 672000000, 673000000, 674000000, 675000000, 676000000, 
 677000000, 678000000, 679000000, 696000000, 697000000, 876000000, 877000000, 
 898000000, 899000000, 700000000, 701000000, 702000000, 703000000, 704000000, 
 705000000, 706000000, 707000000, 708000000, 709000000, 780000000, 781000000, 
 906000000, 907000000, 986000000, 987000000, 710000000, 711000000, 712000000, 
 713000000, 714000000, 715000000, 716000000, 717000000, 718000000, 719000000, 
 790000000, 791000000, 916000000, 917000000, 996000000, 997000000, 720000000, 
 721000000, 722000000, 723000000, 724000000, 725000000, 726000000, 727000000, 
 728000000, 729000000, 782000000, 783000000, 926000000, 927000000, 968000000, 
 969000000, 730000000, 731000000, 732000000, 733000000, 734000000, 735000000, 
 736000000, 737000000, 738000000, 739000000, 792000000, 793000000, 936000000, 
 937000000, 978000000, 979000000, 740000000, 741000000, 742000000, 743000000, 
 744000000, 745000000, 746000000, 747000000, 748000000, 749000000, 784000000, 
 785000000, 946000000, 947000000, 788000000, 789000000, 750000000, 751000000, 
 752000000, 753000000, 754000000, 755000000, 756000000, 757000000, 758000000, 
 759000000, 794000000, 795000000, 956000000, 957000000, 798000000, 799000000, 
 760000000, 761000000, 762000000, 763000000, 764000000, 765000000, 766000000, 
 767000000, 768000000, 769000000, 786000000, 787000000, 966000000, 967000000, 
 988000000, 989000000, 770000000, 771000000, 772000000, 773000000, 774000000, 
 775000000, 776000000, 777000000, 778000000, 779000000, 796000000, 797000000, 
 976000000, 977000000, 998000000, 999000000};
#endif
 
#if defined(DEC_BIN2CHAR) && DEC_BIN2CHAR==1 && !defined(DECBIN2CHAR)
#define DECBIN2CHAR
 
const uint8_t BIN2CHAR[4001]={
 '\0','0','0','0', '\1','0','0','1', '\1','0','0','2', '\1','0','0','3', '\1','0','0','4', 
 '\1','0','0','5', '\1','0','0','6', '\1','0','0','7', '\1','0','0','8', '\1','0','0','9', 
 '\2','0','1','0', '\2','0','1','1', '\2','0','1','2', '\2','0','1','3', '\2','0','1','4', 
 '\2','0','1','5', '\2','0','1','6', '\2','0','1','7', '\2','0','1','8', '\2','0','1','9', 
 '\2','0','2','0', '\2','0','2','1', '\2','0','2','2', '\2','0','2','3', '\2','0','2','4', 
 '\2','0','2','5', '\2','0','2','6', '\2','0','2','7', '\2','0','2','8', '\2','0','2','9', 
 '\2','0','3','0', '\2','0','3','1', '\2','0','3','2', '\2','0','3','3', '\2','0','3','4', 
 '\2','0','3','5', '\2','0','3','6', '\2','0','3','7', '\2','0','3','8', '\2','0','3','9', 
 '\2','0','4','0', '\2','0','4','1', '\2','0','4','2', '\2','0','4','3', '\2','0','4','4', 
 '\2','0','4','5', '\2','0','4','6', '\2','0','4','7', '\2','0','4','8', '\2','0','4','9', 
 '\2','0','5','0', '\2','0','5','1', '\2','0','5','2', '\2','0','5','3', '\2','0','5','4', 
 '\2','0','5','5', '\2','0','5','6', '\2','0','5','7', '\2','0','5','8', '\2','0','5','9', 
 '\2','0','6','0', '\2','0','6','1', '\2','0','6','2', '\2','0','6','3', '\2','0','6','4', 
 '\2','0','6','5', '\2','0','6','6', '\2','0','6','7', '\2','0','6','8', '\2','0','6','9', 
 '\2','0','7','0', '\2','0','7','1', '\2','0','7','2', '\2','0','7','3', '\2','0','7','4', 
 '\2','0','7','5', '\2','0','7','6', '\2','0','7','7', '\2','0','7','8', '\2','0','7','9', 
 '\2','0','8','0', '\2','0','8','1', '\2','0','8','2', '\2','0','8','3', '\2','0','8','4', 
 '\2','0','8','5', '\2','0','8','6', '\2','0','8','7', '\2','0','8','8', '\2','0','8','9', 
 '\2','0','9','0', '\2','0','9','1', '\2','0','9','2', '\2','0','9','3', '\2','0','9','4', 
 '\2','0','9','5', '\2','0','9','6', '\2','0','9','7', '\2','0','9','8', '\2','0','9','9', 
 '\3','1','0','0', '\3','1','0','1', '\3','1','0','2', '\3','1','0','3', '\3','1','0','4', 
 '\3','1','0','5', '\3','1','0','6', '\3','1','0','7', '\3','1','0','8', '\3','1','0','9', 
 '\3','1','1','0', '\3','1','1','1', '\3','1','1','2', '\3','1','1','3', '\3','1','1','4', 
 '\3','1','1','5', '\3','1','1','6', '\3','1','1','7', '\3','1','1','8', '\3','1','1','9', 
 '\3','1','2','0', '\3','1','2','1', '\3','1','2','2', '\3','1','2','3', '\3','1','2','4', 
 '\3','1','2','5', '\3','1','2','6', '\3','1','2','7', '\3','1','2','8', '\3','1','2','9', 
 '\3','1','3','0', '\3','1','3','1', '\3','1','3','2', '\3','1','3','3', '\3','1','3','4', 
 '\3','1','3','5', '\3','1','3','6', '\3','1','3','7', '\3','1','3','8', '\3','1','3','9', 
 '\3','1','4','0', '\3','1','4','1', '\3','1','4','2', '\3','1','4','3', '\3','1','4','4', 
 '\3','1','4','5', '\3','1','4','6', '\3','1','4','7', '\3','1','4','8', '\3','1','4','9', 
 '\3','1','5','0', '\3','1','5','1', '\3','1','5','2', '\3','1','5','3', '\3','1','5','4', 
 '\3','1','5','5', '\3','1','5','6', '\3','1','5','7', '\3','1','5','8', '\3','1','5','9', 
 '\3','1','6','0', '\3','1','6','1', '\3','1','6','2', '\3','1','6','3', '\3','1','6','4', 
 '\3','1','6','5', '\3','1','6','6', '\3','1','6','7', '\3','1','6','8', '\3','1','6','9', 
 '\3','1','7','0', '\3','1','7','1', '\3','1','7','2', '\3','1','7','3', '\3','1','7','4', 
 '\3','1','7','5', '\3','1','7','6', '\3','1','7','7', '\3','1','7','8', '\3','1','7','9', 
 '\3','1','8','0', '\3','1','8','1', '\3','1','8','2', '\3','1','8','3', '\3','1','8','4', 
 '\3','1','8','5', '\3','1','8','6', '\3','1','8','7', '\3','1','8','8', '\3','1','8','9', 
 '\3','1','9','0', '\3','1','9','1', '\3','1','9','2', '\3','1','9','3', '\3','1','9','4', 
 '\3','1','9','5', '\3','1','9','6', '\3','1','9','7', '\3','1','9','8', '\3','1','9','9', 
 '\3','2','0','0', '\3','2','0','1', '\3','2','0','2', '\3','2','0','3', '\3','2','0','4', 
 '\3','2','0','5', '\3','2','0','6', '\3','2','0','7', '\3','2','0','8', '\3','2','0','9', 
 '\3','2','1','0', '\3','2','1','1', '\3','2','1','2', '\3','2','1','3', '\3','2','1','4', 
 '\3','2','1','5', '\3','2','1','6', '\3','2','1','7', '\3','2','1','8', '\3','2','1','9', 
 '\3','2','2','0', '\3','2','2','1', '\3','2','2','2', '\3','2','2','3', '\3','2','2','4', 
 '\3','2','2','5', '\3','2','2','6', '\3','2','2','7', '\3','2','2','8', '\3','2','2','9', 
 '\3','2','3','0', '\3','2','3','1', '\3','2','3','2', '\3','2','3','3', '\3','2','3','4', 
 '\3','2','3','5', '\3','2','3','6', '\3','2','3','7', '\3','2','3','8', '\3','2','3','9', 
 '\3','2','4','0', '\3','2','4','1', '\3','2','4','2', '\3','2','4','3', '\3','2','4','4', 
 '\3','2','4','5', '\3','2','4','6', '\3','2','4','7', '\3','2','4','8', '\3','2','4','9', 
 '\3','2','5','0', '\3','2','5','1', '\3','2','5','2', '\3','2','5','3', '\3','2','5','4', 
 '\3','2','5','5', '\3','2','5','6', '\3','2','5','7', '\3','2','5','8', '\3','2','5','9', 
 '\3','2','6','0', '\3','2','6','1', '\3','2','6','2', '\3','2','6','3', '\3','2','6','4', 
 '\3','2','6','5', '\3','2','6','6', '\3','2','6','7', '\3','2','6','8', '\3','2','6','9', 
 '\3','2','7','0', '\3','2','7','1', '\3','2','7','2', '\3','2','7','3', '\3','2','7','4', 
 '\3','2','7','5', '\3','2','7','6', '\3','2','7','7', '\3','2','7','8', '\3','2','7','9', 
 '\3','2','8','0', '\3','2','8','1', '\3','2','8','2', '\3','2','8','3', '\3','2','8','4', 
 '\3','2','8','5', '\3','2','8','6', '\3','2','8','7', '\3','2','8','8', '\3','2','8','9', 
 '\3','2','9','0', '\3','2','9','1', '\3','2','9','2', '\3','2','9','3', '\3','2','9','4', 
 '\3','2','9','5', '\3','2','9','6', '\3','2','9','7', '\3','2','9','8', '\3','2','9','9', 
 '\3','3','0','0', '\3','3','0','1', '\3','3','0','2', '\3','3','0','3', '\3','3','0','4', 
 '\3','3','0','5', '\3','3','0','6', '\3','3','0','7', '\3','3','0','8', '\3','3','0','9', 
 '\3','3','1','0', '\3','3','1','1', '\3','3','1','2', '\3','3','1','3', '\3','3','1','4', 
 '\3','3','1','5', '\3','3','1','6', '\3','3','1','7', '\3','3','1','8', '\3','3','1','9', 
 '\3','3','2','0', '\3','3','2','1', '\3','3','2','2', '\3','3','2','3', '\3','3','2','4', 
 '\3','3','2','5', '\3','3','2','6', '\3','3','2','7', '\3','3','2','8', '\3','3','2','9', 
 '\3','3','3','0', '\3','3','3','1', '\3','3','3','2', '\3','3','3','3', '\3','3','3','4', 
 '\3','3','3','5', '\3','3','3','6', '\3','3','3','7', '\3','3','3','8', '\3','3','3','9', 
 '\3','3','4','0', '\3','3','4','1', '\3','3','4','2', '\3','3','4','3', '\3','3','4','4', 
 '\3','3','4','5', '\3','3','4','6', '\3','3','4','7', '\3','3','4','8', '\3','3','4','9', 
 '\3','3','5','0', '\3','3','5','1', '\3','3','5','2', '\3','3','5','3', '\3','3','5','4', 
 '\3','3','5','5', '\3','3','5','6', '\3','3','5','7', '\3','3','5','8', '\3','3','5','9', 
 '\3','3','6','0', '\3','3','6','1', '\3','3','6','2', '\3','3','6','3', '\3','3','6','4', 
 '\3','3','6','5', '\3','3','6','6', '\3','3','6','7', '\3','3','6','8', '\3','3','6','9', 
 '\3','3','7','0', '\3','3','7','1', '\3','3','7','2', '\3','3','7','3', '\3','3','7','4', 
 '\3','3','7','5', '\3','3','7','6', '\3','3','7','7', '\3','3','7','8', '\3','3','7','9', 
 '\3','3','8','0', '\3','3','8','1', '\3','3','8','2', '\3','3','8','3', '\3','3','8','4', 
 '\3','3','8','5', '\3','3','8','6', '\3','3','8','7', '\3','3','8','8', '\3','3','8','9', 
 '\3','3','9','0', '\3','3','9','1', '\3','3','9','2', '\3','3','9','3', '\3','3','9','4', 
 '\3','3','9','5', '\3','3','9','6', '\3','3','9','7', '\3','3','9','8', '\3','3','9','9', 
 '\3','4','0','0', '\3','4','0','1', '\3','4','0','2', '\3','4','0','3', '\3','4','0','4', 
 '\3','4','0','5', '\3','4','0','6', '\3','4','0','7', '\3','4','0','8', '\3','4','0','9', 
 '\3','4','1','0', '\3','4','1','1', '\3','4','1','2', '\3','4','1','3', '\3','4','1','4', 
 '\3','4','1','5', '\3','4','1','6', '\3','4','1','7', '\3','4','1','8', '\3','4','1','9', 
 '\3','4','2','0', '\3','4','2','1', '\3','4','2','2', '\3','4','2','3', '\3','4','2','4', 
 '\3','4','2','5', '\3','4','2','6', '\3','4','2','7', '\3','4','2','8', '\3','4','2','9', 
 '\3','4','3','0', '\3','4','3','1', '\3','4','3','2', '\3','4','3','3', '\3','4','3','4', 
 '\3','4','3','5', '\3','4','3','6', '\3','4','3','7', '\3','4','3','8', '\3','4','3','9', 
 '\3','4','4','0', '\3','4','4','1', '\3','4','4','2', '\3','4','4','3', '\3','4','4','4', 
 '\3','4','4','5', '\3','4','4','6', '\3','4','4','7', '\3','4','4','8', '\3','4','4','9', 
 '\3','4','5','0', '\3','4','5','1', '\3','4','5','2', '\3','4','5','3', '\3','4','5','4', 
 '\3','4','5','5', '\3','4','5','6', '\3','4','5','7', '\3','4','5','8', '\3','4','5','9', 
 '\3','4','6','0', '\3','4','6','1', '\3','4','6','2', '\3','4','6','3', '\3','4','6','4', 
 '\3','4','6','5', '\3','4','6','6', '\3','4','6','7', '\3','4','6','8', '\3','4','6','9', 
 '\3','4','7','0', '\3','4','7','1', '\3','4','7','2', '\3','4','7','3', '\3','4','7','4', 
 '\3','4','7','5', '\3','4','7','6', '\3','4','7','7', '\3','4','7','8', '\3','4','7','9', 
 '\3','4','8','0', '\3','4','8','1', '\3','4','8','2', '\3','4','8','3', '\3','4','8','4', 
 '\3','4','8','5', '\3','4','8','6', '\3','4','8','7', '\3','4','8','8', '\3','4','8','9', 
 '\3','4','9','0', '\3','4','9','1', '\3','4','9','2', '\3','4','9','3', '\3','4','9','4', 
 '\3','4','9','5', '\3','4','9','6', '\3','4','9','7', '\3','4','9','8', '\3','4','9','9', 
 '\3','5','0','0', '\3','5','0','1', '\3','5','0','2', '\3','5','0','3', '\3','5','0','4', 
 '\3','5','0','5', '\3','5','0','6', '\3','5','0','7', '\3','5','0','8', '\3','5','0','9', 
 '\3','5','1','0', '\3','5','1','1', '\3','5','1','2', '\3','5','1','3', '\3','5','1','4', 
 '\3','5','1','5', '\3','5','1','6', '\3','5','1','7', '\3','5','1','8', '\3','5','1','9', 
 '\3','5','2','0', '\3','5','2','1', '\3','5','2','2', '\3','5','2','3', '\3','5','2','4', 
 '\3','5','2','5', '\3','5','2','6', '\3','5','2','7', '\3','5','2','8', '\3','5','2','9', 
 '\3','5','3','0', '\3','5','3','1', '\3','5','3','2', '\3','5','3','3', '\3','5','3','4', 
 '\3','5','3','5', '\3','5','3','6', '\3','5','3','7', '\3','5','3','8', '\3','5','3','9', 
 '\3','5','4','0', '\3','5','4','1', '\3','5','4','2', '\3','5','4','3', '\3','5','4','4', 
 '\3','5','4','5', '\3','5','4','6', '\3','5','4','7', '\3','5','4','8', '\3','5','4','9', 
 '\3','5','5','0', '\3','5','5','1', '\3','5','5','2', '\3','5','5','3', '\3','5','5','4', 
 '\3','5','5','5', '\3','5','5','6', '\3','5','5','7', '\3','5','5','8', '\3','5','5','9', 
 '\3','5','6','0', '\3','5','6','1', '\3','5','6','2', '\3','5','6','3', '\3','5','6','4', 
 '\3','5','6','5', '\3','5','6','6', '\3','5','6','7', '\3','5','6','8', '\3','5','6','9', 
 '\3','5','7','0', '\3','5','7','1', '\3','5','7','2', '\3','5','7','3', '\3','5','7','4', 
 '\3','5','7','5', '\3','5','7','6', '\3','5','7','7', '\3','5','7','8', '\3','5','7','9', 
 '\3','5','8','0', '\3','5','8','1', '\3','5','8','2', '\3','5','8','3', '\3','5','8','4', 
 '\3','5','8','5', '\3','5','8','6', '\3','5','8','7', '\3','5','8','8', '\3','5','8','9', 
 '\3','5','9','0', '\3','5','9','1', '\3','5','9','2', '\3','5','9','3', '\3','5','9','4', 
 '\3','5','9','5', '\3','5','9','6', '\3','5','9','7', '\3','5','9','8', '\3','5','9','9', 
 '\3','6','0','0', '\3','6','0','1', '\3','6','0','2', '\3','6','0','3', '\3','6','0','4', 
 '\3','6','0','5', '\3','6','0','6', '\3','6','0','7', '\3','6','0','8', '\3','6','0','9', 
 '\3','6','1','0', '\3','6','1','1', '\3','6','1','2', '\3','6','1','3', '\3','6','1','4', 
 '\3','6','1','5', '\3','6','1','6', '\3','6','1','7', '\3','6','1','8', '\3','6','1','9', 
 '\3','6','2','0', '\3','6','2','1', '\3','6','2','2', '\3','6','2','3', '\3','6','2','4', 
 '\3','6','2','5', '\3','6','2','6', '\3','6','2','7', '\3','6','2','8', '\3','6','2','9', 
 '\3','6','3','0', '\3','6','3','1', '\3','6','3','2', '\3','6','3','3', '\3','6','3','4', 
 '\3','6','3','5', '\3','6','3','6', '\3','6','3','7', '\3','6','3','8', '\3','6','3','9', 
 '\3','6','4','0', '\3','6','4','1', '\3','6','4','2', '\3','6','4','3', '\3','6','4','4', 
 '\3','6','4','5', '\3','6','4','6', '\3','6','4','7', '\3','6','4','8', '\3','6','4','9', 
 '\3','6','5','0', '\3','6','5','1', '\3','6','5','2', '\3','6','5','3', '\3','6','5','4', 
 '\3','6','5','5', '\3','6','5','6', '\3','6','5','7', '\3','6','5','8', '\3','6','5','9', 
 '\3','6','6','0', '\3','6','6','1', '\3','6','6','2', '\3','6','6','3', '\3','6','6','4', 
 '\3','6','6','5', '\3','6','6','6', '\3','6','6','7', '\3','6','6','8', '\3','6','6','9', 
 '\3','6','7','0', '\3','6','7','1', '\3','6','7','2', '\3','6','7','3', '\3','6','7','4', 
 '\3','6','7','5', '\3','6','7','6', '\3','6','7','7', '\3','6','7','8', '\3','6','7','9', 
 '\3','6','8','0', '\3','6','8','1', '\3','6','8','2', '\3','6','8','3', '\3','6','8','4', 
 '\3','6','8','5', '\3','6','8','6', '\3','6','8','7', '\3','6','8','8', '\3','6','8','9', 
 '\3','6','9','0', '\3','6','9','1', '\3','6','9','2', '\3','6','9','3', '\3','6','9','4', 
 '\3','6','9','5', '\3','6','9','6', '\3','6','9','7', '\3','6','9','8', '\3','6','9','9', 
 '\3','7','0','0', '\3','7','0','1', '\3','7','0','2', '\3','7','0','3', '\3','7','0','4', 
 '\3','7','0','5', '\3','7','0','6', '\3','7','0','7', '\3','7','0','8', '\3','7','0','9', 
 '\3','7','1','0', '\3','7','1','1', '\3','7','1','2', '\3','7','1','3', '\3','7','1','4', 
 '\3','7','1','5', '\3','7','1','6', '\3','7','1','7', '\3','7','1','8', '\3','7','1','9', 
 '\3','7','2','0', '\3','7','2','1', '\3','7','2','2', '\3','7','2','3', '\3','7','2','4', 
 '\3','7','2','5', '\3','7','2','6', '\3','7','2','7', '\3','7','2','8', '\3','7','2','9', 
 '\3','7','3','0', '\3','7','3','1', '\3','7','3','2', '\3','7','3','3', '\3','7','3','4', 
 '\3','7','3','5', '\3','7','3','6', '\3','7','3','7', '\3','7','3','8', '\3','7','3','9', 
 '\3','7','4','0', '\3','7','4','1', '\3','7','4','2', '\3','7','4','3', '\3','7','4','4', 
 '\3','7','4','5', '\3','7','4','6', '\3','7','4','7', '\3','7','4','8', '\3','7','4','9', 
 '\3','7','5','0', '\3','7','5','1', '\3','7','5','2', '\3','7','5','3', '\3','7','5','4', 
 '\3','7','5','5', '\3','7','5','6', '\3','7','5','7', '\3','7','5','8', '\3','7','5','9', 
 '\3','7','6','0', '\3','7','6','1', '\3','7','6','2', '\3','7','6','3', '\3','7','6','4', 
 '\3','7','6','5', '\3','7','6','6', '\3','7','6','7', '\3','7','6','8', '\3','7','6','9', 
 '\3','7','7','0', '\3','7','7','1', '\3','7','7','2', '\3','7','7','3', '\3','7','7','4', 
 '\3','7','7','5', '\3','7','7','6', '\3','7','7','7', '\3','7','7','8', '\3','7','7','9', 
 '\3','7','8','0', '\3','7','8','1', '\3','7','8','2', '\3','7','8','3', '\3','7','8','4', 
 '\3','7','8','5', '\3','7','8','6', '\3','7','8','7', '\3','7','8','8', '\3','7','8','9', 
 '\3','7','9','0', '\3','7','9','1', '\3','7','9','2', '\3','7','9','3', '\3','7','9','4', 
 '\3','7','9','5', '\3','7','9','6', '\3','7','9','7', '\3','7','9','8', '\3','7','9','9', 
 '\3','8','0','0', '\3','8','0','1', '\3','8','0','2', '\3','8','0','3', '\3','8','0','4', 
 '\3','8','0','5', '\3','8','0','6', '\3','8','0','7', '\3','8','0','8', '\3','8','0','9', 
 '\3','8','1','0', '\3','8','1','1', '\3','8','1','2', '\3','8','1','3', '\3','8','1','4', 
 '\3','8','1','5', '\3','8','1','6', '\3','8','1','7', '\3','8','1','8', '\3','8','1','9', 
 '\3','8','2','0', '\3','8','2','1', '\3','8','2','2', '\3','8','2','3', '\3','8','2','4', 
 '\3','8','2','5', '\3','8','2','6', '\3','8','2','7', '\3','8','2','8', '\3','8','2','9', 
 '\3','8','3','0', '\3','8','3','1', '\3','8','3','2', '\3','8','3','3', '\3','8','3','4', 
 '\3','8','3','5', '\3','8','3','6', '\3','8','3','7', '\3','8','3','8', '\3','8','3','9', 
 '\3','8','4','0', '\3','8','4','1', '\3','8','4','2', '\3','8','4','3', '\3','8','4','4', 
 '\3','8','4','5', '\3','8','4','6', '\3','8','4','7', '\3','8','4','8', '\3','8','4','9', 
 '\3','8','5','0', '\3','8','5','1', '\3','8','5','2', '\3','8','5','3', '\3','8','5','4', 
 '\3','8','5','5', '\3','8','5','6', '\3','8','5','7', '\3','8','5','8', '\3','8','5','9', 
 '\3','8','6','0', '\3','8','6','1', '\3','8','6','2', '\3','8','6','3', '\3','8','6','4', 
 '\3','8','6','5', '\3','8','6','6', '\3','8','6','7', '\3','8','6','8', '\3','8','6','9', 
 '\3','8','7','0', '\3','8','7','1', '\3','8','7','2', '\3','8','7','3', '\3','8','7','4', 
 '\3','8','7','5', '\3','8','7','6', '\3','8','7','7', '\3','8','7','8', '\3','8','7','9', 
 '\3','8','8','0', '\3','8','8','1', '\3','8','8','2', '\3','8','8','3', '\3','8','8','4', 
 '\3','8','8','5', '\3','8','8','6', '\3','8','8','7', '\3','8','8','8', '\3','8','8','9', 
 '\3','8','9','0', '\3','8','9','1', '\3','8','9','2', '\3','8','9','3', '\3','8','9','4', 
 '\3','8','9','5', '\3','8','9','6', '\3','8','9','7', '\3','8','9','8', '\3','8','9','9', 
 '\3','9','0','0', '\3','9','0','1', '\3','9','0','2', '\3','9','0','3', '\3','9','0','4', 
 '\3','9','0','5', '\3','9','0','6', '\3','9','0','7', '\3','9','0','8', '\3','9','0','9', 
 '\3','9','1','0', '\3','9','1','1', '\3','9','1','2', '\3','9','1','3', '\3','9','1','4', 
 '\3','9','1','5', '\3','9','1','6', '\3','9','1','7', '\3','9','1','8', '\3','9','1','9', 
 '\3','9','2','0', '\3','9','2','1', '\3','9','2','2', '\3','9','2','3', '\3','9','2','4', 
 '\3','9','2','5', '\3','9','2','6', '\3','9','2','7', '\3','9','2','8', '\3','9','2','9', 
 '\3','9','3','0', '\3','9','3','1', '\3','9','3','2', '\3','9','3','3', '\3','9','3','4', 
 '\3','9','3','5', '\3','9','3','6', '\3','9','3','7', '\3','9','3','8', '\3','9','3','9', 
 '\3','9','4','0', '\3','9','4','1', '\3','9','4','2', '\3','9','4','3', '\3','9','4','4', 
 '\3','9','4','5', '\3','9','4','6', '\3','9','4','7', '\3','9','4','8', '\3','9','4','9', 
 '\3','9','5','0', '\3','9','5','1', '\3','9','5','2', '\3','9','5','3', '\3','9','5','4', 
 '\3','9','5','5', '\3','9','5','6', '\3','9','5','7', '\3','9','5','8', '\3','9','5','9', 
 '\3','9','6','0', '\3','9','6','1', '\3','9','6','2', '\3','9','6','3', '\3','9','6','4', 
 '\3','9','6','5', '\3','9','6','6', '\3','9','6','7', '\3','9','6','8', '\3','9','6','9', 
 '\3','9','7','0', '\3','9','7','1', '\3','9','7','2', '\3','9','7','3', '\3','9','7','4', 
 '\3','9','7','5', '\3','9','7','6', '\3','9','7','7', '\3','9','7','8', '\3','9','7','9', 
 '\3','9','8','0', '\3','9','8','1', '\3','9','8','2', '\3','9','8','3', '\3','9','8','4', 
 '\3','9','8','5', '\3','9','8','6', '\3','9','8','7', '\3','9','8','8', '\3','9','8','9', 
 '\3','9','9','0', '\3','9','9','1', '\3','9','9','2', '\3','9','9','3', '\3','9','9','4', 
 '\3','9','9','5', '\3','9','9','6', '\3','9','9','7', '\3','9','9','8', '\3','9','9','9', '\0'};
#endif
 
#if defined(DEC_DPD2BCD8) && DEC_DPD2BCD8==1 && !defined(DECDPD2BCD8)
#define DECDPD2BCD8
 
const uint8_t DPD2BCD8[4096]={
 0,0,0,0, 0,0,1,1, 0,0,2,1, 0,0,3,1, 0,0,4,1, 0,0,5,1, 0,0,6,1, 0,0,7,1, 0,0,8,1, 
 0,0,9,1, 0,8,0,2, 0,8,1,2, 8,0,0,3, 8,0,1,3, 8,8,0,3, 8,8,1,3, 0,1,0,2, 0,1,1,2, 
 0,1,2,2, 0,1,3,2, 0,1,4,2, 0,1,5,2, 0,1,6,2, 0,1,7,2, 0,1,8,2, 0,1,9,2, 0,9,0,2, 
 0,9,1,2, 8,1,0,3, 8,1,1,3, 8,9,0,3, 8,9,1,3, 0,2,0,2, 0,2,1,2, 0,2,2,2, 0,2,3,2, 
 0,2,4,2, 0,2,5,2, 0,2,6,2, 0,2,7,2, 0,2,8,2, 0,2,9,2, 0,8,2,2, 0,8,3,2, 8,2,0,3, 
 8,2,1,3, 8,0,8,3, 8,0,9,3, 0,3,0,2, 0,3,1,2, 0,3,2,2, 0,3,3,2, 0,3,4,2, 0,3,5,2, 
 0,3,6,2, 0,3,7,2, 0,3,8,2, 0,3,9,2, 0,9,2,2, 0,9,3,2, 8,3,0,3, 8,3,1,3, 8,1,8,3, 
 8,1,9,3, 0,4,0,2, 0,4,1,2, 0,4,2,2, 0,4,3,2, 0,4,4,2, 0,4,5,2, 0,4,6,2, 0,4,7,2, 
 0,4,8,2, 0,4,9,2, 0,8,4,2, 0,8,5,2, 8,4,0,3, 8,4,1,3, 0,8,8,2, 0,8,9,2, 0,5,0,2, 
 0,5,1,2, 0,5,2,2, 0,5,3,2, 0,5,4,2, 0,5,5,2, 0,5,6,2, 0,5,7,2, 0,5,8,2, 0,5,9,2, 
 0,9,4,2, 0,9,5,2, 8,5,0,3, 8,5,1,3, 0,9,8,2, 0,9,9,2, 0,6,0,2, 0,6,1,2, 0,6,2,2, 
 0,6,3,2, 0,6,4,2, 0,6,5,2, 0,6,6,2, 0,6,7,2, 0,6,8,2, 0,6,9,2, 0,8,6,2, 0,8,7,2, 
 8,6,0,3, 8,6,1,3, 8,8,8,3, 8,8,9,3, 0,7,0,2, 0,7,1,2, 0,7,2,2, 0,7,3,2, 0,7,4,2, 
 0,7,5,2, 0,7,6,2, 0,7,7,2, 0,7,8,2, 0,7,9,2, 0,9,6,2, 0,9,7,2, 8,7,0,3, 8,7,1,3, 
 8,9,8,3, 8,9,9,3, 1,0,0,3, 1,0,1,3, 1,0,2,3, 1,0,3,3, 1,0,4,3, 1,0,5,3, 1,0,6,3, 
 1,0,7,3, 1,0,8,3, 1,0,9,3, 1,8,0,3, 1,8,1,3, 9,0,0,3, 9,0,1,3, 9,8,0,3, 9,8,1,3, 
 1,1,0,3, 1,1,1,3, 1,1,2,3, 1,1,3,3, 1,1,4,3, 1,1,5,3, 1,1,6,3, 1,1,7,3, 1,1,8,3, 
 1,1,9,3, 1,9,0,3, 1,9,1,3, 9,1,0,3, 9,1,1,3, 9,9,0,3, 9,9,1,3, 1,2,0,3, 1,2,1,3, 
 1,2,2,3, 1,2,3,3, 1,2,4,3, 1,2,5,3, 1,2,6,3, 1,2,7,3, 1,2,8,3, 1,2,9,3, 1,8,2,3, 
 1,8,3,3, 9,2,0,3, 9,2,1,3, 9,0,8,3, 9,0,9,3, 1,3,0,3, 1,3,1,3, 1,3,2,3, 1,3,3,3, 
 1,3,4,3, 1,3,5,3, 1,3,6,3, 1,3,7,3, 1,3,8,3, 1,3,9,3, 1,9,2,3, 1,9,3,3, 9,3,0,3, 
 9,3,1,3, 9,1,8,3, 9,1,9,3, 1,4,0,3, 1,4,1,3, 1,4,2,3, 1,4,3,3, 1,4,4,3, 1,4,5,3, 
 1,4,6,3, 1,4,7,3, 1,4,8,3, 1,4,9,3, 1,8,4,3, 1,8,5,3, 9,4,0,3, 9,4,1,3, 1,8,8,3, 
 1,8,9,3, 1,5,0,3, 1,5,1,3, 1,5,2,3, 1,5,3,3, 1,5,4,3, 1,5,5,3, 1,5,6,3, 1,5,7,3, 
 1,5,8,3, 1,5,9,3, 1,9,4,3, 1,9,5,3, 9,5,0,3, 9,5,1,3, 1,9,8,3, 1,9,9,3, 1,6,0,3, 
 1,6,1,3, 1,6,2,3, 1,6,3,3, 1,6,4,3, 1,6,5,3, 1,6,6,3, 1,6,7,3, 1,6,8,3, 1,6,9,3, 
 1,8,6,3, 1,8,7,3, 9,6,0,3, 9,6,1,3, 9,8,8,3, 9,8,9,3, 1,7,0,3, 1,7,1,3, 1,7,2,3, 
 1,7,3,3, 1,7,4,3, 1,7,5,3, 1,7,6,3, 1,7,7,3, 1,7,8,3, 1,7,9,3, 1,9,6,3, 1,9,7,3, 
 9,7,0,3, 9,7,1,3, 9,9,8,3, 9,9,9,3, 2,0,0,3, 2,0,1,3, 2,0,2,3, 2,0,3,3, 2,0,4,3, 
 2,0,5,3, 2,0,6,3, 2,0,7,3, 2,0,8,3, 2,0,9,3, 2,8,0,3, 2,8,1,3, 8,0,2,3, 8,0,3,3, 
 8,8,2,3, 8,8,3,3, 2,1,0,3, 2,1,1,3, 2,1,2,3, 2,1,3,3, 2,1,4,3, 2,1,5,3, 2,1,6,3, 
 2,1,7,3, 2,1,8,3, 2,1,9,3, 2,9,0,3, 2,9,1,3, 8,1,2,3, 8,1,3,3, 8,9,2,3, 8,9,3,3, 
 2,2,0,3, 2,2,1,3, 2,2,2,3, 2,2,3,3, 2,2,4,3, 2,2,5,3, 2,2,6,3, 2,2,7,3, 2,2,8,3, 
 2,2,9,3, 2,8,2,3, 2,8,3,3, 8,2,2,3, 8,2,3,3, 8,2,8,3, 8,2,9,3, 2,3,0,3, 2,3,1,3, 
 2,3,2,3, 2,3,3,3, 2,3,4,3, 2,3,5,3, 2,3,6,3, 2,3,7,3, 2,3,8,3, 2,3,9,3, 2,9,2,3, 
 2,9,3,3, 8,3,2,3, 8,3,3,3, 8,3,8,3, 8,3,9,3, 2,4,0,3, 2,4,1,3, 2,4,2,3, 2,4,3,3, 
 2,4,4,3, 2,4,5,3, 2,4,6,3, 2,4,7,3, 2,4,8,3, 2,4,9,3, 2,8,4,3, 2,8,5,3, 8,4,2,3, 
 8,4,3,3, 2,8,8,3, 2,8,9,3, 2,5,0,3, 2,5,1,3, 2,5,2,3, 2,5,3,3, 2,5,4,3, 2,5,5,3, 
 2,5,6,3, 2,5,7,3, 2,5,8,3, 2,5,9,3, 2,9,4,3, 2,9,5,3, 8,5,2,3, 8,5,3,3, 2,9,8,3, 
 2,9,9,3, 2,6,0,3, 2,6,1,3, 2,6,2,3, 2,6,3,3, 2,6,4,3, 2,6,5,3, 2,6,6,3, 2,6,7,3, 
 2,6,8,3, 2,6,9,3, 2,8,6,3, 2,8,7,3, 8,6,2,3, 8,6,3,3, 8,8,8,3, 8,8,9,3, 2,7,0,3, 
 2,7,1,3, 2,7,2,3, 2,7,3,3, 2,7,4,3, 2,7,5,3, 2,7,6,3, 2,7,7,3, 2,7,8,3, 2,7,9,3, 
 2,9,6,3, 2,9,7,3, 8,7,2,3, 8,7,3,3, 8,9,8,3, 8,9,9,3, 3,0,0,3, 3,0,1,3, 3,0,2,3, 
 3,0,3,3, 3,0,4,3, 3,0,5,3, 3,0,6,3, 3,0,7,3, 3,0,8,3, 3,0,9,3, 3,8,0,3, 3,8,1,3, 
 9,0,2,3, 9,0,3,3, 9,8,2,3, 9,8,3,3, 3,1,0,3, 3,1,1,3, 3,1,2,3, 3,1,3,3, 3,1,4,3, 
 3,1,5,3, 3,1,6,3, 3,1,7,3, 3,1,8,3, 3,1,9,3, 3,9,0,3, 3,9,1,3, 9,1,2,3, 9,1,3,3, 
 9,9,2,3, 9,9,3,3, 3,2,0,3, 3,2,1,3, 3,2,2,3, 3,2,3,3, 3,2,4,3, 3,2,5,3, 3,2,6,3, 
 3,2,7,3, 3,2,8,3, 3,2,9,3, 3,8,2,3, 3,8,3,3, 9,2,2,3, 9,2,3,3, 9,2,8,3, 9,2,9,3, 
 3,3,0,3, 3,3,1,3, 3,3,2,3, 3,3,3,3, 3,3,4,3, 3,3,5,3, 3,3,6,3, 3,3,7,3, 3,3,8,3, 
 3,3,9,3, 3,9,2,3, 3,9,3,3, 9,3,2,3, 9,3,3,3, 9,3,8,3, 9,3,9,3, 3,4,0,3, 3,4,1,3, 
 3,4,2,3, 3,4,3,3, 3,4,4,3, 3,4,5,3, 3,4,6,3, 3,4,7,3, 3,4,8,3, 3,4,9,3, 3,8,4,3, 
 3,8,5,3, 9,4,2,3, 9,4,3,3, 3,8,8,3, 3,8,9,3, 3,5,0,3, 3,5,1,3, 3,5,2,3, 3,5,3,3, 
 3,5,4,3, 3,5,5,3, 3,5,6,3, 3,5,7,3, 3,5,8,3, 3,5,9,3, 3,9,4,3, 3,9,5,3, 9,5,2,3, 
 9,5,3,3, 3,9,8,3, 3,9,9,3, 3,6,0,3, 3,6,1,3, 3,6,2,3, 3,6,3,3, 3,6,4,3, 3,6,5,3, 
 3,6,6,3, 3,6,7,3, 3,6,8,3, 3,6,9,3, 3,8,6,3, 3,8,7,3, 9,6,2,3, 9,6,3,3, 9,8,8,3, 
 9,8,9,3, 3,7,0,3, 3,7,1,3, 3,7,2,3, 3,7,3,3, 3,7,4,3, 3,7,5,3, 3,7,6,3, 3,7,7,3, 
 3,7,8,3, 3,7,9,3, 3,9,6,3, 3,9,7,3, 9,7,2,3, 9,7,3,3, 9,9,8,3, 9,9,9,3, 4,0,0,3, 
 4,0,1,3, 4,0,2,3, 4,0,3,3, 4,0,4,3, 4,0,5,3, 4,0,6,3, 4,0,7,3, 4,0,8,3, 4,0,9,3, 
 4,8,0,3, 4,8,1,3, 8,0,4,3, 8,0,5,3, 8,8,4,3, 8,8,5,3, 4,1,0,3, 4,1,1,3, 4,1,2,3, 
 4,1,3,3, 4,1,4,3, 4,1,5,3, 4,1,6,3, 4,1,7,3, 4,1,8,3, 4,1,9,3, 4,9,0,3, 4,9,1,3, 
 8,1,4,3, 8,1,5,3, 8,9,4,3, 8,9,5,3, 4,2,0,3, 4,2,1,3, 4,2,2,3, 4,2,3,3, 4,2,4,3, 
 4,2,5,3, 4,2,6,3, 4,2,7,3, 4,2,8,3, 4,2,9,3, 4,8,2,3, 4,8,3,3, 8,2,4,3, 8,2,5,3, 
 8,4,8,3, 8,4,9,3, 4,3,0,3, 4,3,1,3, 4,3,2,3, 4,3,3,3, 4,3,4,3, 4,3,5,3, 4,3,6,3, 
 4,3,7,3, 4,3,8,3, 4,3,9,3, 4,9,2,3, 4,9,3,3, 8,3,4,3, 8,3,5,3, 8,5,8,3, 8,5,9,3, 
 4,4,0,3, 4,4,1,3, 4,4,2,3, 4,4,3,3, 4,4,4,3, 4,4,5,3, 4,4,6,3, 4,4,7,3, 4,4,8,3, 
 4,4,9,3, 4,8,4,3, 4,8,5,3, 8,4,4,3, 8,4,5,3, 4,8,8,3, 4,8,9,3, 4,5,0,3, 4,5,1,3, 
 4,5,2,3, 4,5,3,3, 4,5,4,3, 4,5,5,3, 4,5,6,3, 4,5,7,3, 4,5,8,3, 4,5,9,3, 4,9,4,3, 
 4,9,5,3, 8,5,4,3, 8,5,5,3, 4,9,8,3, 4,9,9,3, 4,6,0,3, 4,6,1,3, 4,6,2,3, 4,6,3,3, 
 4,6,4,3, 4,6,5,3, 4,6,6,3, 4,6,7,3, 4,6,8,3, 4,6,9,3, 4,8,6,3, 4,8,7,3, 8,6,4,3, 
 8,6,5,3, 8,8,8,3, 8,8,9,3, 4,7,0,3, 4,7,1,3, 4,7,2,3, 4,7,3,3, 4,7,4,3, 4,7,5,3, 
 4,7,6,3, 4,7,7,3, 4,7,8,3, 4,7,9,3, 4,9,6,3, 4,9,7,3, 8,7,4,3, 8,7,5,3, 8,9,8,3, 
 8,9,9,3, 5,0,0,3, 5,0,1,3, 5,0,2,3, 5,0,3,3, 5,0,4,3, 5,0,5,3, 5,0,6,3, 5,0,7,3, 
 5,0,8,3, 5,0,9,3, 5,8,0,3, 5,8,1,3, 9,0,4,3, 9,0,5,3, 9,8,4,3, 9,8,5,3, 5,1,0,3, 
 5,1,1,3, 5,1,2,3, 5,1,3,3, 5,1,4,3, 5,1,5,3, 5,1,6,3, 5,1,7,3, 5,1,8,3, 5,1,9,3, 
 5,9,0,3, 5,9,1,3, 9,1,4,3, 9,1,5,3, 9,9,4,3, 9,9,5,3, 5,2,0,3, 5,2,1,3, 5,2,2,3, 
 5,2,3,3, 5,2,4,3, 5,2,5,3, 5,2,6,3, 5,2,7,3, 5,2,8,3, 5,2,9,3, 5,8,2,3, 5,8,3,3, 
 9,2,4,3, 9,2,5,3, 9,4,8,3, 9,4,9,3, 5,3,0,3, 5,3,1,3, 5,3,2,3, 5,3,3,3, 5,3,4,3, 
 5,3,5,3, 5,3,6,3, 5,3,7,3, 5,3,8,3, 5,3,9,3, 5,9,2,3, 5,9,3,3, 9,3,4,3, 9,3,5,3, 
 9,5,8,3, 9,5,9,3, 5,4,0,3, 5,4,1,3, 5,4,2,3, 5,4,3,3, 5,4,4,3, 5,4,5,3, 5,4,6,3, 
 5,4,7,3, 5,4,8,3, 5,4,9,3, 5,8,4,3, 5,8,5,3, 9,4,4,3, 9,4,5,3, 5,8,8,3, 5,8,9,3, 
 5,5,0,3, 5,5,1,3, 5,5,2,3, 5,5,3,3, 5,5,4,3, 5,5,5,3, 5,5,6,3, 5,5,7,3, 5,5,8,3, 
 5,5,9,3, 5,9,4,3, 5,9,5,3, 9,5,4,3, 9,5,5,3, 5,9,8,3, 5,9,9,3, 5,6,0,3, 5,6,1,3, 
 5,6,2,3, 5,6,3,3, 5,6,4,3, 5,6,5,3, 5,6,6,3, 5,6,7,3, 5,6,8,3, 5,6,9,3, 5,8,6,3, 
 5,8,7,3, 9,6,4,3, 9,6,5,3, 9,8,8,3, 9,8,9,3, 5,7,0,3, 5,7,1,3, 5,7,2,3, 5,7,3,3, 
 5,7,4,3, 5,7,5,3, 5,7,6,3, 5,7,7,3, 5,7,8,3, 5,7,9,3, 5,9,6,3, 5,9,7,3, 9,7,4,3, 
 9,7,5,3, 9,9,8,3, 9,9,9,3, 6,0,0,3, 6,0,1,3, 6,0,2,3, 6,0,3,3, 6,0,4,3, 6,0,5,3, 
 6,0,6,3, 6,0,7,3, 6,0,8,3, 6,0,9,3, 6,8,0,3, 6,8,1,3, 8,0,6,3, 8,0,7,3, 8,8,6,3, 
 8,8,7,3, 6,1,0,3, 6,1,1,3, 6,1,2,3, 6,1,3,3, 6,1,4,3, 6,1,5,3, 6,1,6,3, 6,1,7,3, 
 6,1,8,3, 6,1,9,3, 6,9,0,3, 6,9,1,3, 8,1,6,3, 8,1,7,3, 8,9,6,3, 8,9,7,3, 6,2,0,3, 
 6,2,1,3, 6,2,2,3, 6,2,3,3, 6,2,4,3, 6,2,5,3, 6,2,6,3, 6,2,7,3, 6,2,8,3, 6,2,9,3, 
 6,8,2,3, 6,8,3,3, 8,2,6,3, 8,2,7,3, 8,6,8,3, 8,6,9,3, 6,3,0,3, 6,3,1,3, 6,3,2,3, 
 6,3,3,3, 6,3,4,3, 6,3,5,3, 6,3,6,3, 6,3,7,3, 6,3,8,3, 6,3,9,3, 6,9,2,3, 6,9,3,3, 
 8,3,6,3, 8,3,7,3, 8,7,8,3, 8,7,9,3, 6,4,0,3, 6,4,1,3, 6,4,2,3, 6,4,3,3, 6,4,4,3, 
 6,4,5,3, 6,4,6,3, 6,4,7,3, 6,4,8,3, 6,4,9,3, 6,8,4,3, 6,8,5,3, 8,4,6,3, 8,4,7,3, 
 6,8,8,3, 6,8,9,3, 6,5,0,3, 6,5,1,3, 6,5,2,3, 6,5,3,3, 6,5,4,3, 6,5,5,3, 6,5,6,3, 
 6,5,7,3, 6,5,8,3, 6,5,9,3, 6,9,4,3, 6,9,5,3, 8,5,6,3, 8,5,7,3, 6,9,8,3, 6,9,9,3, 
 6,6,0,3, 6,6,1,3, 6,6,2,3, 6,6,3,3, 6,6,4,3, 6,6,5,3, 6,6,6,3, 6,6,7,3, 6,6,8,3, 
 6,6,9,3, 6,8,6,3, 6,8,7,3, 8,6,6,3, 8,6,7,3, 8,8,8,3, 8,8,9,3, 6,7,0,3, 6,7,1,3, 
 6,7,2,3, 6,7,3,3, 6,7,4,3, 6,7,5,3, 6,7,6,3, 6,7,7,3, 6,7,8,3, 6,7,9,3, 6,9,6,3, 
 6,9,7,3, 8,7,6,3, 8,7,7,3, 8,9,8,3, 8,9,9,3, 7,0,0,3, 7,0,1,3, 7,0,2,3, 7,0,3,3, 
 7,0,4,3, 7,0,5,3, 7,0,6,3, 7,0,7,3, 7,0,8,3, 7,0,9,3, 7,8,0,3, 7,8,1,3, 9,0,6,3, 
 9,0,7,3, 9,8,6,3, 9,8,7,3, 7,1,0,3, 7,1,1,3, 7,1,2,3, 7,1,3,3, 7,1,4,3, 7,1,5,3, 
 7,1,6,3, 7,1,7,3, 7,1,8,3, 7,1,9,3, 7,9,0,3, 7,9,1,3, 9,1,6,3, 9,1,7,3, 9,9,6,3, 
 9,9,7,3, 7,2,0,3, 7,2,1,3, 7,2,2,3, 7,2,3,3, 7,2,4,3, 7,2,5,3, 7,2,6,3, 7,2,7,3, 
 7,2,8,3, 7,2,9,3, 7,8,2,3, 7,8,3,3, 9,2,6,3, 9,2,7,3, 9,6,8,3, 9,6,9,3, 7,3,0,3, 
 7,3,1,3, 7,3,2,3, 7,3,3,3, 7,3,4,3, 7,3,5,3, 7,3,6,3, 7,3,7,3, 7,3,8,3, 7,3,9,3, 
 7,9,2,3, 7,9,3,3, 9,3,6,3, 9,3,7,3, 9,7,8,3, 9,7,9,3, 7,4,0,3, 7,4,1,3, 7,4,2,3, 
 7,4,3,3, 7,4,4,3, 7,4,5,3, 7,4,6,3, 7,4,7,3, 7,4,8,3, 7,4,9,3, 7,8,4,3, 7,8,5,3, 
 9,4,6,3, 9,4,7,3, 7,8,8,3, 7,8,9,3, 7,5,0,3, 7,5,1,3, 7,5,2,3, 7,5,3,3, 7,5,4,3, 
 7,5,5,3, 7,5,6,3, 7,5,7,3, 7,5,8,3, 7,5,9,3, 7,9,4,3, 7,9,5,3, 9,5,6,3, 9,5,7,3, 
 7,9,8,3, 7,9,9,3, 7,6,0,3, 7,6,1,3, 7,6,2,3, 7,6,3,3, 7,6,4,3, 7,6,5,3, 7,6,6,3, 
 7,6,7,3, 7,6,8,3, 7,6,9,3, 7,8,6,3, 7,8,7,3, 9,6,6,3, 9,6,7,3, 9,8,8,3, 9,8,9,3, 
 7,7,0,3, 7,7,1,3, 7,7,2,3, 7,7,3,3, 7,7,4,3, 7,7,5,3, 7,7,6,3, 7,7,7,3, 7,7,8,3, 
 7,7,9,3, 7,9,6,3, 7,9,7,3, 9,7,6,3, 9,7,7,3, 9,9,8,3, 9,9,9,3};
#endif
 
#if defined(DEC_BIN2BCD8) && DEC_BIN2BCD8==1 && !defined(DECBIN2BCD8)
#define DECBIN2BCD8
 
const uint8_t BIN2BCD8[4000]={
 0,0,0,0, 0,0,1,1, 0,0,2,1, 0,0,3,1, 0,0,4,1, 0,0,5,1, 0,0,6,1, 0,0,7,1, 0,0,8,1, 
 0,0,9,1, 0,1,0,2, 0,1,1,2, 0,1,2,2, 0,1,3,2, 0,1,4,2, 0,1,5,2, 0,1,6,2, 0,1,7,2, 
 0,1,8,2, 0,1,9,2, 0,2,0,2, 0,2,1,2, 0,2,2,2, 0,2,3,2, 0,2,4,2, 0,2,5,2, 0,2,6,2, 
 0,2,7,2, 0,2,8,2, 0,2,9,2, 0,3,0,2, 0,3,1,2, 0,3,2,2, 0,3,3,2, 0,3,4,2, 0,3,5,2, 
 0,3,6,2, 0,3,7,2, 0,3,8,2, 0,3,9,2, 0,4,0,2, 0,4,1,2, 0,4,2,2, 0,4,3,2, 0,4,4,2, 
 0,4,5,2, 0,4,6,2, 0,4,7,2, 0,4,8,2, 0,4,9,2, 0,5,0,2, 0,5,1,2, 0,5,2,2, 0,5,3,2, 
 0,5,4,2, 0,5,5,2, 0,5,6,2, 0,5,7,2, 0,5,8,2, 0,5,9,2, 0,6,0,2, 0,6,1,2, 0,6,2,2, 
 0,6,3,2, 0,6,4,2, 0,6,5,2, 0,6,6,2, 0,6,7,2, 0,6,8,2, 0,6,9,2, 0,7,0,2, 0,7,1,2, 
 0,7,2,2, 0,7,3,2, 0,7,4,2, 0,7,5,2, 0,7,6,2, 0,7,7,2, 0,7,8,2, 0,7,9,2, 0,8,0,2, 
 0,8,1,2, 0,8,2,2, 0,8,3,2, 0,8,4,2, 0,8,5,2, 0,8,6,2, 0,8,7,2, 0,8,8,2, 0,8,9,2, 
 0,9,0,2, 0,9,1,2, 0,9,2,2, 0,9,3,2, 0,9,4,2, 0,9,5,2, 0,9,6,2, 0,9,7,2, 0,9,8,2, 
 0,9,9,2, 1,0,0,3, 1,0,1,3, 1,0,2,3, 1,0,3,3, 1,0,4,3, 1,0,5,3, 1,0,6,3, 1,0,7,3, 
 1,0,8,3, 1,0,9,3, 1,1,0,3, 1,1,1,3, 1,1,2,3, 1,1,3,3, 1,1,4,3, 1,1,5,3, 1,1,6,3, 
 1,1,7,3, 1,1,8,3, 1,1,9,3, 1,2,0,3, 1,2,1,3, 1,2,2,3, 1,2,3,3, 1,2,4,3, 1,2,5,3, 
 1,2,6,3, 1,2,7,3, 1,2,8,3, 1,2,9,3, 1,3,0,3, 1,3,1,3, 1,3,2,3, 1,3,3,3, 1,3,4,3, 
 1,3,5,3, 1,3,6,3, 1,3,7,3, 1,3,8,3, 1,3,9,3, 1,4,0,3, 1,4,1,3, 1,4,2,3, 1,4,3,3, 
 1,4,4,3, 1,4,5,3, 1,4,6,3, 1,4,7,3, 1,4,8,3, 1,4,9,3, 1,5,0,3, 1,5,1,3, 1,5,2,3, 
 1,5,3,3, 1,5,4,3, 1,5,5,3, 1,5,6,3, 1,5,7,3, 1,5,8,3, 1,5,9,3, 1,6,0,3, 1,6,1,3, 
 1,6,2,3, 1,6,3,3, 1,6,4,3, 1,6,5,3, 1,6,6,3, 1,6,7,3, 1,6,8,3, 1,6,9,3, 1,7,0,3, 
 1,7,1,3, 1,7,2,3, 1,7,3,3, 1,7,4,3, 1,7,5,3, 1,7,6,3, 1,7,7,3, 1,7,8,3, 1,7,9,3, 
 1,8,0,3, 1,8,1,3, 1,8,2,3, 1,8,3,3, 1,8,4,3, 1,8,5,3, 1,8,6,3, 1,8,7,3, 1,8,8,3, 
 1,8,9,3, 1,9,0,3, 1,9,1,3, 1,9,2,3, 1,9,3,3, 1,9,4,3, 1,9,5,3, 1,9,6,3, 1,9,7,3, 
 1,9,8,3, 1,9,9,3, 2,0,0,3, 2,0,1,3, 2,0,2,3, 2,0,3,3, 2,0,4,3, 2,0,5,3, 2,0,6,3, 
 2,0,7,3, 2,0,8,3, 2,0,9,3, 2,1,0,3, 2,1,1,3, 2,1,2,3, 2,1,3,3, 2,1,4,3, 2,1,5,3, 
 2,1,6,3, 2,1,7,3, 2,1,8,3, 2,1,9,3, 2,2,0,3, 2,2,1,3, 2,2,2,3, 2,2,3,3, 2,2,4,3, 
 2,2,5,3, 2,2,6,3, 2,2,7,3, 2,2,8,3, 2,2,9,3, 2,3,0,3, 2,3,1,3, 2,3,2,3, 2,3,3,3, 
 2,3,4,3, 2,3,5,3, 2,3,6,3, 2,3,7,3, 2,3,8,3, 2,3,9,3, 2,4,0,3, 2,4,1,3, 2,4,2,3, 
 2,4,3,3, 2,4,4,3, 2,4,5,3, 2,4,6,3, 2,4,7,3, 2,4,8,3, 2,4,9,3, 2,5,0,3, 2,5,1,3, 
 2,5,2,3, 2,5,3,3, 2,5,4,3, 2,5,5,3, 2,5,6,3, 2,5,7,3, 2,5,8,3, 2,5,9,3, 2,6,0,3, 
 2,6,1,3, 2,6,2,3, 2,6,3,3, 2,6,4,3, 2,6,5,3, 2,6,6,3, 2,6,7,3, 2,6,8,3, 2,6,9,3, 
 2,7,0,3, 2,7,1,3, 2,7,2,3, 2,7,3,3, 2,7,4,3, 2,7,5,3, 2,7,6,3, 2,7,7,3, 2,7,8,3, 
 2,7,9,3, 2,8,0,3, 2,8,1,3, 2,8,2,3, 2,8,3,3, 2,8,4,3, 2,8,5,3, 2,8,6,3, 2,8,7,3, 
 2,8,8,3, 2,8,9,3, 2,9,0,3, 2,9,1,3, 2,9,2,3, 2,9,3,3, 2,9,4,3, 2,9,5,3, 2,9,6,3, 
 2,9,7,3, 2,9,8,3, 2,9,9,3, 3,0,0,3, 3,0,1,3, 3,0,2,3, 3,0,3,3, 3,0,4,3, 3,0,5,3, 
 3,0,6,3, 3,0,7,3, 3,0,8,3, 3,0,9,3, 3,1,0,3, 3,1,1,3, 3,1,2,3, 3,1,3,3, 3,1,4,3, 
 3,1,5,3, 3,1,6,3, 3,1,7,3, 3,1,8,3, 3,1,9,3, 3,2,0,3, 3,2,1,3, 3,2,2,3, 3,2,3,3, 
 3,2,4,3, 3,2,5,3, 3,2,6,3, 3,2,7,3, 3,2,8,3, 3,2,9,3, 3,3,0,3, 3,3,1,3, 3,3,2,3, 
 3,3,3,3, 3,3,4,3, 3,3,5,3, 3,3,6,3, 3,3,7,3, 3,3,8,3, 3,3,9,3, 3,4,0,3, 3,4,1,3, 
 3,4,2,3, 3,4,3,3, 3,4,4,3, 3,4,5,3, 3,4,6,3, 3,4,7,3, 3,4,8,3, 3,4,9,3, 3,5,0,3, 
 3,5,1,3, 3,5,2,3, 3,5,3,3, 3,5,4,3, 3,5,5,3, 3,5,6,3, 3,5,7,3, 3,5,8,3, 3,5,9,3, 
 3,6,0,3, 3,6,1,3, 3,6,2,3, 3,6,3,3, 3,6,4,3, 3,6,5,3, 3,6,6,3, 3,6,7,3, 3,6,8,3, 
 3,6,9,3, 3,7,0,3, 3,7,1,3, 3,7,2,3, 3,7,3,3, 3,7,4,3, 3,7,5,3, 3,7,6,3, 3,7,7,3, 
 3,7,8,3, 3,7,9,3, 3,8,0,3, 3,8,1,3, 3,8,2,3, 3,8,3,3, 3,8,4,3, 3,8,5,3, 3,8,6,3, 
 3,8,7,3, 3,8,8,3, 3,8,9,3, 3,9,0,3, 3,9,1,3, 3,9,2,3, 3,9,3,3, 3,9,4,3, 3,9,5,3, 
 3,9,6,3, 3,9,7,3, 3,9,8,3, 3,9,9,3, 4,0,0,3, 4,0,1,3, 4,0,2,3, 4,0,3,3, 4,0,4,3, 
 4,0,5,3, 4,0,6,3, 4,0,7,3, 4,0,8,3, 4,0,9,3, 4,1,0,3, 4,1,1,3, 4,1,2,3, 4,1,3,3, 
 4,1,4,3, 4,1,5,3, 4,1,6,3, 4,1,7,3, 4,1,8,3, 4,1,9,3, 4,2,0,3, 4,2,1,3, 4,2,2,3, 
 4,2,3,3, 4,2,4,3, 4,2,5,3, 4,2,6,3, 4,2,7,3, 4,2,8,3, 4,2,9,3, 4,3,0,3, 4,3,1,3, 
 4,3,2,3, 4,3,3,3, 4,3,4,3, 4,3,5,3, 4,3,6,3, 4,3,7,3, 4,3,8,3, 4,3,9,3, 4,4,0,3, 
 4,4,1,3, 4,4,2,3, 4,4,3,3, 4,4,4,3, 4,4,5,3, 4,4,6,3, 4,4,7,3, 4,4,8,3, 4,4,9,3, 
 4,5,0,3, 4,5,1,3, 4,5,2,3, 4,5,3,3, 4,5,4,3, 4,5,5,3, 4,5,6,3, 4,5,7,3, 4,5,8,3, 
 4,5,9,3, 4,6,0,3, 4,6,1,3, 4,6,2,3, 4,6,3,3, 4,6,4,3, 4,6,5,3, 4,6,6,3, 4,6,7,3, 
 4,6,8,3, 4,6,9,3, 4,7,0,3, 4,7,1,3, 4,7,2,3, 4,7,3,3, 4,7,4,3, 4,7,5,3, 4,7,6,3, 
 4,7,7,3, 4,7,8,3, 4,7,9,3, 4,8,0,3, 4,8,1,3, 4,8,2,3, 4,8,3,3, 4,8,4,3, 4,8,5,3, 
 4,8,6,3, 4,8,7,3, 4,8,8,3, 4,8,9,3, 4,9,0,3, 4,9,1,3, 4,9,2,3, 4,9,3,3, 4,9,4,3, 
 4,9,5,3, 4,9,6,3, 4,9,7,3, 4,9,8,3, 4,9,9,3, 5,0,0,3, 5,0,1,3, 5,0,2,3, 5,0,3,3, 
 5,0,4,3, 5,0,5,3, 5,0,6,3, 5,0,7,3, 5,0,8,3, 5,0,9,3, 5,1,0,3, 5,1,1,3, 5,1,2,3, 
 5,1,3,3, 5,1,4,3, 5,1,5,3, 5,1,6,3, 5,1,7,3, 5,1,8,3, 5,1,9,3, 5,2,0,3, 5,2,1,3, 
 5,2,2,3, 5,2,3,3, 5,2,4,3, 5,2,5,3, 5,2,6,3, 5,2,7,3, 5,2,8,3, 5,2,9,3, 5,3,0,3, 
 5,3,1,3, 5,3,2,3, 5,3,3,3, 5,3,4,3, 5,3,5,3, 5,3,6,3, 5,3,7,3, 5,3,8,3, 5,3,9,3, 
 5,4,0,3, 5,4,1,3, 5,4,2,3, 5,4,3,3, 5,4,4,3, 5,4,5,3, 5,4,6,3, 5,4,7,3, 5,4,8,3, 
 5,4,9,3, 5,5,0,3, 5,5,1,3, 5,5,2,3, 5,5,3,3, 5,5,4,3, 5,5,5,3, 5,5,6,3, 5,5,7,3, 
 5,5,8,3, 5,5,9,3, 5,6,0,3, 5,6,1,3, 5,6,2,3, 5,6,3,3, 5,6,4,3, 5,6,5,3, 5,6,6,3, 
 5,6,7,3, 5,6,8,3, 5,6,9,3, 5,7,0,3, 5,7,1,3, 5,7,2,3, 5,7,3,3, 5,7,4,3, 5,7,5,3, 
 5,7,6,3, 5,7,7,3, 5,7,8,3, 5,7,9,3, 5,8,0,3, 5,8,1,3, 5,8,2,3, 5,8,3,3, 5,8,4,3, 
 5,8,5,3, 5,8,6,3, 5,8,7,3, 5,8,8,3, 5,8,9,3, 5,9,0,3, 5,9,1,3, 5,9,2,3, 5,9,3,3, 
 5,9,4,3, 5,9,5,3, 5,9,6,3, 5,9,7,3, 5,9,8,3, 5,9,9,3, 6,0,0,3, 6,0,1,3, 6,0,2,3, 
 6,0,3,3, 6,0,4,3, 6,0,5,3, 6,0,6,3, 6,0,7,3, 6,0,8,3, 6,0,9,3, 6,1,0,3, 6,1,1,3, 
 6,1,2,3, 6,1,3,3, 6,1,4,3, 6,1,5,3, 6,1,6,3, 6,1,7,3, 6,1,8,3, 6,1,9,3, 6,2,0,3, 
 6,2,1,3, 6,2,2,3, 6,2,3,3, 6,2,4,3, 6,2,5,3, 6,2,6,3, 6,2,7,3, 6,2,8,3, 6,2,9,3, 
 6,3,0,3, 6,3,1,3, 6,3,2,3, 6,3,3,3, 6,3,4,3, 6,3,5,3, 6,3,6,3, 6,3,7,3, 6,3,8,3, 
 6,3,9,3, 6,4,0,3, 6,4,1,3, 6,4,2,3, 6,4,3,3, 6,4,4,3, 6,4,5,3, 6,4,6,3, 6,4,7,3, 
 6,4,8,3, 6,4,9,3, 6,5,0,3, 6,5,1,3, 6,5,2,3, 6,5,3,3, 6,5,4,3, 6,5,5,3, 6,5,6,3, 
 6,5,7,3, 6,5,8,3, 6,5,9,3, 6,6,0,3, 6,6,1,3, 6,6,2,3, 6,6,3,3, 6,6,4,3, 6,6,5,3, 
 6,6,6,3, 6,6,7,3, 6,6,8,3, 6,6,9,3, 6,7,0,3, 6,7,1,3, 6,7,2,3, 6,7,3,3, 6,7,4,3, 
 6,7,5,3, 6,7,6,3, 6,7,7,3, 6,7,8,3, 6,7,9,3, 6,8,0,3, 6,8,1,3, 6,8,2,3, 6,8,3,3, 
 6,8,4,3, 6,8,5,3, 6,8,6,3, 6,8,7,3, 6,8,8,3, 6,8,9,3, 6,9,0,3, 6,9,1,3, 6,9,2,3, 
 6,9,3,3, 6,9,4,3, 6,9,5,3, 6,9,6,3, 6,9,7,3, 6,9,8,3, 6,9,9,3, 7,0,0,3, 7,0,1,3, 
 7,0,2,3, 7,0,3,3, 7,0,4,3, 7,0,5,3, 7,0,6,3, 7,0,7,3, 7,0,8,3, 7,0,9,3, 7,1,0,3, 
 7,1,1,3, 7,1,2,3, 7,1,3,3, 7,1,4,3, 7,1,5,3, 7,1,6,3, 7,1,7,3, 7,1,8,3, 7,1,9,3, 
 7,2,0,3, 7,2,1,3, 7,2,2,3, 7,2,3,3, 7,2,4,3, 7,2,5,3, 7,2,6,3, 7,2,7,3, 7,2,8,3, 
 7,2,9,3, 7,3,0,3, 7,3,1,3, 7,3,2,3, 7,3,3,3, 7,3,4,3, 7,3,5,3, 7,3,6,3, 7,3,7,3, 
 7,3,8,3, 7,3,9,3, 7,4,0,3, 7,4,1,3, 7,4,2,3, 7,4,3,3, 7,4,4,3, 7,4,5,3, 7,4,6,3, 
 7,4,7,3, 7,4,8,3, 7,4,9,3, 7,5,0,3, 7,5,1,3, 7,5,2,3, 7,5,3,3, 7,5,4,3, 7,5,5,3, 
 7,5,6,3, 7,5,7,3, 7,5,8,3, 7,5,9,3, 7,6,0,3, 7,6,1,3, 7,6,2,3, 7,6,3,3, 7,6,4,3, 
 7,6,5,3, 7,6,6,3, 7,6,7,3, 7,6,8,3, 7,6,9,3, 7,7,0,3, 7,7,1,3, 7,7,2,3, 7,7,3,3, 
 7,7,4,3, 7,7,5,3, 7,7,6,3, 7,7,7,3, 7,7,8,3, 7,7,9,3, 7,8,0,3, 7,8,1,3, 7,8,2,3, 
 7,8,3,3, 7,8,4,3, 7,8,5,3, 7,8,6,3, 7,8,7,3, 7,8,8,3, 7,8,9,3, 7,9,0,3, 7,9,1,3, 
 7,9,2,3, 7,9,3,3, 7,9,4,3, 7,9,5,3, 7,9,6,3, 7,9,7,3, 7,9,8,3, 7,9,9,3, 8,0,0,3, 
 8,0,1,3, 8,0,2,3, 8,0,3,3, 8,0,4,3, 8,0,5,3, 8,0,6,3, 8,0,7,3, 8,0,8,3, 8,0,9,3, 
 8,1,0,3, 8,1,1,3, 8,1,2,3, 8,1,3,3, 8,1,4,3, 8,1,5,3, 8,1,6,3, 8,1,7,3, 8,1,8,3, 
 8,1,9,3, 8,2,0,3, 8,2,1,3, 8,2,2,3, 8,2,3,3, 8,2,4,3, 8,2,5,3, 8,2,6,3, 8,2,7,3, 
 8,2,8,3, 8,2,9,3, 8,3,0,3, 8,3,1,3, 8,3,2,3, 8,3,3,3, 8,3,4,3, 8,3,5,3, 8,3,6,3, 
 8,3,7,3, 8,3,8,3, 8,3,9,3, 8,4,0,3, 8,4,1,3, 8,4,2,3, 8,4,3,3, 8,4,4,3, 8,4,5,3, 
 8,4,6,3, 8,4,7,3, 8,4,8,3, 8,4,9,3, 8,5,0,3, 8,5,1,3, 8,5,2,3, 8,5,3,3, 8,5,4,3, 
 8,5,5,3, 8,5,6,3, 8,5,7,3, 8,5,8,3, 8,5,9,3, 8,6,0,3, 8,6,1,3, 8,6,2,3, 8,6,3,3, 
 8,6,4,3, 8,6,5,3, 8,6,6,3, 8,6,7,3, 8,6,8,3, 8,6,9,3, 8,7,0,3, 8,7,1,3, 8,7,2,3, 
 8,7,3,3, 8,7,4,3, 8,7,5,3, 8,7,6,3, 8,7,7,3, 8,7,8,3, 8,7,9,3, 8,8,0,3, 8,8,1,3, 
 8,8,2,3, 8,8,3,3, 8,8,4,3, 8,8,5,3, 8,8,6,3, 8,8,7,3, 8,8,8,3, 8,8,9,3, 8,9,0,3, 
 8,9,1,3, 8,9,2,3, 8,9,3,3, 8,9,4,3, 8,9,5,3, 8,9,6,3, 8,9,7,3, 8,9,8,3, 8,9,9,3, 
 9,0,0,3, 9,0,1,3, 9,0,2,3, 9,0,3,3, 9,0,4,3, 9,0,5,3, 9,0,6,3, 9,0,7,3, 9,0,8,3, 
 9,0,9,3, 9,1,0,3, 9,1,1,3, 9,1,2,3, 9,1,3,3, 9,1,4,3, 9,1,5,3, 9,1,6,3, 9,1,7,3, 
 9,1,8,3, 9,1,9,3, 9,2,0,3, 9,2,1,3, 9,2,2,3, 9,2,3,3, 9,2,4,3, 9,2,5,3, 9,2,6,3, 
 9,2,7,3, 9,2,8,3, 9,2,9,3, 9,3,0,3, 9,3,1,3, 9,3,2,3, 9,3,3,3, 9,3,4,3, 9,3,5,3, 
 9,3,6,3, 9,3,7,3, 9,3,8,3, 9,3,9,3, 9,4,0,3, 9,4,1,3, 9,4,2,3, 9,4,3,3, 9,4,4,3, 
 9,4,5,3, 9,4,6,3, 9,4,7,3, 9,4,8,3, 9,4,9,3, 9,5,0,3, 9,5,1,3, 9,5,2,3, 9,5,3,3, 
 9,5,4,3, 9,5,5,3, 9,5,6,3, 9,5,7,3, 9,5,8,3, 9,5,9,3, 9,6,0,3, 9,6,1,3, 9,6,2,3, 
 9,6,3,3, 9,6,4,3, 9,6,5,3, 9,6,6,3, 9,6,7,3, 9,6,8,3, 9,6,9,3, 9,7,0,3, 9,7,1,3, 
 9,7,2,3, 9,7,3,3, 9,7,4,3, 9,7,5,3, 9,7,6,3, 9,7,7,3, 9,7,8,3, 9,7,9,3, 9,8,0,3, 
 9,8,1,3, 9,8,2,3, 9,8,3,3, 9,8,4,3, 9,8,5,3, 9,8,6,3, 9,8,7,3, 9,8,8,3, 9,8,9,3, 
 9,9,0,3, 9,9,1,3, 9,9,2,3, 9,9,3,3, 9,9,4,3, 9,9,5,3, 9,9,6,3, 9,9,7,3, 9,9,8,3, 
 9,9,9,3};
#endif
 
