#pragma once

#include "Factor.h"
#include "BasicFactors.h"
#include "StatisticalFactors.h"
#include "Utils.h"
#include <torch/torch.h>
#include <memory>
#include <vector>

namespace Spectre {

// Forward declarations
class FactorEngine;

// ============================================================================
// Feature Engineering Factors - Corresponding to Python's feature.py
// ============================================================================

// RankFactor: Cross-sectional ranking
class RankFactor : public CrossSectionFactor {
public:
    RankFactor(const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, bool ascending = true)
        : CrossSectionFactor(1, inputs), m_ascending(ascending) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RankFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Get ranks for each time step (across assets)
        torch::Tensor ranks;
        if (m_ascending) {
            // For ascending: smaller values get smaller ranks
            auto sorted_result = torch::sort(data, 0, false);  // descending sort
            torch::Tensor sorted_indices = std::get<1>(sorted_result);
            ranks = torch::empty_like(sorted_indices, torch::kFloat32);
            
            // Assign ranks
            for (int64_t i = 0; i < data.size(0); ++i) {
                ranks.scatter_(0, sorted_indices, torch::arange(data.size(0), torch::kFloat32) + 1);
            }
        } else {
            // For descending: larger values get smaller ranks
            auto sorted_result = torch::sort(data, 0, true);   // ascending sort
            torch::Tensor sorted_indices = std::get<1>(sorted_result);
            ranks = torch::empty_like(sorted_indices, torch::kFloat32);
            
            // Assign ranks
            for (int64_t i = 0; i < data.size(0); ++i) {
                ranks.scatter_(0, sorted_indices, torch::arange(data.size(0), torch::kFloat32) + 1);
            }
        }
        
        return ranks;
    }

private:
    bool m_ascending;
};

// QuantileFactor: Cross-sectional quantile transformation
class QuantileFactor : public CrossSectionFactor {
public:
    QuantileFactor(const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, int n_quantiles = 5)
        : CrossSectionFactor(1, inputs), m_n_quantiles(n_quantiles) {
        if (n_quantiles <= 0) {
            throw std::runtime_error("n_quantiles must be positive");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("QuantileFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate quantile boundaries for each time step
        std::vector<float> quantile_points;
        for (int i = 1; i < m_n_quantiles; ++i) {
            quantile_points.push_back(static_cast<float>(i) / m_n_quantiles);
        }
        
        torch::Tensor result = torch::zeros_like(data);
        
        for (int64_t t = 0; t < data.size(1); ++t) {
            torch::Tensor time_slice = data.select(1, t);
            torch::Tensor valid_mask = ~time_slice.isnan();
            
            if (valid_mask.sum().item<int64_t>() > 0) {
                torch::Tensor valid_data = time_slice.masked_select(valid_mask);
                
                // Calculate quantile thresholds
                std::vector<float> thresholds;
                for (float q : quantile_points) {
                    torch::Tensor quantile_val = torch::quantile(valid_data, q);
                    thresholds.push_back(quantile_val.item<float>());
                }
                
                // Assign quantile labels
                torch::Tensor time_result = torch::zeros_like(time_slice);
                for (int64_t i = 0; i < time_slice.size(0); ++i) {
                    if (valid_mask[i].item<bool>()) {
                        float val = time_slice[i].item<float>();
                        int quantile = 0;
                        for (size_t j = 0; j < thresholds.size(); ++j) {
                            if (val <= thresholds[j]) {
                                quantile = static_cast<int>(j);
                                break;
                            }
                        }
                        if (quantile == 0 && val > thresholds.back()) {
                            quantile = m_n_quantiles - 1;
                        }
                        time_result[i] = quantile + 1;  // 1-based quantiles
                    }
                }
                result.select(1, t).copy_(time_result);
            }
        }
        
        return result;
    }

private:
    int m_n_quantiles;
};

// ZScoreFactor: Cross-sectional z-score normalization
class ZScoreFactor : public CrossSectionFactor {
public:
    ZScoreFactor(const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CrossSectionFactor(1, inputs) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("ZScoreFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate cross-sectional mean and std for each time step
        torch::Tensor mean_val = Spectre::nanmean(data, 0, true);
        torch::Tensor std_val = Spectre::nanstd(data, 0, true);
        
        // Z-score normalization
        torch::Tensor zscore = (data - mean_val) / (std_val + 1e-8f);  // Add small epsilon to avoid division by zero
        
        return zscore;
    }
};

// WinsorizedFactor: Winsorization (outlier clipping)
class WinsorizedFactor : public CrossSectionFactor {
public:
    WinsorizedFactor(const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, 
                    float lower_percentile = 0.05f, float upper_percentile = 0.95f)
        : CrossSectionFactor(1, inputs), m_lower_percentile(lower_percentile), m_upper_percentile(upper_percentile) {
        if (lower_percentile < 0.0f || upper_percentile > 1.0f || lower_percentile >= upper_percentile) {
            throw std::runtime_error("Invalid percentile range for winsorization");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("WinsorizedFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate percentile thresholds for each time step
        torch::Tensor lower_threshold = torch::quantile(data, m_lower_percentile, 0, true);
        torch::Tensor upper_threshold = torch::quantile(data, m_upper_percentile, 0, true);
        
        // Clip values to the thresholds
        torch::Tensor result = torch::clamp(data, lower_threshold, upper_threshold);
        
        return result;
    }

private:
    float m_lower_percentile;
    float m_upper_percentile;
};

// DemeanFactor: Cross-sectional demeaning
class DemeanFactor : public CrossSectionFactor {
public:
    DemeanFactor(const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CrossSectionFactor(1, inputs) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("DemeanFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate cross-sectional mean for each time step
        torch::Tensor mean_val = Spectre::nanmean(data, 0, true);
        
        // Subtract mean
        torch::Tensor demeaned = data - mean_val;
        
        return demeaned;
    }
};

// NeutralizeFactor: Neutralize against another factor
class NeutralizeFactor : public CrossSectionFactor {
public:
    NeutralizeFactor(const std::shared_ptr<BaseFactor>& target_factor, 
                    const std::shared_ptr<BaseFactor>& neutralize_factor)
        : CrossSectionFactor(1, {target_factor, neutralize_factor}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("NeutralizeFactor expects exactly two inputs.");
        }
        
        torch::Tensor target = inputs[0];
        torch::Tensor neutralize = inputs[1];
        
        // Simple linear regression neutralization: target - beta * neutralize
        // where beta = cov(target, neutralize) / var(neutralize)
        
        torch::Tensor target_demean = target - Spectre::nanmean(target, 0, true);
        torch::Tensor neutralize_demean = neutralize - Spectre::nanmean(neutralize, 0, true);
        
        torch::Tensor covariance = Spectre::nanmean(target_demean * neutralize_demean, 0, true);
        torch::Tensor variance = Spectre::nanmean(neutralize_demean * neutralize_demean, 0, true);
        
        torch::Tensor beta = covariance / (variance + 1e-8f);
        torch::Tensor neutralized = target - beta * neutralize;
        
        return neutralized;
    }
};

// Type aliases for convenience
using Rank = RankFactor;
using Quantile = QuantileFactor;
using ZScore = ZScoreFactor;

} // namespace Spectre
