﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{55738EC1-5F68-4EF3-8EFD-98B824ED388F}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>ChartCtrl</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <OutputFile>$(SolutionDir)..\lib\$(Platform)\$(Configuration)\$(TargetName).lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <OutputFile>$(SolutionDir)..\lib\$(Platform)\$(Configuration)\$(TargetName).lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <OutputFile>$(SolutionDir)..\lib\$(Platform)\$(Configuration)\$(TargetName).lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <OutputFile>$(SolutionDir)..\lib\$(Platform)\$(Configuration)\$(TargetName).lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\quicklist\QuickEdit.h" />
    <ClInclude Include="..\quicklist\QuickList.h" />
    <ClInclude Include="..\quicklist\theme.h" />
    <ClInclude Include="..\quicklist\ThemeLib.h" />
    <ClInclude Include="ChartAxis.h" />
    <ClInclude Include="ChartAxisLabel.h" />
    <ClInclude Include="ChartBalloonLabel.h" />
    <ClInclude Include="ChartBarSerie.h" />
    <ClInclude Include="ChartCandlestickSerie.h" />
    <ClInclude Include="ChartCrossHairCursor.h" />
    <ClInclude Include="ChartCtrl.h" />
    <ClInclude Include="ChartCursor.h" />
    <ClInclude Include="ChartDateTimeAxis.h" />
    <ClInclude Include="ChartDragLineCursor.h" />
    <ClInclude Include="ChartFont.h" />
    <ClInclude Include="ChartGanttSerie.h" />
    <ClInclude Include="ChartGradient.h" />
    <ClInclude Include="ChartGrid.h" />
    <ClInclude Include="ChartLabel.h" />
    <ClInclude Include="ChartLegend.h" />
    <ClInclude Include="ChartLineSerie.h" />
    <ClInclude Include="ChartLogarithmicAxis.h" />
    <ClInclude Include="ChartMouseListener.h" />
    <ClInclude Include="ChartPointsArray.h" />
    <ClInclude Include="ChartPointsSerie.h" />
    <ClInclude Include="ChartScrollBar.h" />
    <ClInclude Include="ChartSerie.h" />
    <ClInclude Include="ChartSerieBase.h" />
    <ClInclude Include="ChartSeriesMouseListener.h" />
    <ClInclude Include="ChartStandardAxis.h" />
    <ClInclude Include="ChartStickSerie.h" />
    <ClInclude Include="ChartString.h" />
    <ClInclude Include="ChartSurfaceSerie.h" />
    <ClInclude Include="ChartTitle.h" />
    <ClInclude Include="ChartXYSerie.h" />
    <ClInclude Include="PointsOrdering.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\quicklist\QuickEdit.cpp" />
    <ClCompile Include="..\quicklist\QuickList.cpp" />
    <ClCompile Include="..\quicklist\theme.cpp" />
    <ClCompile Include="ChartAxis.cpp" />
    <ClCompile Include="ChartAxisLabel.cpp" />
    <ClCompile Include="ChartBarSerie.cpp" />
    <ClCompile Include="ChartCandlestickSerie.cpp" />
    <ClCompile Include="ChartCrossHairCursor.cpp" />
    <ClCompile Include="ChartCtrl.cpp" />
    <ClCompile Include="ChartCursor.cpp" />
    <ClCompile Include="ChartDateTimeAxis.cpp" />
    <ClCompile Include="ChartDragLineCursor.cpp" />
    <ClCompile Include="ChartFont.cpp" />
    <ClCompile Include="ChartGanttSerie.cpp" />
    <ClCompile Include="ChartGradient.cpp" />
    <ClCompile Include="ChartGrid.cpp" />
    <ClCompile Include="ChartLegend.cpp" />
    <ClCompile Include="ChartLineSerie.cpp" />
    <ClCompile Include="ChartLogarithmicAxis.cpp" />
    <ClCompile Include="ChartPointsSerie.cpp" />
    <ClCompile Include="ChartScrollBar.cpp" />
    <ClCompile Include="ChartSerie.cpp" />
    <ClCompile Include="ChartStandardAxis.cpp" />
    <ClCompile Include="ChartStickSerie.cpp" />
    <ClCompile Include="ChartSurfaceSerie.cpp" />
    <ClCompile Include="ChartTitle.cpp" />
    <ClCompile Include="ChartXYSerie.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>