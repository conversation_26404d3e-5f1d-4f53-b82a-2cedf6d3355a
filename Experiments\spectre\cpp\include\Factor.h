#pragma once

#include <torch/torch.h>
#include <string>
#include <vector>
#include <memory>

namespace Spectre {

// Forward declarations
class FactorEngine;
class Rolling;

// BaseFactor: Base class for all factors, corresponding to Python's BaseFactor
class BaseFactor {
public:
    // Pure virtual function that all derived classes must implement
    virtual torch::Tensor compute(const std::vector<torch::Tensor>& inputs) = 0;

    // Helper methods called by engine before/after computation
    virtual void pre_compute(FactorEngine* engine) { m_engine = engine; }
    virtual void clean_up() { m_engine = nullptr; }

    // Get the number of historical data points required for computation
    virtual int get_total_backwards() const = 0;

    // Whether this factor should be delayed (e.g., signal generated on day T, usable on T+1)
    virtual bool should_delay() const { return false; }

    // Destructor
    virtual ~BaseFactor() = default;

protected:
    FactorEngine* m_engine = nullptr; // Pointer to engine for accessing data
    std::string m_groupby = "asset"; // Default grouping method
};

// CustomFactor: Corresponds to Python's CustomFactor, adds window and input factors
class CustomFactor : public BaseFactor {
public:
    // Constructor
    CustomFactor(int win = 1, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {});

    // Implement BaseFactor's pure virtual function
    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override = 0;

    // Get the number of historical data points required for computation
    int get_total_backwards() const override;

    // Set mask (for filtering data)
    void set_mask(std::shared_ptr<BaseFactor> mask) { m_mask = mask; }
    std::shared_ptr<BaseFactor> get_mask() const { return m_mask; }

    // Delay control
    void set_delay(bool delay) { m_force_delay = delay; }
    bool should_delay() const override;

    // Pre-computation and cleanup
    void pre_compute(FactorEngine* engine) override;
    void clean_up() override;

    // Main computation entry point (corresponds to Python's compute_)
    torch::Tensor compute_();

protected:
    int m_win; // Window size
    int m_min_win = 1; // Minimum window size
    std::vector<std::shared_ptr<BaseFactor>> m_inputs; // Input factors
    std::shared_ptr<BaseFactor> m_mask; // Mask factor
    bool m_force_delay = false; // Force delay

    // Cache related
    int m_ref_count = 0;
    bool m_keep_cache = false;
    torch::Tensor m_cache;

    // Format input data
    torch::Tensor format_input(std::shared_ptr<BaseFactor> upstream,
                              const torch::Tensor& upstream_out,
                              std::shared_ptr<BaseFactor> mask_factor,
                              const torch::Tensor& mask_out);
};

// CrossSectionFactor: Factors where inputs and return values are grouped by datetime
class CrossSectionFactor : public CustomFactor {
public:
    CrossSectionFactor(int win = 1, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {},
                      std::shared_ptr<BaseFactor> mask = nullptr);

protected:
    // Override groupby to "date" for cross-section factors
    void initialize_groupby() { m_groupby = "date"; }
};

} // namespace Spectre
