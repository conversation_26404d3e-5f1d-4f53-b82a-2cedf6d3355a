#pragma once

#include "Factor.h"
#include "Utils.h"
#include <torch/torch.h>
#include <memory>
#include <string>
#include <unordered_map>

namespace Spectre {

// Forward declarations
class FactorEngine;

// ============================================================================
// Data Factors - Corresponding to <PERSON>'s datafactor.py
// ============================================================================

// InterpolationFactor: Fill missing values using interpolation
class InterpolationFactor : public CustomFactor {
public:
    enum class Method {
        FORWARD_FILL,   // Forward fill (carry last observation forward)
        BACKWARD_FILL,  // Backward fill
        LINEAR,         // Linear interpolation
        MEAN_FILL       // Fill with mean value
    };

    InterpolationFactor(const std::shared_ptr<BaseFactor>& factor, Method method = Method::FORWARD_FILL)
        : CustomFactor(1, {factor}), m_method(method) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("InterpolationFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0].clone();
        
        switch (m_method) {
            case Method::FORWARD_FILL:
                return forward_fill(data);
                
            case Method::BACKWARD_FILL:
                return backward_fill(data);
                
            case Method::LINEAR:
                return linear_interpolate(data);
                
            case Method::MEAN_FILL:
                return mean_fill(data);
        }
        
        return data;
    }

private:
    Method m_method;

    torch::Tensor forward_fill(torch::Tensor data) {
        // Forward fill along time dimension (dim=1)
        for (int64_t asset = 0; asset < data.size(0); ++asset) {
            torch::Tensor asset_data = data[asset];
            float last_valid = std::numeric_limits<float>::quiet_NaN();
            
            for (int64_t t = 0; t < asset_data.size(0); ++t) {
                float val = asset_data[t].item<float>();
                if (!std::isnan(val)) {
                    last_valid = val;
                } else if (!std::isnan(last_valid)) {
                    asset_data[t] = last_valid;
                }
            }
        }
        return data;
    }

    torch::Tensor backward_fill(torch::Tensor data) {
        // Backward fill along time dimension (dim=1)
        for (int64_t asset = 0; asset < data.size(0); ++asset) {
            torch::Tensor asset_data = data[asset];
            float next_valid = std::numeric_limits<float>::quiet_NaN();
            
            for (int64_t t = asset_data.size(0) - 1; t >= 0; --t) {
                float val = asset_data[t].item<float>();
                if (!std::isnan(val)) {
                    next_valid = val;
                } else if (!std::isnan(next_valid)) {
                    asset_data[t] = next_valid;
                }
            }
        }
        return data;
    }

    torch::Tensor linear_interpolate(torch::Tensor data) {
        // Simple linear interpolation
        for (int64_t asset = 0; asset < data.size(0); ++asset) {
            torch::Tensor asset_data = data[asset];
            
            for (int64_t t = 0; t < asset_data.size(0); ++t) {
                if (std::isnan(asset_data[t].item<float>())) {
                    // Find previous and next valid values
                    int64_t prev_idx = -1, next_idx = -1;
                    
                    for (int64_t i = t - 1; i >= 0; --i) {
                        if (!std::isnan(asset_data[i].item<float>())) {
                            prev_idx = i;
                            break;
                        }
                    }
                    
                    for (int64_t i = t + 1; i < asset_data.size(0); ++i) {
                        if (!std::isnan(asset_data[i].item<float>())) {
                            next_idx = i;
                            break;
                        }
                    }
                    
                    // Interpolate if both boundaries exist
                    if (prev_idx >= 0 && next_idx >= 0) {
                        float prev_val = asset_data[prev_idx].item<float>();
                        float next_val = asset_data[next_idx].item<float>();
                        float weight = static_cast<float>(t - prev_idx) / (next_idx - prev_idx);
                        asset_data[t] = prev_val + weight * (next_val - prev_val);
                    }
                }
            }
        }
        return data;
    }

    torch::Tensor mean_fill(torch::Tensor data) {
        // Fill with cross-sectional mean at each time step
        torch::Tensor mean_val = Spectre::nanmean(data, 0, true);
        torch::Tensor mask = data.isnan();
        return data.masked_fill(mask, 0.0f) + mean_val.expand_as(data).masked_fill(~mask, 0.0f);
    }
};

// OutlierDetectionFactor: Detect and optionally remove outliers
class OutlierDetectionFactor : public CrossSectionFactor {
public:
    enum class Method {
        Z_SCORE,        // Z-score based detection
        IQR,           // Interquartile range based detection
        PERCENTILE     // Percentile based detection
    };

    OutlierDetectionFactor(const std::shared_ptr<BaseFactor>& factor, Method method = Method::Z_SCORE, 
                          float threshold = 3.0f, bool remove_outliers = false)
        : CrossSectionFactor(1, {factor}), m_method(method), m_threshold(threshold), m_remove_outliers(remove_outliers) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("OutlierDetectionFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        torch::Tensor outlier_mask = detect_outliers(data);
        
        if (m_remove_outliers) {
            // Replace outliers with NaN
            return data.masked_fill(outlier_mask, std::numeric_limits<float>::quiet_NaN());
        } else {
            // Return outlier mask (1 for outliers, 0 for normal values)
            return outlier_mask.to(torch::kFloat32);
        }
    }

private:
    Method m_method;
    float m_threshold;
    bool m_remove_outliers;

    torch::Tensor detect_outliers(const torch::Tensor& data) {
        switch (m_method) {
            case Method::Z_SCORE:
                return detect_zscore_outliers(data);
                
            case Method::IQR:
                return detect_iqr_outliers(data);
                
            case Method::PERCENTILE:
                return detect_percentile_outliers(data);
        }
        return torch::zeros_like(data, torch::kBool);
    }

    torch::Tensor detect_zscore_outliers(const torch::Tensor& data) {
        torch::Tensor mean_val = Spectre::nanmean(data, 0, true);
        torch::Tensor std_val = Spectre::nanstd(data, 0, true);
        torch::Tensor zscore = torch::abs((data - mean_val) / (std_val + 1e-8f));
        return zscore > m_threshold;
    }

    torch::Tensor detect_iqr_outliers(const torch::Tensor& data) {
        torch::Tensor q25 = torch::quantile(data, 0.25f, 0, true);
        torch::Tensor q75 = torch::quantile(data, 0.75f, 0, true);
        torch::Tensor iqr = q75 - q25;
        torch::Tensor lower_bound = q25 - m_threshold * iqr;
        torch::Tensor upper_bound = q75 + m_threshold * iqr;
        return (data < lower_bound) | (data > upper_bound);
    }

    torch::Tensor detect_percentile_outliers(const torch::Tensor& data) {
        float lower_pct = (100.0f - m_threshold) / 200.0f;  // Convert threshold to percentile
        float upper_pct = 1.0f - lower_pct;
        torch::Tensor lower_bound = torch::quantile(data, lower_pct, 0, true);
        torch::Tensor upper_bound = torch::quantile(data, upper_pct, 0, true);
        return (data < lower_bound) | (data > upper_bound);
    }
};

// DataQualityFactor: Assess data quality metrics
class DataQualityFactor : public CustomFactor {
public:
    enum class Metric {
        MISSING_RATIO,      // Ratio of missing values
        ZERO_RATIO,         // Ratio of zero values
        DUPLICATE_RATIO,    // Ratio of duplicate consecutive values
        VOLATILITY          // Data volatility measure
    };

    DataQualityFactor(const std::shared_ptr<BaseFactor>& factor, Metric metric = Metric::MISSING_RATIO, int window = 20)
        : CustomFactor(window, {factor}), m_metric(metric), m_window(window) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("DataQualityFactor expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        switch (m_metric) {
            case Metric::MISSING_RATIO:
                return calculate_missing_ratio(data);
                
            case Metric::ZERO_RATIO:
                return calculate_zero_ratio(data);
                
            case Metric::DUPLICATE_RATIO:
                return calculate_duplicate_ratio(data);
                
            case Metric::VOLATILITY:
                return calculate_volatility(data);
        }
        
        return torch::zeros({data.size(0), data.size(1)}, data.options());
    }

private:
    Metric m_metric;
    int m_window;

    torch::Tensor calculate_missing_ratio(const torch::Tensor& data) {
        if (data.dim() == 3) {
            torch::Tensor missing_count = data.isnan().sum(2).to(torch::kFloat32);
            return missing_count / static_cast<float>(m_window);
        } else {
            // For 2D data, calculate rolling missing ratio
            torch::Tensor missing_mask = data.isnan().to(torch::kFloat32);
            return rolling_mean(missing_mask, m_window);
        }
    }

    torch::Tensor calculate_zero_ratio(const torch::Tensor& data) {
        if (data.dim() == 3) {
            torch::Tensor zero_count = (data == 0.0f).sum(2).to(torch::kFloat32);
            return zero_count / static_cast<float>(m_window);
        } else {
            torch::Tensor zero_mask = (data == 0.0f).to(torch::kFloat32);
            return rolling_mean(zero_mask, m_window);
        }
    }

    torch::Tensor calculate_duplicate_ratio(const torch::Tensor& data) {
        if (data.dim() == 3) {
            torch::Tensor diff = data.diff(1, 2);
            torch::Tensor duplicate_count = (diff == 0.0f).sum(2).to(torch::kFloat32);
            return duplicate_count / static_cast<float>(m_window - 1);
        } else {
            torch::Tensor diff = data.diff(1, 1);
            torch::Tensor duplicate_mask = (diff == 0.0f).to(torch::kFloat32);
            return rolling_mean(duplicate_mask, m_window);
        }
    }

    torch::Tensor calculate_volatility(const torch::Tensor& data) {
        if (data.dim() == 3) {
            return Spectre::nanstd(data, 2, false);
        } else {
            return rolling_std(data, m_window);
        }
    }

    torch::Tensor rolling_mean(const torch::Tensor& data, int window) {
        // Simple rolling mean implementation
        torch::Tensor padded = torch::cat({torch::full({data.size(0), window - 1}, std::numeric_limits<float>::quiet_NaN(), data.options()), data}, 1);
        torch::Tensor unfolded = padded.unfold(1, window, 1);
        return Spectre::nanmean(unfolded, 2, false);
    }

    torch::Tensor rolling_std(const torch::Tensor& data, int window) {
        // Simple rolling std implementation
        torch::Tensor padded = torch::cat({torch::full({data.size(0), window - 1}, std::numeric_limits<float>::quiet_NaN(), data.options()), data}, 1);
        torch::Tensor unfolded = padded.unfold(1, window, 1);
        return Spectre::nanstd(unfolded, 2, false);
    }
};

} // namespace Spectre
