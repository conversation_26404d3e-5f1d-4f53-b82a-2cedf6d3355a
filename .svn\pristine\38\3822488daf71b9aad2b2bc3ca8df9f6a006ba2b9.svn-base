/*
 * ssdhelper.cpp
 * Implementation for the SSD SDK security primitives
 * <PERSON>
*/
#include "stdafx.h"
#include "./ssdhelper.h"

namespace SSDHelper
{
	BOOL Initialize()
	{
		F_Init lpfnProc;

		bInit = FALSE;

		if( !hMod )	hMod = LoadLibrary(".\\ssdl.dll");

		if(hMod == nullptr)
		{
			//PMSG_ERROR() << "load ssd dll failed.";
			return FALSE;
		}
		bInit = TRUE;

		lpfnProc = (F_Init)GetProcAddress(hMod, MAKEINTRESOURCE(29));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();

	}

	BOOL Signature(LPCTSTR lpszFile, const std::string& strPrivKey, const char* message)
	{
		F_Signature lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		//"Signature"
		lpfnProc = (F_Signature)GetProcAddress(hMod, MAKEINTRESOURCE(1));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(lpszFile, strPrivKey, message);
	}

	BOOL VerifyLicense(LPCTSTR lpszFile, const std::string& strPubKey, const char* message)
	{
		F_VerifyLicense lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_VerifyLicense)GetProcAddress(hMod, MAKEINTRESOURCE(2));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(lpszFile, strPubKey, message);
	}

	BOOL EncryptDataToFile(LPCTSTR lpszFile, const std::string& strData)
	{
		F_EncryptDataToFile lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_EncryptDataToFile)GetProcAddress(hMod, MAKEINTRESOURCE(3));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(lpszFile, strData);
	}

	BOOL DecryptFileToData(LPCTSTR lpszFile, LPCTSTR lpszSign, std::string& plain)
	{
		F_DecryptFileToData lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_DecryptFileToData)GetProcAddress(hMod, MAKEINTRESOURCE(4));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(lpszFile, lpszSign, plain);
	}

	BOOL GenerateKeyString(std::string& strPrivKey, std::string& strPubKey)
	{
		F_GenerateKeyString lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GenerateKeyString)GetProcAddress(hMod, MAKEINTRESOURCE(5));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(strPrivKey, strPubKey);
	}

	BOOL GenerateKeyFile(const char *privFilename, const char *pubFilename)
	{
		F_GenerateKeyFile lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GenerateKeyFile)GetProcAddress(hMod, MAKEINTRESOURCE(6));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(privFilename, pubFilename);
	}

	//////////////////////////////////////////////////////////////////////////
	// Hardware Fingerprint API
	//////////////////////////////////////////////////////////////////////////
	BYTE GetBiosHash()
	{
		F_GetBiosHash lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetBiosHash)GetProcAddress(hMod, MAKEINTRESOURCE(7));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();
	}

	BYTE GetProcessorHash()
	{
		F_GetProcessorHash lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetProcessorHash)GetProcAddress(hMod, MAKEINTRESOURCE(8));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();
	}

	BYTE GetMemoryHash()
	{
		F_GetMemoryHash lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetMemoryHash)GetProcAddress(hMod, MAKEINTRESOURCE(9));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();
	}

	BYTE GetDiskDriveHash()
	{
		F_GetDiskDriveHash lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetDiskDriveHash)GetProcAddress(hMod, MAKEINTRESOURCE(10));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();
	}

	BYTE GetNetworkAdapterHash()
	{
		F_GetNetworkAdapterHash lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetNetworkAdapterHash)GetProcAddress(hMod, MAKEINTRESOURCE(11));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();
	}

	std::string GetHdfpHashString()
	{
		F_GetHdfpHashString lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetHdfpHashString)GetProcAddress(hMod, MAKEINTRESOURCE(12));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc();
	}

	//////////////////////////////////////////////////////////////////////////
	// Register Key Encrypt Data Read And Write API
	//////////////////////////////////////////////////////////////////////////
	LONG ReadRegDWORD ( DWORD& dwValue, BOOL bEncrypted/* = FALSE*/ )
	{
		F_ReadRegDWORD lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_ReadRegDWORD)GetProcAddress(hMod, MAKEINTRESOURCE(13));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(dwValue, bEncrypted);
	}

	LONG ReadRegString( std::string& szValue, BOOL bEncrypted/* = FALSE*/ )
	{
		F_ReadRegString lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_ReadRegString)GetProcAddress(hMod, MAKEINTRESOURCE(14));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(szValue, bEncrypted);
	}

	LONG ReadRegSzString( LPTSTR pszValue, DWORD* dwCharCount, BOOL bEncrypted/* = FALSE */)
	{
		F_ReadRegSzString lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_ReadRegSzString)GetProcAddress(hMod, MAKEINTRESOURCE(15));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(pszValue, dwCharCount, bEncrypted);
	}

	LONG ReadRegBinary( BYTE* pcbData, DWORD* dwSize, BOOL bEncrypted/* = FALSE*/ )
	{
		F_ReadRegBinary lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_ReadRegBinary)GetProcAddress(hMod, MAKEINTRESOURCE(16));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(pcbData, dwSize, bEncrypted);
	}

	LONG WriteRegDWORD( DWORD dwValue, BOOL bEncrypt/* = FALSE */)
	{
		F_WriteRegDWORD lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_WriteRegDWORD)GetProcAddress(hMod, MAKEINTRESOURCE(17));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(dwValue, bEncrypt);
	}

	LONG WriteRegString( LPCTSTR pszData, BOOL bEncrypt/* = FALSE*/ )
	{
		F_WriteRegString lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_WriteRegString)GetProcAddress(hMod, MAKEINTRESOURCE(18));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(pszData, bEncrypt);
	}

	LONG WriteRegBinary( const BYTE* pcbData, UINT nSize, BOOL bEncrypt/* = FALSE */)
	{
		F_WriteRegBinary lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_WriteRegBinary)GetProcAddress(hMod, MAKEINTRESOURCE(19));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(pcbData, nSize, bEncrypt);
	}

	BOOL SetRegHKEY( HKEY hKey )
	{
		F_SetRegHKEY lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_SetRegHKEY)GetProcAddress(hMod, MAKEINTRESOURCE(20));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(hKey);
	}

	BOOL SetRegValueName( LPCTSTR pszValueName )
	{
		F_SetRegValueName lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_SetRegValueName)GetProcAddress(hMod, MAKEINTRESOURCE(21));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(pszValueName);
	}

	BOOL SetRegSubKey( LPCTSTR pszSubKey )
	{
		F_SetRegSubKey lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_SetRegSubKey)GetProcAddress(hMod, MAKEINTRESOURCE(22));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(pszSubKey);
	}


	//////////////////////////////////////////////////////////////////////////
	// AES Encrypt Product Key API
	//////////////////////////////////////////////////////////////////////////

	void SetFeatures(DWORD dwFeatures)
	{
		F_SetFeatures lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return;

		lpfnProc = (F_SetFeatures)GetProcAddress(hMod, MAKEINTRESOURCE(23));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return;
		}

		return lpfnProc(dwFeatures);
	}

	void SetFingerprint(const std::string& sFingerprint)
	{
		F_SetFingerprint lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return;

		lpfnProc = (F_SetFingerprint)GetProcAddress(hMod, MAKEINTRESOURCE(24));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return;
		}

		return lpfnProc(sFingerprint);
	}

	std::string GetProductKey()
	{
		F_GetProductKey lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return "";

		lpfnProc = (F_GetProductKey)GetProcAddress(hMod, MAKEINTRESOURCE(25));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return "";
		}

		return lpfnProc();
	}

	std::string GetRegistKey()
	{
		F_GetRegistKey lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_GetRegistKey)GetProcAddress(hMod, MAKEINTRESOURCE(26));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return "";
		}

		return lpfnProc();
	}

	int VerifyProductKey(const std::string& sProductKey, const std::string& sHdfp, unsigned long& lLastUsedDay)
	{
		F_VerifyProductKey lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return 4;

		lpfnProc = (F_VerifyProductKey)GetProcAddress(hMod, MAKEINTRESOURCE(27));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return 4;
		}

		return lpfnProc(sProductKey, sHdfp, lLastUsedDay);
	}

	BOOL VerifyRegistKey(const std::string& sRegistKey, DWORD& dwFeatures, std::string& sHdfp)
	{
		F_VerifyRegistKey lpfnProc;

		if( !bInit ) Initialize();

		if( !bInit ) return FALSE;

		lpfnProc = (F_VerifyRegistKey)GetProcAddress(hMod, MAKEINTRESOURCE(28));
		if (!lpfnProc )
		{
			//PMSG_ERROR() << "don't get function address.";
			return FALSE;
		}

		return lpfnProc(sRegistKey, dwFeatures, sHdfp);

	}

}