

{
   "barsize" : 6,
   "expr0" : "(entry_signal(td_long,ent_lrs_fast) or entry_signal(td_long,ent_lrs_midd) or entry_signal(td_long, ent_hyo_over)) and  ATR_PREV/CLOSE>0.0025 and trend_level(fd_main,tl_stddev)>=2 and TL_FAST>0",
   "expr1" : "(trend_level(fd_main,tl_stddev)<=2 and stop_profit(td_long, sp_trace)) or (trend_level(fd_main,tl_stddev)>2 and stop_profit(td_long,sp_maxdown))",
   "expr2" : "(trend_level(fd_main,tl_stddev)<=2 and exit_signal(td_long, ext_lrs_fast)) or (trend_level(fd_main,tl_stddev)>2 and exit_signal(td_long, ext_hyo_tenkan))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00180403222144000",
   "name" : "FUT-AW-R-LONG",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "(entry_signal(td_short,ent_lrs_fast) or entry_signal(td_short,ent_lrs_midd) or entry_signal(td_short, ent_hyo_over)) and  ATR_PREV/CLOSE>0.0025 and trend_level(fd_main,tl_stddev)>=2 and TL_FAST<0",
   "expr1" : "(trend_level(fd_main,tl_stddev)<=2 and stop_profit(td_short, sp_trace)) or (trend_level(fd_main,tl_stddev)>2 and stop_profit(td_short,sp_maxdown))",
   "expr2" : "(trend_level(fd_main,tl_stddev)<=2 and exit_signal(td_short, ext_lrs_fast)) or (trend_level(fd_main,tl_stddev)>2 and exit_signal(td_short, ext_hyo_tenkan))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00180403222300001",
   "name" : "FUT-AW-R-SHORT",
   "remark" : "",
   "used" : "策略"
}

















Version 8
{
   "barsize" : 6,
   "expr0" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_FAST>0 and BAND_GRADIENT_PREV<BAND_GRADIENT and ATR_PREV/CLOSE>0.0027",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST<0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>1*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST<LR_SLOPE_MIDD)",
   "expr2" : "(COST_X_ATR>1 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or TL_FAST<0)) or (COST_X_ATR>0 and BAND_GRADIENT<BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV>BAND_GRADIENT and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or (COST_X_ATR<0 and BAND_GRADIENT<0.5*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV>BAND_GRADIENT and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST)",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132623000",
   "name" : "FUT-LRT-LONG-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and TL_FAST<0 and BAND_GRADIENT_PREV>BAND_GRADIENT and ATR_PREV/CLOSE>0.0027",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST>0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>1*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST>LR_SLOPE_MIDD)",
   "expr2" : "(COST_X_ATR>1 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or TL_FAST>0)) or (COST_X_ATR>0 and BAND_GRADIENT>-1*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV<BAND_GRADIENT and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or (COST_X_ATR<0 and BAND_GRADIENT>-0.5*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV<BAND_GRADIENT and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST)",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132657001",
   "name" : "FUT-LRT-SHORT-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 7,
   "expr0" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_FAST>0 and BAND_GRADIENT_PREV<BAND_GRADIENT and ATR_PREV/CLOSE>0.02",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST<0) or (COST_X_ATR>0.4 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or  (COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6 and LR_SLOPE_FAST<LR_SLOPE_MIDD)",
   "expr2" : "(COST_X_ATR>0.2 and ((TL_FAST<TL_SLOW and TL_FAST<0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or TL_FAST<0)) or (COST_X_ATR>0 and BAND_GRADIENT<BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV>BAND_GRADIENT and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or (COST_X_ATR<0 and BAND_GRADIENT<0.5*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV>BAND_GRADIENT and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST)",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "06160929115659001",
   "name" : "FUT-LRT-LONG-D",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 7,
   "expr0" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and TL_FAST<0 and BAND_GRADIENT_PREV>BAND_GRADIENT and ATR_PREV/CLOSE>0.02",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST>0) or (COST_X_ATR>0.4 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6 and LR_SLOPE_FAST>LR_SLOPE_MIDD)",
   "expr2" : "(COST_X_ATR>0.2 and ((TL_FAST>TL_SLOW and TL_FAST>-0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or TL_FAST>0)) or (COST_X_ATR>0 and BAND_GRADIENT>-1*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV<BAND_GRADIENT and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or (COST_X_ATR<0 and BAND_GRADIENT>-0.5*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV<BAND_GRADIENT and LR_SLOPE_FAST>LR_SLOPE_MIDD)",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "06160929115819002",
   "name" : "FUT-LRT-SHORT-D",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_UP(TL_FAST_PREV, TL_FAST,0,0) and TL_SLOW<0 and ATR_PREV/CLOSE>0.0025",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST<0) or (COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6)",
   "expr2" : "(COST_X_ATR>0 and TL_FAST_PREV>TL_FAST and TL_FAST<TL_THRESHOLD) or (COST_X_ATR<0 and TL_FAST<TL_THRESHOLD and TL_FAST_PREV>TL_FAST and LR_SLOPE_FAST<0 and NEW<SQUEEZE_KC_DWL)",
   "expr3" : "",
   "expr4" : "",
   "group" : 16,
   "id" : "00171122113038001",
   "name" : "FUT-ARC-LONG-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_DOWN(TL_FAST_PREV,TL_FAST,0,0) and TL_SLOW>0 and ATR_PREV/CLOSE>0.0025",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST>0) or (COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6)",
   "expr2" : "(COST_X_ATR>0 and TL_FAST_PREV<TL_FAST and TL_FAST>-1*TL_THRESHOLD) or (COST_X_ATR<0 and TL_FAST>-1*TL_THRESHOLD and TL_FAST_PREV<TL_FAST and LR_SLOPE_FAST>0 and NEW>SQUEEZE_KC_UPL)",
   "expr3" : "",
   "expr4" : "",
   "group" : 16,
   "id" : "00171122122411002",
   "name" : "FUT-ARC-SHORT-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_FAST>0 and BAND_GRADIENT_PREV<BAND_GRADIENT and ATR_PREV/CLOSE>0.036",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST<0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>1*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST<LR_SLOPE_MIDD)",
   "expr2" : "(COST_X_ATR>1 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or TL_FAST<0)) or (COST_X_ATR>0 and BAND_GRADIENT<BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV>BAND_GRADIENT and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or (COST_X_ATR<0 and BAND_GRADIENT<0.5*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV>BAND_GRADIENT and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST)",
   "expr3" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
   "expr4" : "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
   "group" : 47,
   "id" : "06160814223748002",
   "name" : "STK-LRT-R",
   "remark" : "",
   "used" : "策略"
}



Version 7

LRT-RANGE
{
   "barsize" : 6,
   "expr0" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_FAST>0 and BAND_GRADIENT_PREV<BAND_GRADIENT and ATR/NEW>0.006",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST<0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>0.6*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) ",
   "expr2" : "COST_X_ATR>0.8 and ((TL_FAST<TL_SLOW and TL_FAST<0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or TL_FAST<0)",
   "expr3" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
   "expr4" : "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
   "group" : 80,
   "id" : "00170524181437000",
   "name" : "FUT-LRT-LONG-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and TL_FAST<0 and BAND_GRADIENT_PREV>BAND_GRADIENT and ATR/NEW>0.006",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST>0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>0.6*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST)",
   "expr2" : "COST_X_ATR>0.8 and ((TL_FAST>TL_SLOW and TL_FAST>-0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or TL_FAST>0)",
   "expr3" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
   "expr4" : "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
   "group" : 80,
   "id" : "00170524181457001",
   "name" : "FUT-LRT-SHORT-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_UP(TL_FAST_PREV,TL_FAST,0,0) and NEW/SHORT_TERM_LOW<1.1 and TL_SLOW<0 and ATR/NEW>0.005",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST<-0.2*TL_THRESHOLD) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>0.6*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST)",
   "expr2" : "COST_X_ATR>1 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD) or (TL_FAST<0))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132806002",
   "name" : "FUT-SW-LONG-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_DOWN(TL_FAST_PREV,TL_FAST,0,0) and NEW/SHORT_TERM_HIGH>0.9 and TL_SLOW>0 and ATR/NEW>0.005",
   "expr1" : "(COST_X_ATR<-2*STDDEV_ATR and TL_FAST>0.2*TL_THRESHOLD) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>0.6*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST)",
   "expr2" : "COST_X_ATR>1 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD) or (TL_FAST>0))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132826003",
   "name" : "FUT-SW-SHORT-R",
   "remark" : "",
   "used" : "策略"
}


Version 6
LRT
(COST_X_ATR<-1.5*STDDEV_THRESHOLD/ATR and TL_FAST<0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_THRESHOLD/ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_THRESHOLD/ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2*STDDEV_THRESHOLD/ATR)

(COST_X_ATR<-1.5*STDDEV_THRESHOLD/ATR and TL_FAST>0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_THRESHOLD/ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_THRESHOLD/ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2*STDDEV_THRESHOLD/ATR)

SW
(COST_X_ATR<-1.5*STDDEV_THRESHOLD/ATR and TL_FAST<-0.5*TL_THRESHOLD) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_THRESHOLD/ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_THRESHOLD/ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2*STDDEV_THRESHOLD/ATR)

(COST_X_ATR<-1.5*STDDEV_THRESHOLD/ATR and TL_FAST>0.5*TL_THRESHOLD) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_THRESHOLD/ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_THRESHOLD/ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2*STDDEV_THRESHOLD/ATR)

Version 5
(COST_X_ATR<-1*STDDEV_THRESHOLD/ATR and TL_FAST<0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>0.5*STDDEV_THRESHOLD/ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_THRESHOLD/ATR) and LR_SLOPE_MIDD<0.5*LR_SLOPE_FAST_THRESHOLD and LR_SLOPE_FAST<LR_SLOPE_FAST_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2*STDDEV_THRESHOLD/ATR)

(COST_X_ATR<-1*STDDEV_THRESHOLD/ATR and TL_FAST>0) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>0.5*STDDEV_THRESHOLD/ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_THRESHOLD/ATR) and LR_SLOPE_MIDD>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST>-1*LR_SLOPE_FAST_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2*STDDEV_THRESHOLD/ATR)


Version 4
# 区分BarSize


Version 3
# 入市与止盈的条件要相互呼应配合
# LRT策略定位为：高频短线
# LRT stop long
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.5*MAXDOWN_ATR and LONG_LR_SLOPE_MIDD<LONG_LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2.0)

# LRT stop short
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.5*MAXDOWN_ATR and LONG_LR_SLOPE_MIDD>-1*LONG_LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2.0)
 
# SW策略定位为：低频长线
# SW stop long
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LONG_LR_SLOPE_MIDD<LONG_LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2.0)

# SW stop short
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LONG_LR_SLOPE_MIDD>-1*LONG_LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2.0)
 


Version 2
# stop long
(COST_X_ATR<-1.2 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2.0)

# stop short
(COST_X_ATR<-1.2 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2.0)
 
Version 1
# stop long
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.333*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)

# stop short
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.333*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)

Version 0
# stop long
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>0.3*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)

#stop short
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>0.3*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)


{
   "barsize" : 7,
   "expr0" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_FAST>0",
   "expr1" : "(COST_X_ATR<-1.2 and TL_FAST<0) or (COST_X_ATR>0.8 and COST_X_ATR+MAXDOWN_ATR>0.5*STDDEV_THRESHOLD/ATR and COST_X_ATR<2*MAXDOWN_ATR and LR_SLOPE_MIDD<LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2.0)",
   "expr2" : "COST_X_ATR>0.2 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or TL_FAST<0)",
   "expr3" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
   "expr4" : "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
   "group" : 80,
   "id" : "06160929115659001",
   "name" : "FUT-LRT-LONG-D",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 7,
   "expr0" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and TL_FAST<0",
   "expr1" : "(COST_X_ATR<-1.2 and TL_FAST>0) or (COST_X_ATR>0.8 and COST_X_ATR+MAXDOWN_ATR>0.5*STDDEV_THRESHOLD/ATR and COST_X_ATR<2*MAXDOWN_ATR and LR_SLOPE_MIDD>-1*LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2.0)",
   "expr2" : "COST_X_ATR>0.2 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or TL_FAST>0)",
   "expr3" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
   "expr4" : "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
   "group" : 80,
   "id" : "06160929115819002",
   "name" : "FUT-LRT-SHORT-D",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_FAST>0",
   "expr1" : "(COST_X_ATR<-1.5 and TL_FAST<0) or (COST_X_ATR>0.8 and COST_X_ATR+MAXDOWN_ATR>0.8*STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LR_SLOPE_MIDD<LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
   "expr2" : "COST_X_ATR>0.8 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV>LR_SLOPE_FAST) or TL_FAST<0)",
   "expr3" : "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
   "expr4" : "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
   "group" : 80,
   "id" : "00170620132623000",
   "name" : "FUT-LRT-LONG-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and TL_FAST<0",
   "expr1" : "(COST_X_ATR<-1.5 and TL_FAST>0) or (COST_X_ATR>0.8 and COST_X_ATR+MAXDOWN_ATR>0.8*STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LR_SLOPE_MIDD>-1*LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
   "expr2" : "COST_X_ATR>0.8 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD and LR_SLOPE_FAST_PREV<LR_SLOPE_FAST) or TL_FAST>0)",
   "expr3" : "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
   "expr4" : "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
   "group" : 80,
   "id" : "00170620132657001",
   "name" : "FUT-LRT-SHORT-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 7,
   "expr0" : "CROSS_UP(TL_FAST_PREV,TL_FAST,0,0)",
   "expr1" : "(COST_X_ATR<-1.5 and TL_FAST<0) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LR_SLOPE_MIDD<LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2.0)",
   "expr2" : "COST_X_ATR>0.382 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD) or (TL_FAST<0))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170116092122023",
   "name" : "FUT-SW-LONG-D",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 7,
   "expr0" : "CROSS_DOWN(TL_FAST_PREV,TL_FAST,0,0)",
   "expr1" : "(COST_X_ATR<-1.5 and TL_FAST>0) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LR_SLOPE_MIDD>-1*LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2.0)",
   "expr2" : "COST_X_ATR>0.382 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD) or (TL_FAST>0))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170116092209024",
   "name" : "FUT-SW-SHORT-D",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_UP(TL_FAST_PREV,TL_FAST,0,0)",
   "expr1" : "(COST_X_ATR<-2.0 and TL_FAST<0) or (COST_X_ATR>1 and COST_X_ATR+MAXDOWN_ATR>STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LR_SLOPE_MIDD<LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
   "expr2" : "COST_X_ATR>0.382 and ((TL_FAST_PREV>TL_FAST and TL_FAST<0.618*TL_THRESHOLD) or (TL_FAST<0))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132806002",
   "name" : "FUT-SW-LONG-R",
   "remark" : "",
   "used" : "策略"
}

{
   "barsize" : 6,
   "expr0" : "CROSS_DOWN(TL_FAST_PREV,TL_FAST,0,0)",
   "expr1" : "(COST_X_ATR<-2.5 and TL_FAST>0) or (COST_X_ATR>1 and COST_X_ATR+MAXDOWN_ATR>STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and LR_SLOPE_MIDD>-1*LR_SLOPE_SLOW_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
   "expr2" : "COST_X_ATR>0.382 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD) or (TL_FAST>0))",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132826003",
   "name" : "FUT-SW-SHORT-R",
   "remark" : "",
   "used" : "策略"
}


///

{
  "barsize": 6,
  "expr0": "CROSS_UP(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,LONG_LR_SLOPE_SLOW_THRESHOLD,LONG_LR_SLOPE_SLOW_THRESHOLD)",
  "expr1": "(COST_X_ATR<-1.0 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.5*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.3*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.5*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-2.0)",
  "expr2": "COST_X_ATR>0.2 and ((LONG_TL_FAST_PREV>LONG_TL_FAST and LONG_TL_FAST<0.618*LONG_TL_THRESHOLD and LONG_LR_SLOPE_FAST_PREV>LONG_LR_SLOPE_FAST) or LONG_TL_FAST<0)",
  "expr3": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
  "expr4": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
  "group": 80,
  "id": "00170519205454000",
  "name": "FUT-BLK-TL-LRT-LONG",
  "remark": "",
  "used": "策略"
}

{
  "barsize": 6,
  "expr0": "CROSS_DOWN(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,-1*LONG_LR_SLOPE_SLOW_THRESHOLD,-1*LONG_LR_SLOPE_SLOW_THRESHOLD)",
  "expr1": "(COST_X_ATR<-1.0 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.2*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.2 and COST_X_ATR+MAXDOWN_ATR>0.3*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<1.5*MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-2.0)",
  "expr2": "COST_X_ATR>0.2 and ((LONG_TL_FAST_PREV<LONG_TL_FAST and LONG_TL_FAST>-0.618*LONG_TL_THRESHOLD and LONG_LR_SLOPE_FAST_PREV<LONG_LR_SLOPE_FAST) or LONG_TL_FAST>0)",
  "expr3": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
  "expr4": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
  "group": 80,
  "id": "00170519205626001",
  "name": "FUT-BLK-TL-LRT-SHORT",
  "remark": "",
  "used": "策略"
}


{
  "barsize": 6,
  "expr0": "CROSS_UP(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,LONG_LR_SLOPE_SLOW_THRESHOLD,LONG_LR_SLOPE_SLOW_THRESHOLD) and  LONG_TL_FAST>0",
  "expr1": "(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.333*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
  "expr2": "COST_X_ATR>0.2 and ((LONG_TL_FAST_PREV>LONG_TL_FAST and LONG_TL_FAST<0.618*LONG_TL_THRESHOLD and LONG_LR_SLOPE_FAST_PREV>LONG_LR_SLOPE_FAST) or LONG_TL_FAST<0)",
  "expr3": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
  "expr4": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
  "group": 80,
  "id": "06160929115659001",
  "name": "FUT-TL-LRT-LONG",
  "remark": "",
  "used": "策略"
}

{
  "barsize": 6,
  "expr0": "CROSS_DOWN(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,-1*LONG_LR_SLOPE_SLOW_THRESHOLD,-1*LONG_LR_SLOPE_SLOW_THRESHOLD) and LONG_TL_FAST<0",
  "expr1": "(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.333*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
  "expr2": "COST_X_ATR>0.2 and ((LONG_TL_FAST_PREV<LONG_TL_FAST and LONG_TL_FAST>-0.618*LONG_TL_THRESHOLD and LONG_LR_SLOPE_FAST_PREV<LONG_LR_SLOPE_FAST) or LONG_TL_FAST>0)",
  "expr3": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
  "expr4": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
  "group": 80,
  "id": "06160929115819002",
  "name": "FUT-TL-LRT-SHORT",
  "remark": "",
  "used": "策略"
}


{
    "barsize": 6,
    "expr0": "CROSS_UP(LONG_TL_FAST_PREV,LONG_TL_FAST,0,0)",
    "expr1": "(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.333*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
    "expr2": "COST_X_ATR>0.382 and ((LONG_TL_FAST_PREV>LONG_TL_FAST and LONG_TL_FAST<0.618*LONG_TL_THRESHOLD) or (LONG_TL_FAST<0))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "00170116092122023",
    "name": "FUT-TL-SW-LONG",
    "remark": "",
    "used": "策略"
}

{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LONG_TL_FAST_PREV,LONG_TL_FAST,0,0)",
    "expr1": "(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)>SLOW_ZLQH_TV_THRESHOLD) or (COST_X_ATR>0.333*LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR and abs(SLOW_ZLQH_TV)<SLOW_ZLQH_TV_THRESHOLD) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
    "expr2": "COST_X_ATR>0.382 and ((LONG_TL_FAST_PREV<LONG_TL_FAST and LONG_TL_FAST>-0.618*LONG_TL_THRESHOLD) or (LONG_TL_FAST>0))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "00170116092209024",
    "name": "FUT-TL-SW-SHORT",
    "remark": "",
    "used": "策略"
}





// 基于市场的判断-》长周期-》短周期
// 基于个体的判断-》长周期-》短周期

{
    "barsize": 6,
    "expr0": "CROSS_UP(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,LONG_LR_SLOPE_SLOW_THRESHOLD,LONG_LR_SLOPE_SLOW_THRESHOLD) and  LONG_TL_FAST>0",
    "expr1": "(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
    "expr2": "LONG_TL_FAST_PREV>LONG_TL_FAST and LONG_TL_FAST<LONG_TL_THRESHOLD",
    "expr3": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
    "expr4": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
    "group": 80,
    "id": "06160929115659001",
    "name": "FUT-2-BK-LRT-LONG",
    "remark": "",
    "used": "策略"
}

{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,-1*LONG_LR_SLOPE_SLOW_THRESHOLD,-1*LONG_LR_SLOPE_SLOW_THRESHOLD) and LONG_TL_FAST<0",
    "expr1": "(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)",
    "expr2": "LONG_TL_FAST_PREV<LONG_TL_FAST and LONG_TL_FAST>-1*LONG_TL_THRESHOLD",
    "expr3": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
    "expr4": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
    "group": 80,
    "id": "06160929115819002",
    "name": "FUT-2-BK-LRT-SHORT",
    "remark": "",
    "used": "策略"
}








//
// 敏感信号
//

// 通用止损止盈

(SHORT_TERM_HIGH - SHORT_TERM_LOW)/ATR

//Long
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV>LONG_TL_FAST) or (COST_X_ATR>LONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR) or (SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and COST_X_ATR<-5.0)

//Short
(COST_X_ATR<-1.5 and LONG_TL_FAST_PREV<LONG_TL_FAST) or (COST_X_ATR>lONG_STDDEV_THRESHOLD/ATR and COST_X_ATR<MAXDOWN_ATR) or (SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and COST_X_ATR<-5.0)


// 入市

// 离市

//
// 顿感信号
//
// 入市
CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TREND_VALUE>FAST_ZLQH_TV_THRESHOLD and LONG_TREND_VALUE>SLOW_ZLQH_TV_THRESHOLD and NEW<LONG_SQUEEZE_KC_UPL and LONG_TL_FAST_PREV<LONG_TL_FAST

// 离市
SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV
TL_SLOW_PREV>TL_SLOW
LR_SLOPE_MIDD<0.618*LR_SLOPE_SLOW_THRESHOLD


// 信号敏感区
SLOW_ZLQH_TV > 2.0*SLOW_ZLQH_TV_THRESHOLD or SLOW_ZLQH_TV < SLOW_ZLQH_TV_THRESHOLD

// 信号顿感区
SLOW_ZLQH_TV < 2.0*SLOW_ZLQH_TV_THRESHOLD and SLOW_ZLQH_TV > SLOW_ZLQH_TV_THRESHOLD


// BK-LRT长线（阵地战）
// Long
{
    "barsize": 6,
    "expr0": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TREND_VALUE>FAST_ZLQH_TV_THRESHOLD and LONG_TREND_VALUE>0.8*SLOW_ZLQH_TV_THRESHOLD and NEW<LONG_SQUEEZE_KC_UPL and LONG_TL_FAST_PREV<LONG_TL_FAST and SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV and SLOW_ZLQH_TV>0.5*SLOW_ZLQH_TV_THRESHOLD",
    "expr1": "COST_X_ATR<-3*ATR and LONG_TREND_VALUE < 1",
    "expr2": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LONG_TL_FAST_PREV>LONG_TL_FAST and ((LONG_TL_FAST<0.618*LONG_TL_THRESHOLD) or (LONG_TL_SLOW>1.5*LONG_TL_SLOW and LONG_TL_SLOW_PREV>LONG_TL_SLOW) or ((SLOW_ZLQH_TV>2.0*SLOW_ZLQH_TV_THRESHOLD or SLOW_ZLQH_TV<SLOW_ZLQH_TV_THRESHOLD) and SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV))",
    "expr3": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>0.8 and NEW < LONG_SQUEEZE_KC_UPL",
    "expr4": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<0.8",
    "group": 80,
    "id": "00161014154827000",
    "name": "FUT-2-BK-LRT-LONG",
    "remark": "",
    "used": "策略"
}

// Short
{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and TREND_VALUE<-1*FAST_ZLQH_TV_THRESHOLD and LONG_TREND_VALUE<-0.8*SLOW_ZLQH_TV_THRESHOLD and NEW>LONG_SQUEEZE_KC_DWL and LONG_TL_FAST_PREV>LONG_TL_FAST and SLOW_ZLQH_TV_PREV>SLOW_ZLQH_TV and SLOW_ZLQH_TV<-0.5*SLOW_ZLQH_TV_THRESHOLD",
    "expr1": "COST_X_ATR<-3*ATR and LONG_TREND_VALUE > -1",
    "expr2": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LONG_TL_FAST_PREV<LONG_TL_FAST and ((LONG_TL_FAST>-0.618*LONG_TL_THRESHOLD) or (LONG_TL_SLOW<-1.5*LONG_TL_SLOW and LONG_TL_SLOW_PREV<LONG_TL_SLOW) or ((SLOW_ZLQH_TV<-2.0*SLOW_ZLQH_TV_THRESHOLD or SLOW_ZLQH_TV>-1*SLOW_ZLQH_TV_THRESHOLD) and SLOW_ZLQH_TV_PREV<SLOW_ZLQH_TV))",
    "expr3": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD) and MAXDOWN_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV<-0.8 and NEW > LONG_SQUEEZE_KC_DWL",
    "expr4": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and COST_X_ATR>5 and LONG_TREND_VALUE-LONG_TREND_VALUE_PREV>-0.8",
    "group": 80,
    "id": "08160928141702001",
    "name": "FUT-2-BK-LRT-SHORT",
    "remark": "",
    "used": "策略"
}

// BK-LRT短线（游击战）
// Long
{
    "barsize": 6,
    "expr0": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_SLOW_PREV<TL_SLOW and TL_FAST > 0.618 * TL_THRESHOLD and  STDDEV_THRESHOLD/NEW>0.003 and BAND_GRADIENT > BAND_GRADIENT_THRESHOLD and TL_SLOW>0 and LONG_TL_SLOW_PREV<LONG_TL_SLOW and (LONG_TL_SLOW>0 or LONG_TL_SLOW<-1.618*LONG_TL_THRESHOLD) and FAST_ZLQH_TV>0.618*FAST_ZLQH_TV_THRESHOLD and FAST_ZLQH_TV_PREV<FAST_ZLQH_TV",
    "expr1": "COST_X_ATR > 1.618 and MAXDOWN_ATR > 0.618",
    "expr2": "(COST_X_ATR > 3 and TL_SLOW_PREV>TL_SLOW and TL_SLOW<TL_THRESHOLD and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0) or (COST_X_ATR<3 and TL_SLOW_PREV>TL_SLOW and TL_SLOW<0 and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0) or (COST_X_ATR>1 and SQUEEZE_GAP>3 and CROSS_DOWN(TYPICAL_PRICE_PREV, TYPICAL_PRICE, SQUEEZE_KC_UPL_PREV, SQUEEZE_KC_UPL)) or FAST_ZLQH_TV_PREV>FAST_ZLQH_TV",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "08160930101827000",
    "name": "FUT-1-BK-LRT-LONG",
    "remark": "",
    "used": "策略"
}

// Short
{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD)  and TL_SLOW_PREV>TL_SLOW and TL_FAST < -0.618 * TL_THRESHOLD and   STDDEV_THRESHOLD/NEW>0.003 and BAND_GRADIENT < -1*BAND_GRADIENT_THRESHOLD and TL_SLOW<0 and LONG_TL_SLOW_PREV>LONG_TL_SLOW and ( LONG_TL_SLOW<0 or LONG_TL_SLOW>1.618*LONG_TL_THRESHOLD) and FAST_ZLQH_TV<-0.618*FAST_ZLQH_TV_THRESHOLD and FAST_ZLQH_TV_PREV>FAST_ZLQH_TV",
    "expr1": "COST_X_ATR > 1.618 and MAXDOWN_ATR > 0.618",
    "expr2": "(COST_X_ATR>3 and TL_SLOW_PREV<TL_SLOW and TL_SLOW>-1*TL_THRESHOLD and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0) or (COST_X_ATR<3 and TL_SLOW_PREV<TL_SLOW and TL_SLOW>0 and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0) or (COST_X_ATR>1 and SQUEEZE_GAP>3 and CROSS_UP(TYPICAL_PRICE_PREV, TYPICAL_PRICE, SQUEEZE_KC_DWL_PREV, SQUEEZE_KC_DWL)) or FAST_ZLQH_TV_PREV<FAST_ZLQH_TV",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "08160930101904001",
    "name": "FUT-1-BK-LRT-SHORT",
    "remark": "",
    "used": "策略"
}


// BK-LRT短-长线（游击战与阵地战结合）
// Long
{
    "barsize": 6,
    "expr0": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_SLOW_PREV<TL_SLOW and TL_FAST > 0.618 * TL_THRESHOLD and STDDEV_FAST > 0.618*STDDEV_THRESHOLD and LONG_TL_SLOW_PREV<LONG_TL_SLOW and LONG_TL_SLOW>0.382*LONG_TL_THRESHOLD",
    "expr1": "",
    "expr2": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0  and ((LONG_TL_FAST_PREV>LONG_TL_FAST and LONG_TL_FAST<0.618*LONG_TL_THRESHOLD) or (LONG_TL_SLOW>1.5*LONG_TL_SLOW and LONG_TL_SLOW_PREV>LONG_TL_SLOW))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "00161014154827000",
    "name": "FUT-2-BK-LRT-LONG",
    "remark": "",
    "used": "策略"
}

// Short
{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD)  and TL_SLOW_PREV>TL_SLOW and TL_FAST < -0.618 * TL_THRESHOLD and STDDEV_FAST > 0.618*STDDEV_THRESHOLD and LONG_TL_SLOW_PREV>LONG_TL_SLOW and LONG_TL_SLOW<-0.382*LONG_TL_THRESHOLD",
    "expr1": "",
    "expr2": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and ((LONG_TL_FAST_PREV<LONG_TL_FAST and LONG_TL_FAST>-0.618*LONG_TL_THRESHOLD) or (LONG_TL_SLOW<-1.5*LONG_TL_SLOW and LONG_TL_SLOW_PREV<LONG_TL_SLOW))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "08160928141702001",
    "name": "FUT-2-BK-LRT-SHORT",
    "remark": "",
    "used": "策略"
}

////////////////////////////////////////////////////////////////////////////////////////
{
    "barsize": 6,
    "expr0": "CROSS_UP(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,LONG_LR_SLOPE_SLOW_THRESHOLD,LONG_LR_SLOPE_SLOW_THRESHOLD) and  TL_SLOW_PREV<TL_SLOW and TL_FAST > 0.618 * TL_THRESHOLD and  LONG_TL_SLOW_PREV<LONG_TL_SLOW and LONG_TL_SLOW>0.382*LONG_TL_THRESHOLD and LONG_TL_SLOW<1.5*LONG_TL_THRESHOLD",
    "expr1": "",
    "expr2": "TL_SLOW_PREV > TL_SLOW and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0  and ((LONG_TL_FAST_PREV>LONG_TL_FAST and LONG_TL_FAST<0.618*LONG_TL_THRESHOLD) or (LONG_TL_SLOW>1.5*LONG_TL_SLOW and LONG_TL_SLOW_PREV>LONG_TL_SLOW))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "08160913133811000",
    "name": "FUT-3-BK-LRT-LONG",
    "remark": "",
    "used": "策略"
}

{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LONG_LR_SLOPE_FAST_PREV,LONG_LR_SLOPE_FAST,-1*LONG_LR_SLOPE_SLOW_THRESHOLD,-1*LONG_LR_SLOPE_SLOW_THRESHOLD) and TL_SLOW_PREV>TL_SLOW and TL_FAST < -0.618 * TL_THRESHOLD and  LONG_TL_SLOW_PREV>LONG_TL_SLOW and LONG_TL_SLOW<-0.382*LONG_TL_THRESHOLD and LONG_TL_SLOW>-1.5*LONG_TL_THRESHOLD",
    "expr1": "",
    "expr2": "TL_SLOW_PREV < TL_SLOW and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0 and ((LONG_TL_FAST_PREV<LONG_TL_FAST and LONG_TL_FAST>-0.618*LONG_TL_THRESHOLD) or (LONG_TL_SLOW<-1.5*LONG_TL_SLOW and LONG_TL_SLOW_PREV<LONG_TL_SLOW))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "08160913133845001",
    "name": "FUT-3-BK-LRT-SHORT",
    "remark": "",
    "used": "策略"
}

///////////////////////////////////////////////////////////////////////////////////////
// EX-BK-LRT
// Long
{
    "barsize": 6,
    "expr0": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,0.6*LR_SLOPE_SLOW_THRESHOLD,0.6*LR_SLOPE_SLOW_THRESHOLD) and  TL_SLOW_PREV<TL_SLOW and TL_FAST > 0.618 * TL_THRESHOLD and TL_SLOW>0",
    "expr1": "",
    "expr2": "(COST_X_ATR > 2 and TL_SLOW_PREV>TL_SLOW and TL_SLOW<TL_THRESHOLD and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0) or (COST_X_ATR<2 and TL_SLOW_PREV>TL_SLOW and TL_SLOW<0 and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0) or (COST_X_ATR>1 and SQUEEZE_GAP>3 and CROSS_DOWN(TYPICAL_PRICE_PREV, TYPICAL_PRICE, SQUEEZE_KC_UPL_PREV, SQUEEZE_KC_UPL))",
    "expr3": "",
    "expr4": "",
    "group": 64,
    "id": "00161011155158001",
    "name": "EX-1-BK-LRT-LONG",
    "remark": "",
    "used": "策略"
}

// Short
{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-0.6*LR_SLOPE_SLOW_THRESHOLD,-0.6*LR_SLOPE_SLOW_THRESHOLD)  and TL_SLOW_PREV>TL_SLOW and TL_FAST < -0.618 * TL_THRESHOLD and TL_SLOW<0",
    "expr1": "",
    "expr2": "(COST_X_ATR>2 and TL_SLOW_PREV<TL_SLOW and TL_SLOW>-1*TL_THRESHOLD and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0) or (COST_X_ATR<2 and TL_SLOW_PREV<TL_SLOW and TL_SLOW>0 and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0) or (COST_X_ATR>1 and SQUEEZE_GAP>3 and CROSS_UP(TYPICAL_PRICE_PREV, TYPICAL_PRICE, SQUEEZE_KC_DWL_PREV, SQUEEZE_KC_DWL))",
    "expr3": "",
    "expr4": "",
    "group": 64,
    "id": "00161011155317002",
    "name": "EX-1-BK-LRT-SHORT",
    "remark": "",
    "used": "策略"
}

//////////////////////////////////////////////////////////////////////////////
// 突袭：出其不意，攻其不备！
// Long
{
    "barsize": 6,
    "expr0": "CROSS_UP(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,LR_SLOPE_SLOW_THRESHOLD,LR_SLOPE_SLOW_THRESHOLD) and  TL_SLOW_PREV<TL_SLOW and TL_FAST > 0.618 * TL_THRESHOLD and  STDDEV_THRESHOLD/NEW>0.003 and TL_SLOW>0 and  and LONG_TL_FAST<-2*LONG_TL_THRESHOLD",
    "expr1": "",
    "expr2": "(COST_X_ATR > 3 and TL_SLOW_PREV>TL_SLOW and TL_SLOW<TL_THRESHOLD and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0) or (COST_X_ATR<3 and TL_SLOW_PREV>TL_SLOW and TL_SLOW<0 and LR_SLOPE_MIDD < 0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST < 0) or (COST_X_ATR>1 and SQUEEZE_GAP>3 and CROSS_DOWN(TYPICAL_PRICE_PREV, TYPICAL_PRICE, SQUEEZE_KC_UPL_PREV, SQUEEZE_KC_UPL))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "00161113224800000",
    "name": "FUT-4-BK-LRT-LONG",
    "remark": "经过一段时间大涨或大跌后，突袭！",
    "used": "策略"
}

//Short
{
    "barsize": 6,
    "expr0": "CROSS_DOWN(LR_SLOPE_FAST_PREV,LR_SLOPE_FAST,-1*LR_SLOPE_SLOW_THRESHOLD,-1*LR_SLOPE_SLOW_THRESHOLD)  and TL_SLOW_PREV>TL_SLOW and TL_FAST < -0.618 * TL_THRESHOLD and   STDDEV_THRESHOLD/NEW>0.003 and TL_SLOW<0 and LONG_TL_FAST>2*LONG_TL_THRESHOLD",
    "expr1": "",
    "expr2": "(COST_X_ATR>3 and TL_SLOW_PREV<TL_SLOW and TL_SLOW>-1*TL_THRESHOLD and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0) or (COST_X_ATR<3 and TL_SLOW_PREV<TL_SLOW and TL_SLOW>0 and LR_SLOPE_MIDD > -0.618*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_FAST > 0) or (COST_X_ATR>1 and SQUEEZE_GAP>3 and CROSS_UP(TYPICAL_PRICE_PREV, TYPICAL_PRICE, SQUEEZE_KC_DWL_PREV, SQUEEZE_KC_DWL))",
    "expr3": "",
    "expr4": "",
    "group": 80,
    "id": "00161113224846001",
    "name": "FUT-4-BK-LRT-SHORT",
    "remark": "经过一段时间大涨或大跌后，突袭！",
    "used": "策略"
}

/////////////////////////////////////////////////////////////////////////////////////////
//Stock
{
    "barsize": 7,
    "expr0": "CROSS_UP(TYPICAL_PRICE_PREV,TYPICAL_PRICE,SQUEEZE_KC_UPL_PREV,SQUEEZE_KC_UPL) and NEW_CHANGE_PERCENT>0  and LONG_MACD_DIFF > 0 and LONG_MACD > 0 and SQUEEZE_GAP > SQUEEZE_GAP_THRESHOLD",
    "expr1": "",
    "expr2": "",
    "expr3": "",
    "expr4": "",
    "group": 15,
    "id": "06160812233035001",
    "name": "BK-KUL-DAY",
    "remark": "",
    "used": "选股"
}

{
    "barsize": 7,
    "expr0": "CROSS_UP(TYPICAL_PRICE_PREV,TYPICAL_PRICE,SQUEEZE_BAND_UPL_PREV,SQUEEZE_BAND_UPL) and SQUEEZE_GAP>0.618",
    "expr1": "",
    "expr2": "CROSS_DOWN(LR_SLOPE_FAST_PREV, LR_SLOPE_FAST, LR_SLOPE_FAST_THRESHOLD, LR_SLOPE_FAST_THRESHOLD)",
    "expr3": "",
    "expr4": "",
    "group": 15,
    "id": "08160920104707000",
    "name": "STK-BK-BUL-DAY",
    "remark": "",
    "used": "策略"
}

