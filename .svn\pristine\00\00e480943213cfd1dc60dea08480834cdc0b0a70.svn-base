﻿// DlgTargetPrice.cpp: 实现文件
//

#include "stdafx.h"
#include "XRobot.h"
#include "afxdialogex.h"
#include "DlgTargetPrice.h"


// DlgTargetPrice 对话框

IMPLEMENT_DYNAMIC(DlgTargetPrice, CDialogEx)

DlgTargetPrice::DlgTargetPrice(int run_mode, double buy_target, double sell_target, double price, CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_TARGET_PRICE, pParent)
	, _RunMode(run_mode)
	, m_dNewTarget(0.0)
	, m_dBuyTarget(buy_target)
	, m_dSellTarget(sell_target)
	, m_dPrice(price)
	, m_nBuySell(0)
	, m_nOperateType(0)
{

}

DlgTargetPrice::~DlgTargetPrice()
{
}

void DlgTargetPrice::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_EDIT_NEW_TARGET, m_dNewTarget);
	DDX_Radio(pDX, IDC_RADIO_OPERATE_TYPE, m_nOperateType);
}


BEGIN_MESSAGE_MAP(DlgTargetPrice, CDialogEx)
	ON_BN_CLICKED(IDC_BUTTON_MODIFY, &DlgTargetPrice::OnBnClickedButtonModify)
	ON_STN_CLICKED(IDC_STATIC_TARGET, &DlgTargetPrice::OnStnClickedStaticTarget)
	ON_STN_CLICKED(IDC_STATIC_TARGET2, &DlgTargetPrice::OnStnClickedStaticTarget2)
END_MESSAGE_MAP()


// DlgTargetPrice 消息处理程序

BOOL DlgTargetPrice::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	if (_RunMode == 0) {
		GetDlgItem(IDC_RADIO_OPERATE_TYPE)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_RADIO_OPERATE_TYPE2)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_RADIO_OPERATE_TYPE3)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_EDIT_NEW_TARGET)->ShowWindow(SW_SHOW);
	}
	else if (_RunMode == 1) {
		GetDlgItem(IDC_RADIO_OPERATE_TYPE)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_RADIO_OPERATE_TYPE2)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_RADIO_OPERATE_TYPE3)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_EDIT_NEW_TARGET)->ShowWindow(SW_HIDE);
	}

	GetDlgItem(IDC_STATIC_PRICE)->SetWindowText(_T(fmt::format("{:.2f}", m_dPrice).c_str()));
	if (m_dBuyTarget > 0.0) {
		m_nBuySell = 0;
		GetDlgItem(IDC_STATIC_TARGET)->SetWindowText(_T(fmt::format("{:.2f}", m_dBuyTarget).c_str()));
		GetDlgItem(IDC_STATIC_PERCENT)->SetWindowText(_T(fmt::format("{:.2f}%", (m_dBuyTarget - m_dPrice) / m_dPrice * 100.).c_str()));
	}
	if (m_dSellTarget > 0.0) {
		m_nBuySell = 1;
		GetDlgItem(IDC_STATIC_TARGET2)->SetWindowText(_T(fmt::format("{:.2f}", m_dSellTarget).c_str()));
		GetDlgItem(IDC_STATIC_PERCENT2)->SetWindowText(_T(fmt::format("{:.2f}%", (m_dSellTarget - m_dPrice) / m_dPrice * 100.).c_str()));
	}
	UpdateData(FALSE);
	return TRUE;
}


void DlgTargetPrice::OnStnClickedStaticTarget()
{
	if (m_dBuyTarget > 0.0) {
		m_nBuySell = 0;
		m_dNewTarget = m_dBuyTarget;
		UpdateData(FALSE);
	}
}


void DlgTargetPrice::OnStnClickedStaticTarget2()
{
	if (m_dSellTarget > 0.0) {
		m_nBuySell = 1;
		m_dNewTarget = m_dSellTarget;
		UpdateData(FALSE);
	}
}

void DlgTargetPrice::OnBnClickedButtonModify()
{
	UpdateData(TRUE);

	if (_RunMode == 0) {
		if (m_nBuySell == 0 && m_dBuyTarget > 0.0) {
			if (m_dNewTarget <= m_dPrice) {
				AfxMessageBox(_T("多头目标价必须大于最新价"));
				return;
			}
			m_dBuyTarget = m_dNewTarget;
			UpdateData(FALSE);
		}
		else if (m_nBuySell == 1 && m_dSellTarget > 0.0) {
			if (m_dNewTarget >= m_dPrice) {
				AfxMessageBox(_T("空头目标价必须小于最新价"));
				return;
			}
			m_dSellTarget = m_dNewTarget;
			UpdateData(FALSE);
		}
	}
	else if (_RunMode) {

	}
	EndDialog(IDOK);
}


