# 常用开发命令（Windows环境）

## 构建与编译
- 使用CMake生成VS工程：
  ```
  cmake -S . -B build
  cmake --build build --config Release
  ```
- 直接用Visual Studio打开.sln文件编译

## 运行与调试
- 各模块可独立运行（如XRobot.exe、TRobot.exe等）
- 日志/配置文件在各模块目录下

## 测试
- test/ 目录下有测试工程（test.sln），可用VS运行

## 代码搜索/分析
- 推荐用VS、VSCode、grep、findstr等工具

## 依赖管理
- 主要依赖源码在3rdParty/，部分NuGet包在packages/

## 版本管理
- git（常规命令：git status, git pull, git commit, git log等）

## 其他
- 目录切换：cd
- 文件查找：dir /s /b <pattern>
- 文本搜索：findstr /s /i <pattern> *.*
