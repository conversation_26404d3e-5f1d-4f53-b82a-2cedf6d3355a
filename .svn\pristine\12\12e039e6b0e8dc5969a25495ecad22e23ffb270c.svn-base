
#pragma once

#include "ado.h"
#include <string>
#include <boost/shared_ptr.hpp>

//SQL
//char connectstr[] = "Provider=SQLOLEDB.1;Persist Security Info=False;Data Source=127.0.0.1;User ID=xuzy;Password=******;Initial Catalog=BondBase;";

namespace utils
{
	typedef boost::shared_ptr<ADO::CADODatabase> DbPtr;
	typedef boost::shared_ptr<ADO::CADORecordset> RsPtr;
	class AdoDb
	{
	public:
		AdoDb(void);
		virtual ~AdoDb(void);
		virtual void setConnectString(const char* conn);
		virtual bool openDb();
		virtual void closeDb();
		virtual bool sqlDb(const char* sql, bool closeRs = true);
		virtual void updateDb();

	protected:
		DbPtr _dbPtr;
		RsPtr _rsPtr;
		std::string _connectString;
	};
}