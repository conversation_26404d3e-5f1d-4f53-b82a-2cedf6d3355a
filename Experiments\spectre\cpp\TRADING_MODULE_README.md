# Spectre Trading Module - C++ Implementation

## 概述

本项目成功将 Python 版本的 Spectre Trading 模块转换为 C++ 实现，基于 libtorch 库提供高性能的量化交易功能。

## 已完成的功能

### 核心类

1. **Position 类** (`include/trading/Position.h`, `src/trading/Position.cpp`)
   - 单资产持仓管理
   - 平均价格计算（包含佣金）
   - 已实现/未实现盈亏计算
   - 股票分割和分红处理
   - 止损模型集成

2. **Portfolio 类** (`include/trading/Portfolio.h`, `src/trading/Portfolio.cpp`)
   - 多资产投资组合管理
   - 现金管理和资金流跟踪
   - 历史记录和收益率计算
   - 杠杆率计算
   - 公司行为处理（分割、分红、借贷利息）

3. **Event 系统** (`include/trading/Event.h`, `src/trading/Event.cpp`)
   - 事件驱动架构
   - EventReceiver 和 EventManager
   - 市场事件（开盘、收盘）
   - 日历事件支持

4. **StopModel 类** (`include/trading/StopModel.h`, `src/trading/StopModel.cpp`)
   - 基础止损模型
   - 追踪止损模型
   - P&L 衰减追踪止损
   - 时间衰减追踪止损

5. **Metric 计算函数** (`include/trading/Metric.h`, `src/trading/Metric.cpp`)
   - 回撤和回撤持续时间计算
   - 夏普比率、索提诺比率、卡尔马比率
   - 年化波动率和最大回撤
   - VaR 和 CVaR 计算
   - 胜率、盈亏比、信息比率
   - Alpha 和 Beta 计算

6. **Calendar 类** (`include/trading/Calendar.h`)
   - 交易日历管理
   - 中国和日本市场日历支持
   - 节假日处理

### 项目结构

```
Experiments/spectre/cpp/
├── include/trading/
│   ├── Trading.h          # 主头文件
│   ├── Position.h         # 持仓类
│   ├── Portfolio.h        # 投资组合类
│   ├── Event.h           # 事件系统
│   ├── StopModel.h       # 止损模型
│   ├── Metric.h          # 性能指标
│   └── Calendar.h        # 交易日历
├── src/trading/
│   ├── Trading.cpp       # 主实现文件
│   ├── Position.cpp      # 持仓实现
│   ├── Portfolio.cpp     # 投资组合实现
│   ├── Event.cpp         # 事件系统实现
│   ├── StopModel.cpp     # 止损模型实现
│   └── Metric.cpp        # 性能指标实现
└── src/test_trading.cpp  # 单元测试
```

## 编译和使用

### 编译要求

- C++17 或更高版本
- CMake 3.12 或更高版本
- LibTorch 库
- Visual Studio 2019/2022 (Windows) 或 GCC/Clang (Linux/macOS)

### 编译步骤

```bash
cd Experiments/spectre/cpp
mkdir build && cd build
cmake ..
cmake --build . --config Release
```

### 运行测试

```bash
# 运行 trading 模块测试
./Release/test_trading.exe

# 运行主程序（包含 trading 模块演示）
./Release/spectre_main.exe
```

## 使用示例

```cpp
#include "trading/Trading.h"

// 初始化 trading 模块
Spectre::Trading::initialize();

// 创建投资组合
Spectre::Trading::Portfolio portfolio;
portfolio.update_cash(10000.0, true);
portfolio.set_datetime(std::chrono::system_clock::now());

// 添加持仓
double realized = portfolio.update("AAPL", 100, 150.0, 1.0);

// 更新价格
Spectre::Trading::Portfolio::PriceMap prices = {{"AAPL", 155.0}};
portfolio.update_value(prices);

// 计算性能指标
auto returns = portfolio.returns();
double sharpe = Spectre::Trading::Metric::sharpe_ratio(returns, 0.02);

// 清理
Spectre::Trading::cleanup();
```

## 性能特点

- **高性能**: 基于 C++ 和 libtorch，提供比 Python 版本更高的执行效率
- **内存安全**: 使用智能指针和 RAII 模式管理内存
- **类型安全**: 强类型系统避免运行时错误
- **并行计算**: 利用 libtorch 的并行计算能力
- **跨平台**: 支持 Windows、Linux 和 macOS

## 与 Python 版本的兼容性

- **API 兼容**: 保持与 Python 版本相似的 API 设计
- **功能对等**: 实现了 Python 版本的核心功能
- **数据兼容**: 计算结果与 Python 版本保持一致
- **扩展性**: 易于添加新功能和自定义策略

## 测试覆盖

所有核心功能都有对应的单元测试：

- Position 类的持仓管理和 P&L 计算
- Portfolio 类的多资产管理和历史记录
- StopModel 的各种止损策略
- Metric 函数的性能指标计算
- Event 系统的事件调度

## 未来扩展

虽然核心功能已经完成，但还有一些高级功能可以在未来添加：

1. **Blotter 类**: 订单管理和交易执行
2. **Algorithm 基类**: 完整的算法框架
3. **实时数据接口**: 与实时数据源的集成
4. **回测引擎**: 完整的历史回测功能
5. **风险管理**: 更高级的风险控制功能

## 总结

Spectre Trading Module 的 C++ 实现成功提供了一个高性能、类型安全的量化交易框架。通过利用现代 C++ 特性和 libtorch 库，该模块为量化交易策略的开发和执行提供了坚实的基础。

所有测试都通过，模块已成功集成到现有的 Spectre C++ 项目中，可以立即投入使用。
