﻿// DlgInstrumentView.cpp: 实现文件
//

#include "stdafx.h"
#include "afxdialogex.h"
#include "resource.h"
#include "DlgInstrumentView.h"

#include <DataHub.h>
#include <Instrument.h>
#include <Strategy.h>
#include <TradeSystem.h>

#include <common.hpp>
#include <ql/math/statistics/histogram.hpp>
#include <ql/math/statistics/riskstatistics.hpp>

#include "../DataHub/Settings.h"
#include "DlgSetRangeValue.h"
#include "STSDlg.h"
// DlgInstrumentView 对话框
char* DlgInstrumentView::_szHeaders[ivlistCols] = {
    "NO.", "代码", "名称", "涨幅", "现价", "L-Atr",  "S-Atr", "L/S",    "T-Atr",
    "L-%", "S-%",  "T-%",  "sTrd", "行业", "NL-Atr", "NL%",   "NS-Atr", "NS%", "到期", "LP", "SP", "L-S" };
char* DlgInstrumentView::_szMarkStar[6] = {"☆☆☆☆☆", "★☆☆☆☆", "★★☆☆☆",
                                           "★★★☆☆", "★★★★☆", "★★★★★"};
COLORREF DlgInstrumentView::_szMarkColor[6] = {
    RGB(0, 0, 0),   RGB(220, 20, 60), RGB(218, 165, 32),
    RGB(0, 0, 139), RGB(0, 128, 0),   RGB(255, 140, 0)};
int DlgInstrumentView::nCurCol = 7;
bool DlgInstrumentView::bFlag = false;

IMPLEMENT_DYNAMIC(DlgInstrumentView, CDialogEx)

DlgInstrumentView::DlgInstrumentView(CWnd* pParent /*=nullptr*/)
    : CDialogEx(IDD_DIALOG_INSTRUMENT, pParent),
      m_sBindBlock(_T("主选期货")),
      m_nApplyObject(0),
      m_nPeriodType(0),
      m_dThreshold1(0),
      m_dThreshold2(0),
      m_dL1(0),
      m_dL2(0),
      m_dL3(0),
      m_nHistoryNum(0),
      m_sModelName(_T(""))
{}

DlgInstrumentView::~DlgInstrumentView() {}

void DlgInstrumentView::DoDataExchange(CDataExchange* pDX) {
  CDialogEx::DoDataExchange(pDX);
  DDX_Radio(pDX, IDC_RADIO_FUTURE, m_nApplyObject);
  DDX_Text(pDX, IDC_EDIT_HISTORY_NUM, m_nHistoryNum);
  DDX_Radio(pDX, IDC_RADIO_SHORT, m_nPeriodType);
  DDX_Text(pDX, IDC_EDIT_ATR_THRESHOLD1, m_dThreshold1);
  DDX_Text(pDX, IDC_EDIT_ATR_THRESHOLD2, m_dThreshold2);
  DDX_Text(pDX, IDC_EDIT_RANGE_L1, m_dL1);
  DDX_Text(pDX, IDC_EDIT_RANGE_L2, m_dL2);
  DDX_Text(pDX, IDC_EDIT_RANGE_L3, m_dL3);
  DDX_CBString(pDX, IDC_COMBO_BIND_BLOCK, m_sBindBlock);
  DDX_Control(pDX, IDC_LIST_FILTER_RESULT, m_listFilterResult);
  DDX_CBString(pDX, IDC_COMBO_MODEL, m_sModelName);
}

BEGIN_MESSAGE_MAP(DlgInstrumentView, CDialogEx)
ON_MESSAGE(WM_QUICKLIST_GETLISTITEMDATA, OnGetListItem)
ON_NOTIFY(NM_DBLCLK,
          IDC_LIST_FILTER_RESULT,
          &DlgInstrumentView::OnNMDblclkListFilterResult)
ON_NOTIFY(LVN_COLUMNCLICK,
          IDC_LIST_FILTER_RESULT,
          &DlgInstrumentView::OnLvnColumnclickListSec)
ON_NOTIFY(NM_CLICK,
          IDC_LIST_FILTER_RESULT,
          &DlgInstrumentView::OnNMClickListFilterResult)
ON_CBN_SELENDOK(IDC_COMBO_BIND_BLOCK,
                &DlgInstrumentView::OnCbnSelendokComboBindBlock)
ON_BN_CLICKED(IDC_BUTTON_WRITE_RANGE,
              &DlgInstrumentView::OnBnClickedButtonTestRange)
ON_BN_CLICKED(IDC_BUTTON_RELOAD_OPTION,
              &DlgInstrumentView::OnBnClickedButtonSaveOption)
ON_BN_CLICKED(IDC_RADIO_FUTURE, &DlgInstrumentView::OnBnClickedRadioFuture)
ON_BN_CLICKED(IDC_RADIO_STOCK, &DlgInstrumentView::OnBnClickedRadioStock)
ON_BN_CLICKED(IDC_RADIO_SHORT, &DlgInstrumentView::OnBnClickedRadioShort)
ON_BN_CLICKED(IDC_RADIO_LONG, &DlgInstrumentView::OnBnClickedRadioLong)
ON_NOTIFY(UDN_DELTAPOS,
          IDC_SPIN_THRESHOLD1,
          &DlgInstrumentView::OnDeltaposSpinThreshold1)
ON_NOTIFY(UDN_DELTAPOS,
          IDC_SPIN_THRESHOLD2,
          &DlgInstrumentView::OnDeltaposSpinThreshold2)
ON_NOTIFY(UDN_DELTAPOS, IDC_SPIN_L1, &DlgInstrumentView::OnDeltaposSpinL1)
ON_NOTIFY(UDN_DELTAPOS, IDC_SPIN_L2, &DlgInstrumentView::OnDeltaposSpinL2)
ON_NOTIFY(UDN_DELTAPOS, IDC_SPIN_L3, &DlgInstrumentView::OnDeltaposSpinL3)
ON_BN_CLICKED(IDC_BUTTON_IMPORT_RANGE,
              &DlgInstrumentView::OnBnClickedButtonImportRange)
  ON_CBN_SELENDOK(IDC_COMBO_MODEL, &DlgInstrumentView::OnCbnSelendokComboModel)
END_MESSAGE_MAP()

// DlgInstrumentView 消息处理程序
void DlgInstrumentView::InitFilterResultList() {
  ListView_SetExtendedListViewStyleEx(
      m_listFilterResult.m_hWnd, LVS_EX_FULLROWSELECT, LVS_EX_FULLROWSELECT);
  ListView_SetExtendedListViewStyleEx(
      m_listFilterResult.m_hWnd, LVS_EX_SUBITEMIMAGES, LVS_EX_SUBITEMIMAGES);
  // ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd,
  // LVS_EX_CHECKBOXES, LVS_EX_CHECKBOXES);
  ListView_SetExtendedListViewStyleEx(
      m_listFilterResult.m_hWnd, LVS_EX_HEADERDRAGDROP, LVS_EX_HEADERDRAGDROP);
  ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd,
                                      LVS_EX_DOUBLEBUFFER, LVS_EX_DOUBLEBUFFER);
  ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd,
                                      LVS_EX_GRIDLINES, LVS_EX_GRIDLINES);
  //                                 0      1       2   3   4   5   6   7   8 9
  //                                 10  11  12  13  14 15
  int nColWidths[ivlistCols] = {30, 65, 65, 40, 55, 50, 50, 45, 40,
                                46, 50, 50, 50, 60, 50, 50, 50, 50, 45, 45, 45, 45 };
  // char*  szHeaders[frlistCols] = {"NO.", "代码", "名称", "涨幅", "现价",
  // "POS", "Grd", "B-Wd", "B-Exp", "RSI", "fTrd", "sTrd", "行业", "策略",
  // "标记"};
  // add columns
  DWORD fmt;
  for (int i = 0; i < ivlistCols; i++) {
    fmt = /*(i == 1) ? LVCFMT_LEFT : */ LVCFMT_RIGHT;
    fmt = (i == 1 || i == 2 || i == 11 || i == 12 || i == 13)
              ? LVCFMT_CENTER
              : fmt;
    // fmt = (i == 18) ? LVCFMT_LEFT : fmt;
    m_listFilterResult.InsertColumn(i, _szHeaders[i], fmt, nColWidths[i]);
  }

  m_listFilterResult.EnableToolTips(TRUE);
}

void DlgInstrumentView::UpdateCombox()
{
  int i = 0;

  CComboBox* pCombox = (CComboBox*)GetDlgItem(IDC_COMBO_MODEL);
  while (pCombox->GetCount())
    pCombox->DeleteString(0);

  std::vector<std::string> blkFiles;
  size_t count = kits::search_folder_files(fmt::format("{}/Model", dal::dh().GetRunDir()), ".json", &blkFiles, true, false);
  auto md_name = [](const std::string& fname) {
    return fname.substr(0, fname.rfind("."));
  };

  for (auto fname : blkFiles) {
    std::string model = md_name(fname);
    pCombox->AddString(model.c_str());
  }

  //pCombox->SelectString(0, "LRS_context_long");
}

BOOL DlgInstrumentView::OnInitDialog() {
  CDialogEx::OnInitDialog();

  CComboBox* pCombox = (CComboBox*)GetDlgItem(IDC_COMBO_BIND_BLOCK);
  while (pCombox->GetCount()) {
    pCombox->DeleteString(0);
  }

  int i = 0;
  for (i = 0; i < dal::dh().GetFilterBlockDataSize(); i++) {
    pCombox->InsertString(i, dal::dh().GetFilterBlockDataName(i).c_str());
  }

  pCombox->InsertString(i++, "A股");
  pCombox->AddString("ZLQH");
  for (i = 2; i < dal::dh().GetBlockSize(emOther); i++) {
    pCombox->AddString(dal::dh().GetBlockName(emOther, i).c_str());
  }
  pCombox->AddString("其它市场.EX");

  InitFilterResultList();

  _whis.clear();
  //_whis.push_back("WFI999.IX");
  //_whis.push_back("WYZL99.IX");
  //_whis.push_back("WHSL99.IX");
  //_whis.push_back("WHGB99.IX");
  //_whis.push_back("WYSB99.IX");
  LoadSecRangeBarValue();

  UpdateCombox();

  OnCbnSelendokComboBindBlock();

  return TRUE;
}

LRESULT DlgInstrumentView::OnGetListItem(WPARAM wParam, LPARAM lParam) {
  ASSERT((HWND)wParam == m_listFilterResult.GetSafeHwnd());

  CQuickList::CListItemData* data = (CQuickList::CListItemData*)lParam;

  int item = data->GetItem();
  int subItem = data->GetSubItem();

  data->m_text = _listText[item].txt[subItem];
  switch (subItem) {
    case 0:
      data->m_text.Format(_T("%d"), (item + 1));
      break;
    case 1:
    case 2: {
      int clr = std::atoi(_listText[item].txt[ivlistCols]);
      if (clr > 0 && clr < 6) {
        data->m_colors.m_textColor = _szMarkColor[clr];
        data->m_textStyle.m_bold = true;
      }
    } break;
    case 3:  //涨幅
    case 7:
    case 9:
    case 15:
    case 17: {
      double riserate = std::atof(_listText[item].txt[subItem]);
      int clr = (int)(abs(riserate) * 30);
      if (clr > 255)
        clr = 255;
      clr = 255 - clr;
      if (riserate >= 0) {
        data->m_colors.m_textColor = RGB(10, 10, 10);
        data->m_colors.m_backColor = RGB(255, clr, clr);
      } else {
        data->m_colors.m_textColor = RGB(10, 10, 10);
        data->m_colors.m_backColor = RGB(clr, 255, clr);
      }
      data->m_textStyle.m_bold = true;
    } break;
    case 10:
    case 11: {
      double riserate = std::atof(_listText[item].txt[subItem]);
      int clr = (int)(abs(riserate) * 300);
      if (clr > 255)
        clr = 255;
      clr = 255 - clr;
      if (riserate >= 0) {
        data->m_colors.m_textColor = RGB(10, 10, 10);
        data->m_colors.m_backColor = RGB(255, clr, clr);
      } else {
        data->m_colors.m_textColor = RGB(10, 10, 10);
        data->m_colors.m_backColor = RGB(clr, 255, clr);
      }
      data->m_textStyle.m_bold = true;
    } break;
      // case 7://B-Wd
      {
        // int bwd = std::atoi(_listText[item].txt[subItem]);
        // int clr = (int)(abs(bwd) * 2);
        // if (clr > 255) clr = 255;
        // clr = 255 - clr;
        // if (bwd >= 0)
        //{
        //	data->m_colors.m_textColor = RGB(10, 10, 10);
        //	data->m_colors.m_backColor = RGB(255, clr, clr);
        //}
        // else
        //{
        //	data->m_colors.m_textColor = RGB(10, 10, 10);
        //	data->m_colors.m_backColor = RGB(clr, 255, clr);
        //}
        // data->m_textStyle.m_bold = true;
      }
      break;
    case 8:  // B-Exp
    {
      // int bexp = std::atoi(_listText[item].txt[subItem]);
      // int clr = (int)(abs(bexp) * 2);
      // if (clr > 255) clr = 255;
      // clr = 255 - clr;
      // if (bexp >= 0)
      //{
      //	data->m_colors.m_textColor = RGB(10, 10, 10);
      //	data->m_colors.m_backColor = RGB(255, clr, clr);
      //}
      // else
      //{
      //	data->m_colors.m_textColor = RGB(10, 10, 10);
      //	data->m_colors.m_backColor = RGB(clr, 255, clr);
      //}
      // data->m_textStyle.m_bold = true;
    } break;
    // case 9://maRSI
    //{
    //	//double riserate = std::atof(_listText[item].txt[subItem]) - 50.0;
    //	//int clr = (int)(abs(riserate) * 8);
    //	//if (clr > 255) clr = 255;
    //	//clr = 255 - clr;
    //	//if (riserate >= 0)
    //	//{
    //	//	data->m_colors.m_textColor = RGB(10, 10, 10);
    //	//	data->m_colors.m_backColor = RGB(255, clr, clr);
    //	//}
    //	//else
    //	//{
    //	//	data->m_colors.m_textColor = RGB(10, 10, 10);
    //	//	data->m_colors.m_backColor = RGB(clr, 255, clr);
    //	//}
    //}
    // break;
    // case 10://NEW
    //	break;
    case 18: {
      if (std::atoi(_listText[item].txt[subItem]) > 0)
        data->m_colors.m_textColor = RGB(10, 10, 10);
      else
        data->m_colors.m_textColor = RGB(255, 0, 0);
    } break;
    case 19:
    case 20: {
      if (_long_model.find("_ls") != std::string::npos) {
        if (std::atof(_listText[item].txt[subItem]) > 0)
          data->m_colors.m_textColor = RGB(220, 20, 20);
        else
          data->m_colors.m_textColor = RGB(0, 128, 0);
      }
      else {
        if (std::atof(_listText[item].txt[subItem]) - 0.5 > 0)
          data->m_colors.m_textColor = RGB(220, 20, 20);
        else
          data->m_colors.m_textColor = RGB(0, 128, 0);
      }
    } break;
    case 21: {
      if (std::atof(_listText[item].txt[subItem]) > 0)
        data->m_colors.m_textColor = RGB(220, 20, 20);
      else
        data->m_colors.m_textColor = RGB(0, 128, 0);
    }break;
  }

  return 0;
}

void DlgInstrumentView::FillFilterResultList(BlockDataPtr blk_ptr) {
  if (blk_ptr.get() != nullptr) {
    _blk_ptr = blk_ptr;
  }

  if (_blk_ptr.get() == nullptr) {
    m_listFilterResult.SetItemCount(0);
    return;
  }

  _listText.resize(_blk_ptr->size());

  m_listFilterResult.SetRedraw(FALSE);
  m_listFilterResult.SetItemCount(_blk_ptr->size());

  // insert the items and subitems into the list
  int row = 0;
  std::string label;
  for (auto it : *_blk_ptr) {
    label = dal::dh().GetLabel(it);
    QuoteData* qd_ptr = dal::dh().GetQuoteData(label);
    if (qd_ptr == nullptr) {
      continue;
    }

    if (qd_ptr->_update == 0) {
      dal::dh().GetNewQuoteData(label);
    }
    dal::InstrumentPtr it_ptr = dal::getInstrument(label, 0);
    if (it_ptr.get() == nullptr)
      continue;

    int col = 0;
    //序号
    // sprintf_s(_listText[row].txt[col++], 64, "%d", row+1);
    col++;
    //代码
    sprintf_s(_listText[row].txt[col++], 64, "%s", label.c_str());
    //名称
    sprintf_s(_listText[row].txt[col++], 64, "%s",
              dal::dh().GetSecName(label).c_str());

    //涨跌幅
    double riserate = 0.0;
    if (qd_ptr->_preclose > 0.00001 && qd_ptr->_new > 0.00001) {
      riserate = (qd_ptr->_new - qd_ptr->_preclose) / qd_ptr->_preclose;
    }
    sprintf_s(_listText[row].txt[col++], 64, "%.2f", riserate * 100);

    //现价
    sprintf_s(_listText[row].txt[col++], 64, "%.2f", qd_ptr->_new);

    // long range atr
    sprintf_s(_listText[row].txt[col++], 32, "%.3f",
              it_ptr->_md_ptr->long_range_atr);

    // short range atr
    sprintf_s(_listText[row].txt[col++], 32, "%.3f",
              it_ptr->_md_ptr->short_range_atr);

    // long/short range atr
    sprintf_s(
        _listText[row].txt[col++], 32, "%.2f",
        it_ptr->_md_ptr->long_range_atr / it_ptr->_md_ptr->short_range_atr);

    // TATR
    sprintf_s(_listText[row].txt[col++], 32, "%.3f",
              it_ptr->fdptr(BarSize::range)->factors[FS_TATR].value());

    // B-Exp
    sprintf_s(_listText[row].txt[col++], 32, "%.3f",
              it_ptr->_md_ptr->long_range_atr / it_ptr->Price() * 100.0);
    sprintf_s(_listText[row].txt[col++], 32, "%.3f",
              it_ptr->_md_ptr->short_range_atr / it_ptr->Price() * 100.0);
    sprintf_s(_listText[row].txt[col++], 32, "%.3f",
              it_ptr->fdptr(BarSize::range)->factors[FS_TATR].value() /
                  it_ptr->Price() * 100.0);

    // FAST TREND VALUE
    // sprintf_s(_listText[row].txt[col++], 64, "%.2f",
    // it_ptr->fdptr()->factors[FS_TREND_VALUE].value());

    // SLOW TREND VALUE
    sprintf_s(_listText[row].txt[col++], 64, "%.2f",
              it_ptr->_long_fd_ptr->factors[FS_TREND_VALUE].value());

    //行业
    sprintf_s(_listText[row].txt[col++], 64, "%s",
              dal::dh().GetSecIndustry(label).c_str());

		auto iter = _optiman_atrs.find(label);
    if (iter != _optiman_atrs.end()) {
      // new long range atr
      sprintf_s(_listText[row].txt[col++], 64, "%.2f", iter->second.long_atr);
      sprintf_s(
          _listText[row].txt[col++], 32, "%.2f",
          100 * (iter->second.long_atr / it_ptr->_md_ptr->long_range_atr - 1));

      // short range atr
      sprintf_s(_listText[row].txt[col++], 64, "%.2f", iter->second.short_atr);
      sprintf_s(
          _listText[row].txt[col++], 32, "%.2f",
          100 *
              (iter->second.short_atr / it_ptr->_md_ptr->short_range_atr - 1));
    } else {
      // new long range atr
      sprintf_s(_listText[row].txt[col++], 64, "--.--");
      sprintf_s(_listText[row].txt[col++], 32, "--.--");

      // short range atr
      sprintf_s(_listText[row].txt[col++], 64, "--.--");
      sprintf_s(_listText[row].txt[col++], 32, "--.--");
    }
    sprintf_s(_listText[row].txt[col++], 32, "%d", it_ptr->_expire_days);
    double lp = bll::Predict(it_ptr->_label, _long_model);
    double sp = bll::Predict(it_ptr->_label, _short_model);
    sprintf_s(_listText[row].txt[col++], 32, "%.4f", lp);
    sprintf_s(_listText[row].txt[col++], 32, "%.4f", sp);
    sprintf_s(_listText[row].txt[col++], 32, "%.4f", lp - sp);

    row++;
  }

  SortColumn();

  // unlock window updates
  m_listFilterResult.SetRedraw(TRUE);

  // invalidate the entire control, force painting
  m_listFilterResult.Invalidate();
  m_listFilterResult.UpdateWindow();
}

//排序比较函数
bool compaire_col(const DlgInstrumentView::ListText& txt1,
                  const DlgInstrumentView::ListText& txt2) {
  if (DlgInstrumentView::nCurCol == 0) {
    return dal::sort_compaire_func("", txt1.txt[1], txt2.txt[1]);
  } else if (DlgInstrumentView::nCurCol == 1 ||
             DlgInstrumentView::nCurCol == 2 ||
             DlgInstrumentView::nCurCol == 13) {
    if (_stricmp(txt1.txt[DlgInstrumentView::nCurCol],
                 txt2.txt[DlgInstrumentView::nCurCol]) < 0)
      return DlgInstrumentView::bFlag;
    else
      return !DlgInstrumentView::bFlag;
  } else {
    double d1 = std::atof(txt1.txt[DlgInstrumentView::nCurCol]);
    double d2 = std::atof(txt2.txt[DlgInstrumentView::nCurCol]);
    if (d1 < d2)  //降序
      return DlgInstrumentView::bFlag;
    else
      return !DlgInstrumentView::bFlag;
  }

  return DlgInstrumentView::bFlag;
}

void DlgInstrumentView::OnLvnColumnclickListSec(NMHDR* pNMHDR,
                                                LRESULT* pResult) {
  LPNMLISTVIEW pNMLV = reinterpret_cast<LPNMLISTVIEW>(pNMHDR);

  if (nCurCol != pNMLV->iSubItem) {
    nCurCol = pNMLV->iSubItem;
  } else {
    bFlag = !bFlag;
  }

  FillFilterResultList();

  *pResult = 0;
}

//列排序
void DlgInstrumentView::SortColumn() {
  if (nCurCol < 0 || nCurCol > ivlistCols)
    return;
#ifndef _DEBUG
  std::sort(_listText.begin(), _listText.end(), compaire_col);
#endif
}

void DlgInstrumentView::OnOK() {
  CDialogEx::OnOK();
}

void DlgInstrumentView::OnCancel() {
  CDialogEx::OnCancel();
}

void DlgInstrumentView::OnSize(UINT nType, int cx, int cy) {
  CDialogEx::OnSize(nType, cx, cy);

  if (m_listFilterResult.GetSafeHwnd()) {
    CRect rect;
    this->GetWindowRect(&rect);
    ScreenToClient(rect);
    m_listFilterResult.MoveWindow(rect);
  }
}

std::string DlgInstrumentView::GetPrevLabel() {
  m_nCurSel--;
  if (m_nCurSel < 0) {
    m_nCurSel = 0;
  }

  if (_labels_sel == 0) {
    CString label = m_listFilterResult.GetItemText(m_nCurSel, 1);
    return std::string((LPCSTR)label);
  } else {
    if (m_nCurSel < 0 || m_nCurSel >= _whis.size()) {
      m_nCurSel = 0;
    }

    return _whis[m_nCurSel];
  }

  return "";
}

std::string DlgInstrumentView::GetNextLabel() {
  m_nCurSel++;
  if (_labels_sel == 0) {
    if (m_nCurSel >= m_listFilterResult.GetItemCount()) {
      if (MessageBox("已经到最后一个，是否从头开始？", "提示",
                     MB_YESNO | MB_ICONQUESTION) == IDYES) {
        m_nCurSel = 0;
      } else {
        m_nCurSel = m_listFilterResult.GetItemCount() - 1;
      }
    }

    CString label = m_listFilterResult.GetItemText(m_nCurSel, 1);

    return std::string((LPCSTR)label);
  } else {
    if (m_nCurSel < 0 || m_nCurSel >= _whis.size()) {
      m_nCurSel = 0;
    }

    return _whis[m_nCurSel];
  }

  return "";
}

void DlgInstrumentView::OnNMDblclkListFilterResult(NMHDR* pNMHDR,
                                                   LRESULT* pResult) {
  LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
  if (_blk_ptr.get() == nullptr) {
    return;
  }

  m_nCurSel = pNMItemActivate->iItem;

  if (m_nCurSel >= 0 && m_nCurSel < _blk_ptr->size()) {
    CString label = m_listFilterResult.GetItemText(m_nCurSel, 1);
    if (pNMItemActivate->iSubItem == 4) {
      QuoteData* qd_ptr = dal::dh().GetQuoteData((LPCSTR)label);
      if (qd_ptr != nullptr && qd_ptr->_update == 0) {
        dal::dh().GetNewQuoteData((LPCSTR)label);
      }
    } else if (pNMItemActivate->iSubItem == 5) {
      if (MessageBox("您需要重新计算Long Range Atr吗？", "提示",
                     MB_YESNO | MB_ICONQUESTION) == IDNO) {
        return;
      }
      BarSize barsize = BarSize::day;
      if (!dal::dh().IsFutures((LPCSTR)label)) {
        barsize = BarSize::week;
      }
      double atr = dal::dh().GetRangeBarAtr((LPCSTR)label, barsize, true);
      if (atr > 0.0) {
        dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
        it_ptr->_md_ptr->long_range_atr = atr;
        FillFilterResultList();
      }
    } else if (pNMItemActivate->iSubItem == 6) {
      if (MessageBox("您需要重新计算Short Range Atr吗？", "提示",
                     MB_YESNO | MB_ICONQUESTION) == IDNO) {
        return;
      }
      double atr =
          dal::dh().GetRangeBarAtr((LPCSTR)label, BarSize::range, true);
      if (atr > 0.0) {
        dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
        it_ptr->_md_ptr->short_range_atr = atr;
        FillFilterResultList();
      }
    } else if (pNMItemActivate->iSubItem == 9 ||
               pNMItemActivate->iSubItem == 10) {
      if (MessageBox("您需要手动修改Range Atr吗？", "提示",
                     MB_YESNO | MB_ICONQUESTION) == IDNO) {
        return;
      }
      DlgSetRangeValue dlg;
      double range = 0.0;
      dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
      if (pNMItemActivate->iSubItem == 10) {
        range = it_ptr->_md_ptr->short_range_atr;
        dlg.m_nBarSize = 0;
      } else {
        range = it_ptr->_md_ptr->long_range_atr;
        dlg.m_nBarSize = 1;
      }

      dlg.m_sValue.Format("%.3f", range);
      dlg.m_sLabel = label;
      if (dlg.DoModal() != IDOK) {
        return;
      }
      FillFilterResultList();
    } else {
      if (label.IsEmpty()) {
        return;
      }
      _labels_sel = 0;
    }
  }
  *pResult = 0;
}

void DlgInstrumentView::OnNMClickListFilterResult(NMHDR* pNMHDR,
                                                  LRESULT* pResult) {
  LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
  m_nCurSel = pNMItemActivate->iItem;

  if (m_nCurSel >= 0 && m_nCurSel < _blk_ptr->size()) {
    //((CSTSDlg*)GetParent())->m_sBindLabel =
    //m_listFilterResult.GetItemText(m_nCurSel, 1);
    // dal::settings()._sim_bind_label =
    // (LPCSTR)((CSTSDlg*)GetParent())->m_sBindLabel;
    //((CSTSDlg*)GetParent())->UpdateData(FALSE);
    if (pNMItemActivate->iSubItem == 18) {
      std::string label = (LPCSTR)m_listFilterResult.GetItemText(m_nCurSel, 1);
      dal::InstrumentPtr it_ptr = dal::getInstrument(label);
      if (it_ptr != nullptr && it_ptr->_expire_days <= 0 && dal::dh().GetLabelMarketIndex(label) != MarketIndex::FX) {
        PMSG_WARN("{}:缺少合约信息!", label);
        tb::query_broker_data(
            kits::get_next_id(dal::settings()._clientId), label,
            QBD_CTP_INSTRUMENT);
        Sleep(1100);
        tb::query_broker_data(
            kits::get_next_id(dal::settings()._clientId), label,
            QBD_CTP_INSTRUMENT_MARGIN_RATE);
      }
    }
  }

  *pResult = 0;
}

void DlgInstrumentView::OnCbnSelendokComboBindBlock() {
  UpdateData();
  _blk_ptr = dal::dh().GetBlockData((LPCSTR)m_sBindBlock);
  FillFilterResultList(_blk_ptr);
}

/////////////////////////////////////////
void DlgInstrumentView::LoadSecRangeBarValue() {
  // UpdateData();
  m_nApplyObject = 0;
  m_nPeriodType = 0;

  nlohmann::json& root = dal::dh().GetRangeBarAtrConfig();
  if (root.is_null() || root.empty()) {
    std::ifstream ifs(fmt::format("{}\\RangeBar.json", dal::dh().GetRunDir()));
    if (!ifs.is_open()) {
      return;
    }
    try {
      ifs >> root;
      ifs.close();
    } catch (...) {
      // PopMessage(MSG_Error, "Read Sec RangeBar json file parse error.");
      return;
    }
  }

  try {
    m_nHistoryNum = root["fut"]["short_period"]["history_bar_num"].get<int>();
    m_dThreshold1 = root["fut"]["short_period"]["threshold1"].get<double>();
    m_dThreshold2 = root["fut"]["short_period"]["threshold2"].get<double>();
    m_dL1 = root["fut"]["short_period"]["level1"].get<double>();
    m_dL2 = root["fut"]["short_period"]["level2"].get<double>();
    m_dL3 = root["fut"]["short_period"]["level3"].get<double>();
  } catch (...) {
    // PopMessage(MSG_Error, "Read Sec RangeBar json error.");
  }
  UpdateData(FALSE);
}

void DlgInstrumentView::UpdateParameter() {
  UpdateData();
  std::string key1, key2;
  if (m_nApplyObject == 0) {
    key1 = "fut";
  } else {
    key1 = "stk";
  }

  if (m_nPeriodType == 0) {
    key2 = "short_period";
  } else {
    key2 = "long_period";
  }

  try {
    nlohmann::json& root = dal::dh().GetRangeBarAtrConfig();
    m_nHistoryNum = root[key1][key2]["history_bar_num"].get<int>();
    m_dThreshold1 = root[key1][key2]["threshold1"].get<double>();
    m_dThreshold2 = root[key1][key2]["threshold2"].get<double>();
    m_dL1 = root[key1][key2]["level1"].get<double>();
    m_dL2 = root[key1][key2]["level2"].get<double>();
    m_dL3 = root[key1][key2]["level3"].get<double>();
  } catch (...) {
    // PopMessage(MSG_Error, "Read Sec RangeBar json error.");
  }

  UpdateData(FALSE);
}

void DlgInstrumentView::OnBnClickedRadioFuture() {
  UpdateParameter();
}

void DlgInstrumentView::OnBnClickedRadioStock() {
  UpdateParameter();
}

void DlgInstrumentView::OnBnClickedRadioShort() {
  UpdateParameter();
}

void DlgInstrumentView::OnBnClickedRadioLong() {
  UpdateParameter();
}

void DlgInstrumentView::OnBnClickedButtonTestRange() {
  UpdateData();

  std::string key1, key2;
  if (m_nApplyObject == 0) {
    key1 = "fut";
  } else {
    key1 = "stk";
  }

  if (m_nPeriodType == 0) {
    key2 = "short_period";
  } else {
    key2 = "long_period";
  }

  if (m_nHistoryNum < 15 || m_nHistoryNum > 260) {
    MessageBox("历史数据长度在[15,260]期间内！", "提示",
               MB_OK | MB_ICONWARNING);
    return;
  }

  if (m_dThreshold1 > m_dThreshold2 || m_dThreshold1 < 0.0001 ||
      m_dThreshold2 > 10) {
    MessageBox("ATR Ratio Level1 < Level3 and > 0 ！", "提示",
               MB_OK | MB_ICONWARNING);
    return;
  }

  if (m_dL1 > m_dL2 || m_dL2 > m_dL3 || m_dL1 < 0.01 || m_dL3 > 10) {
    MessageBox("Range Ratio Level1 < Level2 < Level3 and > 0 ！", "提示",
               MB_OK | MB_ICONWARNING);
    return;
  }

  nlohmann::json& root = dal::dh().GetRangeBarAtrConfig();
  try {
    root[key1][key2]["history_bar_num"] = m_nHistoryNum;
    root[key1][key2]["threshold1"] = m_dThreshold1;
    root[key1][key2]["threshold2"] = m_dThreshold2;
    root[key1][key2]["level1"] = m_dL1;
    root[key1][key2]["level2"] = m_dL2;
    root[key1][key2]["level3"] = m_dL3;
  } catch (...) {
    // PopMessage(MSG_Error, "Read Sec RangeBar json error.");
  }

  double atr = 0.0;
  std::string label;
  std::string label_code;
  BlockDataPtr blk_ptr;
  BlockDataPtr blk_ptr2;
  std::vector<double> atr_chgs;

  if (m_nApplyObject == 0) {
    blk_ptr = dal::dh().GetBlockData("ZLQH");
    // blk_ptr2 = dal::dh().GetBlockData("其它市场.EX");
    // if (blk_ptr2.get() != nullptr) {
    //	for (auto i : *blk_ptr2) {
    //		label = dal::dh().GetLabel(i);
    //		ContractDetails* cont_ptr = dal::dh().GetContract(label);
    //		if (cont_ptr != nullptr
    //			&& !cont_ptr->summary.expiry.empty()
    //			&& boost::lexical_cast<long>(cont_ptr->summary.expiry) / 100
    //== kits::get_current_long_date() / 100) {
    // blk_ptr->insert(i);
    //		}
    //	}
    //}

    for (auto i : *blk_ptr) {
      _optiman_atrs[label] = bll::BtRpt();
    }

    for (auto i : *blk_ptr) {
      label = dal::dh().GetLabel(i);
      label_code = dal::dh().GetFuturesLabelCode(label);

      dal::InstrumentPtr it_ptr = dal::getInstrument(label);

      //
      if (m_nPeriodType == 0) {
        atr_chgs.push_back(
            dal::dh().GetDefaultRangeBarAtr(label, BarSize::range, false) /
            dal::dh().GetLastPrice(label));
        atr =
            dal::dh().GetDefaultRangeBarAtr(label, BarSize::range, true, true);

        if (atr > 0.0) {
          _optiman_atrs[label].short_atr = atr;
        }
      } else {
        atr_chgs.push_back(
            dal::dh().GetDefaultRangeBarAtr(label, BarSize::day, false) /
            dal::dh().GetLastPrice(label));
        atr = dal::dh().GetDefaultRangeBarAtr(label, BarSize::day, true, true);

        if (atr > 0.0) {
          _optiman_atrs[label].long_atr = atr;
        }
      }
    }
  } else {
    blk_ptr = dal::dh().GetBlockData("A股");
    blk_ptr->insert(dal::dh().GetIndex("150197.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150290.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150228.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150118.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150019.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150201.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150270.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150153.SZ"));
    blk_ptr->insert(dal::dh().GetIndex("150131.SZ"));
    for (auto i : *blk_ptr) {
      label = dal::dh().GetLabel(i);
      dal::InstrumentPtr it_ptr = dal::getInstrument(label);
      double price = dal::dh().GetLastPrice(label);
      if (m_nPeriodType == 0) {
        if (price > 0.0) {
          atr_chgs.push_back(
              dal::dh().GetDefaultRangeBarAtr(label, BarSize::range, false) /
              price);
        }
        atr =
            dal::dh().GetDefaultRangeBarAtr(label, BarSize::range, true, true);
        if (atr > 0.0) {
          if (abs(atr - it_ptr->_md_ptr->short_range_atr) > 0.001) {
            PMSG_NOTICE("{} short RangeBar value: {:.3f} -> {:.3f}",
                                     label,
                                     it_ptr->_md_ptr->short_range_atr, atr);
          }
          it_ptr->_md_ptr->short_range_atr = atr;
        }
      } else {
        if (price > 0.0) {
          atr_chgs.push_back(
              dal::dh().GetDefaultRangeBarAtr(label, BarSize::week, false) /
              price);
        }
        atr = dal::dh().GetDefaultRangeBarAtr(label, BarSize::week, true, true);
        if (atr > 0.0) {
          if (abs(atr - it_ptr->_md_ptr->long_range_atr) > 0.001) {
            PMSG_NOTICE("{} long RangeBar value: {:.3f} -> {:.3f}",
                                     label,
                                     it_ptr->_md_ptr->long_range_atr, atr);
          }
          it_ptr->_md_ptr->long_range_atr = atr;
        }
      }
    }
  }

  if (atr_chgs.size() > 1) {
    QuantLib::RiskStatistics s;
    s.addSequence(atr_chgs.begin(), atr_chgs.end());
    GetDlgItem(IDC_STATIC_STATS)
        ->SetWindowText(fmt::format(
                            "样本： "
                            "{:d}\t最小：{:.4f}\t最大：{:.4f}\n均值：{:.4f}\t标准差：{:."
                            "4f}\n参考值1：{:.4f}\t参考值2：{:.4f}",
                            atr_chgs.size(), s.min(), s.max(), s.mean(),
                            s.standardDeviation(),
                            s.mean() - 1.5 * s.standardDeviation(),
                            s.mean() + 1.5 * s.standardDeviation())
                            .c_str());
  }

  FillFilterResultList();

  MessageBox("参数更新完成！");
}

// 分级RangeBar参数
void DlgInstrumentView::OnBnClickedButtonSaveOption() {
  std::string label;
  std::string label_code;
  BlockDataPtr blk_ptr;
  blk_ptr = dal::dh().GetBlockData("ZLQH");
  nlohmann::json& root = dal::dh().GetRangeBarAtrConfig();
  for (auto i : *blk_ptr) {
    label = dal::dh().GetLabel(i);
    label_code = dal::dh().GetFuturesLabelCode(label);

    dal::InstrumentPtr it_ptr = dal::getInstrument(label);
    auto iter = _optiman_atrs.find(label);
    if (iter != _optiman_atrs.end()) {
      if (iter->second.short_atr > 0.0) {
        it_ptr->_md_ptr->short_range_atr = iter->second.short_atr;
        root["fut"]["short_range_bar"][label_code] = iter->second.short_atr;
        dal::BarSeries* bs_ptr = dal::dh().GetBarSeries(label, BarSize::range);
        if (bs_ptr != nullptr) {
          bs_ptr->SetRange(iter->second.short_atr);
        }
      }
      if (iter->second.long_atr > 0.0) {
        it_ptr->_md_ptr->long_range_atr = iter->second.long_atr;
        root["fut"]["long_range_bar"][label_code] = iter->second.long_atr;
        dal::BarSeries* bs_ptr = dal::dh().GetBarSeries(label, BarSize::day);
        if (bs_ptr != nullptr) {
          bs_ptr->SetRange(iter->second.long_atr);
        }
      }
    }
  }

  FillFilterResultList();
  std::ofstream ofs(fmt::format("{}\\RangeBar.json", dal::dh().GetRunDir()));
  if (!ofs.is_open()) {
    return;
  }
  std::string str = root.dump(4);
  ofs.write(str.c_str(), str.length());
  ofs.close();

  if (dal::dh().SaveBaseDataEx(true)) {
    MessageBox("分级RangeBar参数更新保存成功！");
  }
}

double DlgInstrumentView::GetSpinValue() {
  if (m_nApplyObject == 0) {
    if (m_nPeriodType == 0) {
      m_dSpin = 0.01;
    } else {
      m_dSpin = 0.01;
    }
  } else {
    if (m_nPeriodType == 0) {
      m_dSpin = 0.01;
    } else {
      m_dSpin = 0.01;
    }
  }
  return m_dSpin;
}

void DlgInstrumentView::OnDeltaposSpinThreshold1(NMHDR* pNMHDR,
                                                 LRESULT* pResult) {
  UpdateData();
  GetSpinValue();
  LPNMUPDOWN pNMUpDown = reinterpret_cast<LPNMUPDOWN>(pNMHDR);
  if (pNMUpDown->iDelta == -1) {  // 如果此值为-1 , 说明点击了Spin的往上箭头
    m_dThreshold1 += m_dSpin;
  } else if (pNMUpDown->iDelta == 1) {  // 如果此值为, 说明点击了Spin的往下箭头
    if (m_dThreshold1 > m_dSpin) {
      m_dThreshold1 -= m_dSpin;
    }
  }

  *pResult = 0;
  UpdateData(FALSE);
}

void DlgInstrumentView::OnDeltaposSpinThreshold2(NMHDR* pNMHDR,
                                                 LRESULT* pResult) {
  UpdateData();
  GetSpinValue();
  LPNMUPDOWN pNMUpDown = reinterpret_cast<LPNMUPDOWN>(pNMHDR);
  if (pNMUpDown->iDelta == -1) {  // 如果此值为-1 , 说明点击了Spin的往上箭头
    m_dThreshold2 += m_dSpin;
  } else if (pNMUpDown->iDelta == 1) {  // 如果此值为, 说明点击了Spin的往下箭头
    if (m_dThreshold2 > m_dSpin) {
      m_dThreshold2 -= m_dSpin;
    }
  }
  *pResult = 0;
  UpdateData(FALSE);
}

void DlgInstrumentView::OnDeltaposSpinL1(NMHDR* pNMHDR, LRESULT* pResult) {
  UpdateData();
  GetSpinValue();
  LPNMUPDOWN pNMUpDown = reinterpret_cast<LPNMUPDOWN>(pNMHDR);
  if (pNMUpDown->iDelta == -1) {  // 如果此值为-1 , 说明点击了Spin的往上箭头
    m_dL1 += m_dSpin;
  } else if (pNMUpDown->iDelta == 1) {  // 如果此值为, 说明点击了Spin的往下箭头
    if (m_dL1 > m_dSpin) {
      m_dL1 -= m_dSpin;
    }
  }

  *pResult = 0;
  UpdateData(FALSE);
}

void DlgInstrumentView::OnDeltaposSpinL2(NMHDR* pNMHDR, LRESULT* pResult) {
  UpdateData();
  GetSpinValue();
  LPNMUPDOWN pNMUpDown = reinterpret_cast<LPNMUPDOWN>(pNMHDR);
  if (pNMUpDown->iDelta == -1) {  // 如果此值为-1 , 说明点击了Spin的往上箭头
    m_dL2 += m_dSpin;
  } else if (pNMUpDown->iDelta == 1) {  // 如果此值为, 说明点击了Spin的往下箭头
    if (m_dL2 > m_dSpin) {
      m_dL2 -= m_dSpin;
    }
  }

  *pResult = 0;
  UpdateData(FALSE);
}

void DlgInstrumentView::OnDeltaposSpinL3(NMHDR* pNMHDR, LRESULT* pResult) {
  UpdateData();
  GetSpinValue();
  LPNMUPDOWN pNMUpDown = reinterpret_cast<LPNMUPDOWN>(pNMHDR);
  if (pNMUpDown->iDelta == -1) {  // 如果此值为-1 , 说明点击了Spin的往上箭头
    m_dL3 += m_dSpin;
  } else if (pNMUpDown->iDelta == 1) {  // 如果此值为, 说明点击了Spin的往下箭头
    if (m_dL3 > m_dSpin) {
      m_dL3 -= m_dSpin;
    }
  }

  *pResult = 0;
  UpdateData(FALSE);
}

void DlgInstrumentView::OnBnClickedButtonImportRange() {
  CFileDialog dlg(
      TRUE, nullptr, nullptr, OFN_OVERWRITEPROMPT,
      _T("Comma Separated Values(*.json)|*.json|Text Files (*.txt)|*.txt||"));

  if (dlg.DoModal() == IDOK) {
    if (!dal::dh().ImportRangeBarAtr((LPCSTR)dlg.GetPathName())) {
      MessageBox("导入Range bar配置参数失败！", "提示", MB_OK | MB_ICONWARNING);
    } else {
      MessageBox("导入Range bar配置参数成功，重启生效！", "提示",
                 MB_OK | MB_ICONEXCLAMATION);
    }
  }
}


void DlgInstrumentView::OnCbnSelendokComboModel()
{
  UpdateData();
  _long_model = (LPCSTR)m_sModelName;
  if (_long_model.find("_ls") != std::string::npos) {
    _short_model = _long_model;

    std::array<double, 4> stat = bll::GetModelPredStat(_long_model);
    GetDlgItem(IDC_STATIC_STATS)->SetWindowText(
      fmt::format("模型预测统计:\n mean: {:.3f}\tstddev: {:.3f}\tpercent01: {:.3f}\tpercent99: {:.3f}",
        stat[0], stat[1], stat[2], stat[3]).c_str());
  }
  else {
    if (_long_model.find("long") != std::string::npos) {
      _short_model = _long_model.substr(0, _long_model.length() - 4) + "short";
    }
    else {
      _short_model = _long_model;
      _long_model = _long_model.substr(0, _long_model.length() - 5) + "long";
    }

    std::array<double, 4> long_stat = bll::GetModelPredStat(_long_model);
    std::array<double, 4> short_stat = bll::GetModelPredStat(_short_model);
    GetDlgItem(IDC_STATIC_STATS)->SetWindowText(
      fmt::format("模型预测统计:\nLONG  mean: {:.3f}\tstddev: {:.3f}\tpercent01: {:.3f}\tpercent99: {:.3f}\nSHORT mean: {:.3f}\tstddev: {:.3f}\tpercent01: {:.3f}\tpercent99: {:.3f}",
        long_stat[0], long_stat[1], long_stat[2], long_stat[3],
        short_stat[0], short_stat[1], short_stat[2], short_stat[3]).c_str());
  }

  FillFilterResultList();
}
