#pragma once
#include "../client_socket_utils.h"
#include <thread>

using namespace net;
class clientimpl : public net::client_socket_utils
{
public:
	clientimpl();
	~clientimpl();

	protected:
		void handle_read_data(message& msg, socket_session_ptr pSession){
			if (msg.command == 3) {
				long date;
				float price;
				char symbol[16];
				memset(symbol, 0, sizeof(symbol));
				memcpy_s((char*)&date, 4, &msg.body[0], 4);
				memcpy_s((char*)&price, 4, &msg.body[4], 4);
				memcpy_s(symbol, 16, &msg.body[8], msg.body.length() - 8);
				std::cout << "quote: " << symbol << ", " << date << ", " << price << std::endl;
			}
		}

};

clientimpl::clientimpl()
{
}

clientimpl::~clientimpl()
{
}

int main(int argc, char* argv[])
{
	boost::asio::io_service io_service;
	socket_session_ptr ss_ptr = socket_session_ptr(new socket_session(io_service));
	ss_ptr->set_serilzation_type(ST_NONE);
	ss_ptr->set_app_id(0);
	ss_ptr->set_business_type(0);
	std::string ip_adrr = "127.0.0.1:10005";
	ss_ptr->set_remote_addr(ip_adrr);
	ss_ptr->set_op_time();
	clientimpl clt;
	clt.session_connect(ss_ptr);
	std::thread t([&io_service](){ io_service.run(); });
	//ss_ptr->start();
	message msg;
	msg.command = 2;
	msg.business_type = 0;
	msg.app_id = 1;
	msg.body = "hello, server!";
	std::string cmd = "hello, server!";
	while (cmd != "exit") {
		ss_ptr->async_write(msg);
		std::cin >> cmd;
		msg.body.assign(cmd);
	}
	ss_ptr->close();
	t.join();
	return 0;
}