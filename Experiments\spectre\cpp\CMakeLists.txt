# CMakeList.txt for spectre_cpp
cmake_minimum_required(VERSION 3.15)
project(spectre_cpp CXX)

# --- C++ Standard ---
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# --- LibTorch Dependency ---
# ???????: ???????LibTorch??????
# ??????????????????? CMAKE_PREFIX_PATH ?????????
# ????????????????????????????????
set(CMAKE_PREFIX_PATH "E:/BaseLibrary/libtorch")
find_package(Torch REQUIRED)
if(NOT Torch_FOUND)
    message(FATAL_ERROR "LibTorch not found. Please set CMAKE_PREFIX_PATH.")
endif()
message(STATUS "Found LibTorch: ${TORCH_LIBRARIES}")

# --- Include Directories ---
# ???????????????????????????
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# --- Trading Module Sources ---
set(TRADING_SOURCES
    src/trading/Position.cpp
    src/trading/Portfolio.cpp
    src/trading/Event.cpp
    src/trading/StopModel.cpp
    src/trading/Metric.cpp
)

# --- Add Executable ---
# ???????????????
add_executable(spectre_main
    src/main.cpp
    src/Factor.cpp
    src/Engine.cpp
    src/Rolling.cpp
    src/Parallel.cpp
    src/RollingParallel.cpp
    ${TRADING_SOURCES}
)

# ???????????????
add_executable(test_simple
    src/test_simple.cpp
    src/Parallel.cpp
)

# Add trading module test executable
add_executable(test_trading
    src/test_trading.cpp
)

# --- Trading Module Library ---
# Create a separate library for trading module
add_library(spectre_trading STATIC
    ${TRADING_SOURCES}
)

# Link trading library with torch
target_link_libraries(spectre_trading PUBLIC ${TORCH_LIBRARIES})
target_include_directories(spectre_trading PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# --- Link Libraries ---
# ???????????????Torch??
target_link_libraries(spectre_main PRIVATE ${TORCH_LIBRARIES} spectre_trading)
target_link_libraries(test_simple PRIVATE ${TORCH_LIBRARIES})
target_link_libraries(test_trading PRIVATE spectre_trading)

# --- Final Setup for MSVC (Windows) ---
if(MSVC)
    # ????????????????Torch??DLL
    file(GLOB TORCH_DLLS "${TORCH_INSTALL_PREFIX}/lib/*.dll")
    add_custom_command(TARGET spectre_main POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${TORCH_DLLS}
        $<TARGET_FILE_DIR:spectre_main>)
endif()

message(STATUS "Project setup complete. You can now build the 'spectre_main' target.")
