#pragma once
#include <string>
#include <vector>
#include <map>
#include <PortfolioManager.h>
#include <patterns/singleton.hpp>
#include "PortfolioI.h"
#include <BusinessDataDef.h>

namespace bll
{
using namespace dal;
class PortfolioManagerI : public PortfolioManager, public kits::Singleton<PortfolioManagerI>
{
	friend class kits::Singleton<PortfolioManagerI>;
	PortfolioManagerI();
public:
	~PortfolioManagerI(void);

	bool Init();
	int GetPortfolioSize();
	std::string GetPortfolioName(int idx);
	std::string GetPortfolioName(const std::string& id);
	std::string GetPortfolioId(const std::string& name);
	Portfolio* GetPortfolio(const std::string& name);
	Portfolio* GetPortfolioById(const std::string& id);
	bool IsExistPortfolio(const std::string& name);

	bool AddPortfolio(const BasePortfolio& bp, RunMode mode = RunMode::live);
	bool CreatePortfolio(const std::string& name, const std::string& broker_id, PORTFOLIO_TYPE type,
		double units, double max_risk_degree, int save_factor, int positionsize, time_t datetime);

	bool ModifyPortfolio(const std::string& portfolio_id, const std::string& portfolio_name,
		const std::string& broker_id, PORTFOLIO_TYPE type, double units, double max_risk_degree, 
		int save_factor, int positionsize, time_t datetime);

	bool DeletePortfolio(const std::string& name);
	bool SavePortfolio();

	Portfolio* GetPortfolioByAccount(const std::string& acountid, const std::string& label, double qty, PositionSide side);
	std::string GetPositionSizeAdvise(TradePeriodType type = emShortPeriod, const std::string& mainidx = "000300.SH", PositionSide ps = PositionSide::Long, int positiontype = 5);
	float GetPositionSizeRate(const std::string& ps);
	BlockDataPtr GetAllHoldBlockData(int category = 0);
	void CheckPortfolioPositionSize() override;
	void AdjustPositionSize(float position);
	void ResizePositionForProfit() override;
	void CheckPortfolioAccount() override;
  int GetAllPositionSize(const std::string& label) override;

	PerformancePtr GetPerformance(const std::string& id) override;
	RiskPtr GetRisk(const std::string& id) override;
	void UpdatePortfolioStrategyInfo() override;
	std::array<std::string, 4> GetPortfolioStrategyInfo(const std::string& id) override;

protected:
	std::vector<PortfolioI> _pfs;
	BrokerAccountDataArrayPtr _badPtr;
	std::map<std::string, PerformancePtr> _pfms;
	std::map<std::string, RiskPtr> _risks;
	std::map<std::string, std::array<std::string, 4>> _dictPS;
};

}
