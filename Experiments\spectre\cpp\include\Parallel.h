#pragma once

#include <torch/torch.h>
#include <vector>
#include <functional>
#include <memory>
#include <unordered_map>
#include <tuple>
#include "Utils.h"

namespace Spectre {

// ============================================================================
// ParallelGroupBy - 快速并行分组操作
// ============================================================================

class ParallelGroupBy {
public:
    static bool GROUPBY_SORT_IN_GPU; // 如果GPU内存>20G可以启用

    explicit ParallelGroupBy(const torch::Tensor& keys);

    // 属性访问
    torch::Tensor padding_mask() const { return m_padding_mask; }

    // 核心操作
    torch::Tensor split(const torch::Tensor& data);
    torch::Tensor revert(const torch::Tensor& split_data, const std::string& dbg_str = "None");
    torch::Tensor create(torch::ScalarType dtype, double values, double nan_fill = std::numeric_limits<double>::quiet_NaN());

private:
    std::vector<int64_t> m_boundary;
    torch::Tensor m_sorted_indices;
    torch::Tensor m_padding_mask;
    torch::Tensor m_inverse_indices;
    int64_t m_width;
    int64_t m_groups;
    std::vector<int64_t> m_data_shape;
};

// ============================================================================
// DummyParallelGroupBy - 简化版分组操作
// ============================================================================

class DummyParallelGroupBy {
public:
    DummyParallelGroupBy(const std::vector<int64_t>& shape, torch::Device device, int64_t dim = -1);

    torch::Tensor padding_mask() const { return m_padding_mask; }
    torch::Tensor split(const torch::Tensor& data);
    torch::Tensor revert(const torch::Tensor& split_data, const std::string& dbg_str = "None");
    torch::Tensor create(torch::ScalarType dtype, double values, double nan_fill = std::numeric_limits<double>::quiet_NaN());

private:
    int64_t m_dim;
    torch::Tensor m_padding_mask;
};

// ============================================================================
// 统计函数声明
// ============================================================================

// 基础统计函数
torch::Tensor unmasked_sum(const torch::Tensor& data, const torch::Tensor& mask, int64_t dim = 1, bool inplace = false);
torch::Tensor unmasked_prod(const torch::Tensor& data, const torch::Tensor& mask, int64_t dim = 1, bool inplace = false);
torch::Tensor unmasked_mean(const torch::Tensor& data, const torch::Tensor& mask, int64_t dim = 1, bool inplace = false);
torch::Tensor unmasked_var(const torch::Tensor& data, const torch::Tensor& mask, int64_t dim = 1, int64_t ddof = 0);

// NaN安全统计函数
torch::Tensor nansum(const torch::Tensor& data, int64_t dim = 1, bool inplace = false);
torch::Tensor nanprod(const torch::Tensor& data, int64_t dim = 1, bool inplace = false);
torch::Tensor nanmean(const torch::Tensor& data, int64_t dim = 1, bool inplace = false);
torch::Tensor nanvar(const torch::Tensor& data, int64_t dim = 1, int64_t ddof = 0);
torch::Tensor nanstd(const torch::Tensor& data, int64_t dim = 1, int64_t ddof = 0);
torch::Tensor nanmax(const torch::Tensor& data, int64_t dim = 1);
torch::Tensor nanmin(const torch::Tensor& data, int64_t dim = 1);

// 掩码操作函数
torch::Tensor masked_last(const torch::Tensor& data, const torch::Tensor& mask, int64_t dim = 1, bool reverse = false);
torch::Tensor masked_first(const torch::Tensor& data, const torch::Tensor& mask, int64_t dim = 1);
torch::Tensor nanlast(const torch::Tensor& data, int64_t dim = 1, int64_t offset = 0);

// 数据处理函数
torch::Tensor pad_2d(const torch::Tensor& data, bool including_inf = false, bool including_nan = true);
torch::Tensor rankdata(const torch::Tensor& data, int64_t dim = 1, bool ascending = true, 
                      const std::string& method = "average", bool normalize = false);

// 高级统计函数
torch::Tensor unmasked_covariance(const torch::Tensor& x, const torch::Tensor& y, 
                                 const torch::Tensor& mask, int64_t dim = 1, int64_t ddof = 0);
torch::Tensor covariance(const torch::Tensor& x, const torch::Tensor& y, int64_t dim = 1, int64_t ddof = 0);
torch::Tensor pearsonr(const torch::Tensor& x, const torch::Tensor& y, int64_t dim = 1, int64_t ddof = 0);
torch::Tensor spearman(const torch::Tensor& rank_x, const torch::Tensor& rank_y, int64_t dim = 1);
std::tuple<torch::Tensor, torch::Tensor> linear_regression_1d(const torch::Tensor& x, const torch::Tensor& y, int64_t dim = 1);

// 分位数和其他函数
torch::Tensor quantile(const torch::Tensor& data, int64_t bins, int64_t dim = 1);
std::tuple<std::vector<torch::Tensor>, std::vector<std::pair<torch::Tensor, torch::Tensor>>> 
masked_kth_value_1d(const torch::Tensor& data, const torch::Tensor& universe_mask, 
                   const std::vector<double>& k_percents, bool even_mean = true,
                   const std::string& nan_policy = "omit", int64_t dim = 1);
torch::Tensor clamp_1d_(torch::Tensor& data, const torch::Tensor& min_val, const torch::Tensor& max_val);

} // namespace Spectre
