#pragma once

/**
 * @file Calendar.h
 * @brief Trading calendar for market events
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, <PERSON><PERSON>z<PERSON>. All rights reserved.
 * @license Apache 2.0
 */

#include <chrono>
#include <string>
#include <vector>
#include <unordered_map>

namespace Spectre {
namespace Trading {

/**
 * @brief Trading calendar for managing market events
 */
class Calendar {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using EventMap = std::unordered_map<std::string, std::vector<TimePoint>>;

    /**
     * @brief Constructor
     */
    Calendar() = default;

    /**
     * @brief Build calendar with daily events
     * @param start Start date
     * @param end End date
     * @param daily_events Map of event names to time strings
     * @param timezone Timezone string (default: "UTC")
     */
    void build(const std::string& start, const std::string& end,
               const std::unordered_map<std::string, std::string>& daily_events,
               const std::string& timezone = "UTC");

    /**
     * @brief Add a single event
     * @param event Event name
     * @param datetime Event time
     */
    void add_event(const std::string& event, const TimePoint& datetime);

    /**
     * @brief Remove all events on a specific date
     * @param date Date to remove events from
     */
    void remove_events(const TimePoint& date);

    /**
     * @brief Set a date as holiday (remove all events)
     * @param date Holiday date
     */
    void set_as_holiday(const TimePoint& date);

    /**
     * @brief Get current time in calendar timezone
     * @return Current time
     */
    TimePoint hr_now() const;

    /**
     * @brief Remove passed events for a specific event type
     * @param event_name Event name
     */
    void pop_passed(const std::string& event_name);

    /**
     * @brief Get today's next events
     * @return Map of event names to next occurrence times
     */
    std::unordered_map<std::string, TimePoint> today_next() const;

    /**
     * @brief Get all events
     * @return Reference to events map
     */
    const EventMap& events() const { return events_; }

    /**
     * @brief Get timezone
     * @return Timezone string
     */
    const std::string& timezone() const { return timezone_; }

protected:
    EventMap events_;
    std::string timezone_;

    /**
     * @brief Parse time string to duration
     * @param time_str Time string (e.g., "09:30:00")
     * @return Duration from midnight
     */
    std::chrono::duration<double> parse_time(const std::string& time_str) const;

    /**
     * @brief Parse date string to time point
     * @param date_str Date string
     * @return Time point
     */
    TimePoint parse_date(const std::string& date_str) const;
};

/**
 * @brief Chinese market calendar
 */
class CNCalendar : public Calendar {
public:
    /**
     * @brief Constructor
     * @param start Start date (optional)
     * @param pop_passed Whether to remove passed events
     */
    explicit CNCalendar(const std::string& start = "", bool pop_passed = true);

private:
    static const std::vector<std::string> closed_dates_;
    static const std::unordered_map<std::string, std::string> daily_events_;
};

/**
 * @brief Japanese market calendar
 */
class JPCalendar : public Calendar {
public:
    /**
     * @brief Constructor
     * @param start Start date (optional)
     * @param pop_passed Whether to remove passed events
     */
    explicit JPCalendar(const std::string& start = "", bool pop_passed = true);

private:
    static const std::vector<std::string> closed_dates_;
    static const std::unordered_map<std::string, std::string> daily_events_;
};

} // namespace Trading
} // namespace Spectre
