#pragma once

#include "Factor.h"
#include "Utils.h"

#include <torch/torch.h>
#include <memory>

namespace Spectre {

// Forward declarations
class FactorEngine;

// ============================================================================
// Filter Factors - Corresponding to Python's filter.py
// ============================================================================

// FilterFactor: Base class for filter factors
class FilterFactor : public CustomFactor {
public:
    FilterFactor(int win = 1, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 1;
    }

    // Override to return boolean tensor
    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override = 0;

protected:
    // Helper function to ensure boolean output
    torch::Tensor ensure_boolean(const torch::Tensor& input) {
        if (input.dtype() == torch::kBool) {
            return input;
        }
        return input.to(torch::kBool);
    }
};

// PlaceHolderFilter: Always returns True (no filtering)
class PlaceHolderFilter : public FilterFactor {
public:
    PlaceHolderFilter(const std::shared_ptr<BaseFactor>& like_factor)
        : FilterFactor(1, {like_factor}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("PlaceHolderFilter expects exactly one input for shape reference.");
        }
        
        torch::Tensor reference = inputs[0];
        return torch::ones(reference.sizes(), torch::TensorOptions().dtype(torch::kBool).device(reference.device()));
    }
};

// GreaterThanFilter: Filter for values greater than threshold
class GreaterThanFilter : public FilterFactor {
public:
    GreaterThanFilter(const std::shared_ptr<BaseFactor>& factor, float threshold)
        : FilterFactor(1, {factor}), m_threshold(threshold) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("GreaterThanFilter expects exactly one input.");
        }
        
        return inputs[0] > m_threshold;
    }

private:
    float m_threshold;
};

// LessThanFilter: Filter for values less than threshold
class LessThanFilter : public FilterFactor {
public:
    LessThanFilter(const std::shared_ptr<BaseFactor>& factor, float threshold)
        : FilterFactor(1, {factor}), m_threshold(threshold) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("LessThanFilter expects exactly one input.");
        }
        
        return inputs[0] < m_threshold;
    }

private:
    float m_threshold;
};

// EqualFilter: Filter for values equal to threshold
class EqualFilter : public FilterFactor {
public:
    EqualFilter(const std::shared_ptr<BaseFactor>& factor, float threshold, float tolerance = 1e-6f)
        : FilterFactor(1, {factor}), m_threshold(threshold), m_tolerance(tolerance) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("EqualFilter expects exactly one input.");
        }
        
        torch::Tensor diff = torch::abs(inputs[0] - m_threshold);
        return diff <= m_tolerance;
    }

private:
    float m_threshold;
    float m_tolerance;
};

// NotNullFilter: Filter for non-null (non-NaN) values
class NotNullFilter : public FilterFactor {
public:
    NotNullFilter(const std::shared_ptr<BaseFactor>& factor)
        : FilterFactor(1, {factor}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("NotNullFilter expects exactly one input.");
        }
        
        return ~inputs[0].isnan();
    }
};

// IsNullFilter: Filter for null (NaN) values
class IsNullFilter : public FilterFactor {
public:
    IsNullFilter(const std::shared_ptr<BaseFactor>& factor)
        : FilterFactor(1, {factor}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("IsNullFilter expects exactly one input.");
        }
        
        return inputs[0].isnan();
    }
};

// ============================================================================
// Logical Operation Factors
// ============================================================================

// AndFactor: Logical AND of two boolean factors
class AndFactor : public FilterFactor {
public:
    AndFactor(const std::shared_ptr<BaseFactor>& left, const std::shared_ptr<BaseFactor>& right)
        : FilterFactor(1, {left, right}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("AndFactor expects exactly two inputs.");
        }
        
        torch::Tensor left = ensure_boolean(inputs[0]);
        torch::Tensor right = ensure_boolean(inputs[1]);
        
        return left & right;
    }
};

// OrFactor: Logical OR of two boolean factors
class OrFactor : public FilterFactor {
public:
    OrFactor(const std::shared_ptr<BaseFactor>& left, const std::shared_ptr<BaseFactor>& right)
        : FilterFactor(1, {left, right}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("OrFactor expects exactly two inputs.");
        }
        
        torch::Tensor left = ensure_boolean(inputs[0]);
        torch::Tensor right = ensure_boolean(inputs[1]);
        
        return left | right;
    }
};

// NotFactor: Logical NOT of a boolean factor
class NotFactor : public FilterFactor {
public:
    NotFactor(const std::shared_ptr<BaseFactor>& factor)
        : FilterFactor(1, {factor}) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("NotFactor expects exactly one input.");
        }
        
        torch::Tensor input = ensure_boolean(inputs[0]);
        return ~input;
    }
};

// ============================================================================
// Ranking and Percentile Filters
// ============================================================================

// PercentileFilter: Filter based on percentile ranking
class PercentileFilter : public CrossSectionFactor {
public:
    PercentileFilter(const std::shared_ptr<BaseFactor>& factor, float min_percentile = 0.0f, float max_percentile = 100.0f)
        : CrossSectionFactor(1, {factor}), m_min_percentile(min_percentile), m_max_percentile(max_percentile) {
        if (min_percentile < 0.0f || max_percentile > 100.0f || min_percentile >= max_percentile) {
            throw std::runtime_error("Invalid percentile range");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("PercentileFilter expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate percentiles for each time step
        torch::Tensor min_threshold = torch::quantile(data, m_min_percentile / 100.0f, 0, true);
        torch::Tensor max_threshold = torch::quantile(data, m_max_percentile / 100.0f, 0, true);
        
        return (data >= min_threshold) & (data <= max_threshold);
    }

private:
    float m_min_percentile;
    float m_max_percentile;
};

// TopKFilter: Filter for top K values
class TopKFilter : public CrossSectionFactor {
public:
    TopKFilter(const std::shared_ptr<BaseFactor>& factor, int k)
        : CrossSectionFactor(1, {factor}), m_k(k) {
        if (k <= 0) {
            throw std::runtime_error("k must be positive");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("TopKFilter expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Get top k values for each time step
        auto topk_result = torch::topk(data, m_k, 0, true, false);
        torch::Tensor topk_values = std::get<0>(topk_result);
        auto min_result = topk_values.min(0);
        torch::Tensor min_topk = std::get<0>(min_result);
        
        return data >= min_topk.unsqueeze(0);
    }

private:
    int m_k;
};

} // namespace Spectre
