#pragma once
#include <string>
#include <DataDef.h>
#include <BusinessDataDef.h>
#include <CTP\ThostFtdcUserApiStruct.h>
#include <patterns/observable.hpp>
#include <boost/function.hpp>
#include <Instrument.h>

namespace bll
{
#ifdef _EXPORT_GTI
#	define DLL_GTI_TS __declspec( dllexport )
#else
#	define DLL_GTI_TS __declspec( dllimport )
#endif
enum class ModelType { torch, lgbm, unkown };

inline std::string get_expr_id(const std::string& id, int idx) { return fmt::format("{:s}{:d}", id, idx); }

typedef boost::function<int(std::string, StrategyOrderType, int, std::string)> ExecOrderInteractiveCallback;

DLL_GTI_TS ModelType GetModelType(const std::string& model_id);
DLL_GTI_TS double Predict(const std::string& label, const std::string& model_id);
DLL_GTI_TS double Predict(const std::string& model_id, std::vector<float>& data, std::vector<int>& embedding);
DLL_GTI_TS double Gbdt(const std::string& model_id, std::vector<float>& data, int embedding=-1);
DLL_GTI_TS bool CloudProxyStart();
DLL_GTI_TS void CloudProxyStop();
DLL_GTI_TS void TimerTaskStart();
DLL_GTI_TS void TimerTaskStop();
DLL_GTI_TS void UpdateFilterBlock();
DLL_GTI_TS std::vector<TaskData>* GetTaskData();
DLL_GTI_TS void SaveTaskData();

DLL_GTI_TS double SvmPredict(const std::string& id, const svm_node* x_ptr);
//DLL_GTI_TS bool set_subjective_factor(const std::string& key, double val);
//DLL_GTI_TS double get_subjective_factor(const std::string& key);
DLL_GTI_TS void update_expression(const std::string& id = "");
DLL_GTI_TS bool compile_expression(const std::string& expression);
DLL_GTI_TS std::string compile_error_case();
DLL_GTI_TS bool run_expression(dal::InstrumentPtr itm_ptr, const std::string& expression, BarSize barsize, ExprContextPtr ctxt_ptr = nullptr);
DLL_GTI_TS BlockDataPtr run_expression_snapshot(unsigned short group, const std::string& id, BarSize barsize);
DLL_GTI_TS BlockDataPtr run_block_expression_snapshot(unsigned short group, const std::string& stk_id, const std::string& blk_id, BarSize barsize);
DLL_GTI_TS double run_expression_test(const std::string& lf, const std::string& sf, const std::string& ct, const std::string& program);

// old api
DLL_GTI_TS bool factor_check(dal::InstrumentPtr itm_ptr, const std::string& expression, BarSize barsize, ExprContextPtr ctxt_ptr = nullptr);
DLL_GTI_TS BlockDataPtr factor_filter_snapshot(unsigned short group, const std::string& expression, BarSize barsize);
DLL_GTI_TS BlockDataPtr factor_block_check_snapshot(unsigned short group, const std::string& expr_stk, const std::string& expr_blk, BarSize barsize);
}
