﻿// DlgInstrumentView.cpp: 实现文件
//

#include "stdafx.h"
#include "DlgInstrumentView.h"
#include "DlgSetRangeValue.h"
#include "afxdialogex.h"
#include "resource.h"
#include <DataHub.h>
#include <common.hpp>
#include <Instrument.h>
#include <TradeSystem.h>
#include <Strategy.h>
#include "LabToolsDlg.h"
#include "../DataHub/Settings.h"
#include <mkdbs.hpp>
// DlgInstrumentView 对话框
char* DlgInstrumentView::_szHeaders[factorlistCols] = { "NO.", "代码", "名称", "涨幅", "现价", "L-Atr", "S-Atr", "L/S", "T-Atr", "L-%", "S-%", "T-%", "sTrd", "行业", "NL-Atr", "NL%", "NS-Atr", "NS%" };
char* DlgInstrumentView::_szMarkStar[6] = { "☆☆☆☆☆", "★☆☆☆☆", "★★☆☆☆", "★★★☆☆", "★★★★☆", "★★★★★" };
COLORREF DlgInstrumentView::_szMarkColor[6] = { RGB(0, 0, 0), RGB(220, 20, 60), RGB(218, 165, 32), RGB(0, 0, 139), RGB(0, 128, 0), RGB(255, 140, 0) };
int DlgInstrumentView::nCurCol = 7;
bool DlgInstrumentView::bFlag = false;

IMPLEMENT_DYNAMIC(DlgInstrumentView, CDialogEx)

DlgInstrumentView::DlgInstrumentView(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_INSTRUMENT, pParent)
{

}

DlgInstrumentView::~DlgInstrumentView()
{
}

void DlgInstrumentView::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST_FILTER_RESULT, m_listFilterResult);
}


BEGIN_MESSAGE_MAP(DlgInstrumentView, CDialogEx)
	ON_MESSAGE(WM_QUICKLIST_GETLISTITEMDATA, OnGetListItem)
	ON_NOTIFY(NM_DBLCLK, IDC_LIST_FILTER_RESULT, &DlgInstrumentView::OnNMDblclkListFilterResult)
	ON_NOTIFY(LVN_COLUMNCLICK, IDC_LIST_FILTER_RESULT, &DlgInstrumentView::OnLvnColumnclickListSec)
	ON_NOTIFY(NM_CLICK, IDC_LIST_FILTER_RESULT, &DlgInstrumentView::OnNMClickListFilterResult)
END_MESSAGE_MAP()


// DlgInstrumentView 消息处理程序
void DlgInstrumentView::InitFilterResultList()
{
	ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd, LVS_EX_FULLROWSELECT, LVS_EX_FULLROWSELECT);
	ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd, LVS_EX_SUBITEMIMAGES, LVS_EX_SUBITEMIMAGES);
	//ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd, LVS_EX_CHECKBOXES, LVS_EX_CHECKBOXES);
	ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd, LVS_EX_HEADERDRAGDROP, LVS_EX_HEADERDRAGDROP);
	ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd, LVS_EX_DOUBLEBUFFER, LVS_EX_DOUBLEBUFFER);
	ListView_SetExtendedListViewStyleEx(m_listFilterResult.m_hWnd, LVS_EX_GRIDLINES, LVS_EX_GRIDLINES);
	//                                 0      1       2   3   4   5   6   7   8   9  10  11  12  13  14 15, 16, 17
	int nColWidths[factorlistCols] = { 30,    65,    65, 40,  55, 50, 50, 45,  40, 46, 50, 50, 50, 60, 50, 50, 50, 50 };
	//char*  szHeaders[frlistCols] = {"NO.", "代码", "名称", "涨幅", "现价", "POS", "Grd", "B-Wd", "B-Exp", "RSI", "fTrd", "sTrd", "行业", "策略", "标记"};
	// add columns
	DWORD fmt;
	for (int i = 0; i < factorlistCols; i++) {
		fmt = /*(i == 1) ? LVCFMT_LEFT : */LVCFMT_RIGHT;
		fmt = (i == 1 || i == 2 || i == 11 || i == 12 || i == 13) ? LVCFMT_CENTER : fmt;
		//fmt = (i == 18) ? LVCFMT_LEFT : fmt;
		m_listFilterResult.InsertColumn(i, _szHeaders[i], fmt, nColWidths[i]);
	}

	m_listFilterResult.EnableToolTips(TRUE);
}


BOOL DlgInstrumentView::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	InitFilterResultList();

	_whis.clear();
	//_whis.push_back("WFI999.IX");
	//_whis.push_back("WYZL99.IX");
	//_whis.push_back("WHSL99.IX");
	//_whis.push_back("WHGB99.IX");
	//_whis.push_back("WYSB99.IX");

	return TRUE;
}

LRESULT DlgInstrumentView::OnGetListItem(WPARAM wParam, LPARAM lParam)
{
	ASSERT((HWND)wParam == m_listFilterResult.GetSafeHwnd());

	CQuickList::CListItemData* data = (CQuickList::CListItemData*) lParam;

	int item = data->GetItem();
	int subItem = data->GetSubItem();

	data->m_text = _listText[item].txt[subItem];
	switch (subItem)
	{
	case 0:
		data->m_text.Format(_T("%d"), (item + 1));
		break;
	case 1:
	case 2:
	{
		int clr = std::atoi(_listText[item].txt[factorlistCols]);
		if (clr > 0 && clr < 6)
		{
			data->m_colors.m_textColor = _szMarkColor[clr];
			data->m_textStyle.m_bold = true;
		}
	}
	break;
	case 3://涨幅
	case 7:
	case 9:
	case 11:
	case 15:
	case 17:
	{
		double riserate = std::atof(_listText[item].txt[subItem]);
		int clr = (int)(abs(riserate) * 30);
		if (clr > 255) clr = 255;
		clr = 255 - clr;
		if (riserate >= 0)
		{
			data->m_colors.m_textColor = RGB(10, 10, 10);
			data->m_colors.m_backColor = RGB(255, clr, clr);
		}
		else
		{
			data->m_colors.m_textColor = RGB(10, 10, 10);
			data->m_colors.m_backColor = RGB(clr, 255, clr);
		}
		data->m_textStyle.m_bold = true;
	}
	break;
	case 10:
	{
		double riserate = std::atof(_listText[item].txt[subItem]);
		int clr = (int)(abs(riserate) * 300);
		if (clr > 255) clr = 255;
		clr = 255 - clr;
		if (riserate >= 0)
		{
			data->m_colors.m_textColor = RGB(10, 10, 10);
			data->m_colors.m_backColor = RGB(255, clr, clr);
		}
		else
		{
			data->m_colors.m_textColor = RGB(10, 10, 10);
			data->m_colors.m_backColor = RGB(clr, 255, clr);
		}
		data->m_textStyle.m_bold = true;
	}
	break;
	//case 7://B-Wd
	{
		//int bwd = std::atoi(_listText[item].txt[subItem]);
		//int clr = (int)(abs(bwd) * 2);
		//if (clr > 255) clr = 255;
		//clr = 255 - clr;
		//if (bwd >= 0)
		//{
		//	data->m_colors.m_textColor = RGB(10, 10, 10);
		//	data->m_colors.m_backColor = RGB(255, clr, clr);
		//}
		//else
		//{
		//	data->m_colors.m_textColor = RGB(10, 10, 10);
		//	data->m_colors.m_backColor = RGB(clr, 255, clr);
		//}
		//data->m_textStyle.m_bold = true;
	}
	break;
	case 8://B-Exp
	{
		//int bexp = std::atoi(_listText[item].txt[subItem]);
		//int clr = (int)(abs(bexp) * 2);
		//if (clr > 255) clr = 255;
		//clr = 255 - clr;
		//if (bexp >= 0)
		//{
		//	data->m_colors.m_textColor = RGB(10, 10, 10);
		//	data->m_colors.m_backColor = RGB(255, clr, clr);
		//}
		//else
		//{
		//	data->m_colors.m_textColor = RGB(10, 10, 10);
		//	data->m_colors.m_backColor = RGB(clr, 255, clr);
		//}
		//data->m_textStyle.m_bold = true;
	}
	break;
	//case 9://maRSI
	//{
	//	//double riserate = std::atof(_listText[item].txt[subItem]) - 50.0;
	//	//int clr = (int)(abs(riserate) * 8);
	//	//if (clr > 255) clr = 255;
	//	//clr = 255 - clr;
	//	//if (riserate >= 0)
	//	//{
	//	//	data->m_colors.m_textColor = RGB(10, 10, 10);
	//	//	data->m_colors.m_backColor = RGB(255, clr, clr);
	//	//}
	//	//else
	//	//{
	//	//	data->m_colors.m_textColor = RGB(10, 10, 10);
	//	//	data->m_colors.m_backColor = RGB(clr, 255, clr);
	//	//}
	//}
	//break;
	//case 10://NEW
	//	break;
	case 16:
	{
		//if (data->m_text.Compare(_szMarkStar[0]) == 0)
		//	data->m_colors.m_textColor = RGB(240, 240, 240);
		//else
		//	data->m_colors.m_textColor = RGB(255, 0, 0);
	}
	break;
	}

	return 0;
}

void DlgInstrumentView::FillFilterResultList(BlockDataPtr blk_ptr)
{
	if (blk_ptr.get() != nullptr) {
		_blk_ptr = blk_ptr;
	}
	
	if (_blk_ptr.get() == nullptr) {
		m_listFilterResult.SetItemCount(0);
		return;
	}

	_listText.resize(_blk_ptr->size());

	m_listFilterResult.SetRedraw(FALSE);
	m_listFilterResult.SetItemCount(_blk_ptr->size());

	LabToolsDlg* pApp = (LabToolsDlg*)AfxGetApp()->m_pMainWnd;
  std::map<std::string, bll::BtRpt>* btrs_ptr = &pApp->_optiman_atrs;

	time_t ltd = dal::dh().GetCalendars().get_lastest_trading_date();
	int row = 0;
	std::string label;
	for (auto it: *_blk_ptr) {
		label = dal::dh().GetLabel(it);
		QuoteData* qd_ptr = dal::dh().GetQuoteData(label);
		if (qd_ptr == nullptr) {
			continue;
		}

		if (qd_ptr->_update == 0 || qd_ptr->_update < ltd) {
			dal::dh().GetNewQuoteData(label);
			if (qd_ptr->_update>0) {
			  dal::mkdbs::instance().update_quote_data(label, *qd_ptr);
			}
		}

		dal::InstrumentPtr it_ptr = dal::getInstrument(label, 0);
		if (it_ptr.get() == nullptr) continue;

		int col = 0;
		//序号
		//sprintf_s(_listText[row].txt[col++], 64, "%d", row+1);
		col++;
		//代码
		sprintf_s(_listText[row].txt[col++], 64, "%s", label.c_str());
		//名称
		sprintf_s(_listText[row].txt[col++], 64, "%s", dal::dh().GetSecName(label).c_str());

		//涨跌幅
		double riserate = 0.0;
		if (qd_ptr->_preclose > 0.00001 && qd_ptr->_new > 0.00001)
		{
			riserate = (qd_ptr->_new - qd_ptr->_preclose) / qd_ptr->_preclose;
		}
		sprintf_s(_listText[row].txt[col++], 64, "%.2f", riserate * 100);

		//现价
		sprintf_s(_listText[row].txt[col++], 64, "%.2f", qd_ptr->_new);

		//long range atr
		if (it_ptr->_md_ptr->long_range_atr == 0.0) {
			it_ptr->_md_ptr->long_range_atr = dal::dh().GetDefaultRangeBarAtr(label, it_ptr->_long_fd_ptr->barSize, true, true);
		}
		sprintf_s(_listText[row].txt[col++], 32, "%.3f", it_ptr->_md_ptr->long_range_atr);

		//short range atr
		if (it_ptr->_md_ptr->short_range_atr == 0.0) {
			it_ptr->_md_ptr->short_range_atr = dal::dh().GetDefaultRangeBarAtr(label, it_ptr->_range_fd_ptr->barSize, true, true);
		}
		sprintf_s(_listText[row].txt[col++], 32, "%.3f", it_ptr->_md_ptr->short_range_atr);

		//long/short range atr
		sprintf_s(_listText[row].txt[col++], 32, "%.2f", it_ptr->_md_ptr->long_range_atr / it_ptr->_md_ptr->short_range_atr);

		//TATR
		sprintf_s(_listText[row].txt[col++], 32, "%.1f", it_ptr->_long_fd_ptr->factors[FS_TATR].value());

		//B-Exp
		sprintf_s(_listText[row].txt[col++], 32, "%.2f", it_ptr->_md_ptr->long_range_atr / it_ptr->Price() * 100.0);
		sprintf_s(_listText[row].txt[col++], 32, "%.2f", it_ptr->_md_ptr->short_range_atr / it_ptr->Price() * 100.0);
		sprintf_s(_listText[row].txt[col++], 32, "%.2f", it_ptr->_long_fd_ptr->factors[FS_TATR].value() / it_ptr->Price() * 100.0);

		//SLOW TREND VALUE
		sprintf_s(_listText[row].txt[col++], 64, "%.0f", it_ptr->_long_fd_ptr->factors[FS_TREND_VALUE].value());

		//行业
		sprintf_s(_listText[row].txt[col++], 64, "%s", dal::dh().GetSecIndustry(label).c_str());

		auto iter = btrs_ptr->find(label);
		if (btrs_ptr != nullptr && iter != btrs_ptr->end()) {
			//new long range atr
			sprintf_s(_listText[row].txt[col++], 64, "%.2f", iter->second.long_atr);
		  sprintf_s(_listText[row].txt[col++], 32, "%.2f", 100*(iter->second.long_atr / it_ptr->_md_ptr->long_range_atr - 1));

			//short range atr
			sprintf_s(_listText[row].txt[col++], 64, "%.2f", iter->second.short_atr);
		  sprintf_s(_listText[row].txt[col++], 32, "%.2f", 100*(iter->second.short_atr / it_ptr->_md_ptr->short_range_atr - 1));
    } else {
			//new long range atr
			sprintf_s(_listText[row].txt[col++], 64, "--.--");
		  sprintf_s(_listText[row].txt[col++], 32, "--.--");

			//short range atr
			sprintf_s(_listText[row].txt[col++], 64, "--.--");
		  sprintf_s(_listText[row].txt[col++], 32, "--.--");
    }

		row++;
	}

	SortColumn();

	m_listFilterResult.SetRedraw(TRUE);
	m_listFilterResult.Invalidate();
	m_listFilterResult.UpdateWindow();
}

//排序比较函数
bool compaire_col(const DlgInstrumentView::ListText& txt1, const DlgInstrumentView::ListText& txt2)
{
	if (DlgInstrumentView::nCurCol == 0)
	{
		return dal::sort_compaire_func("", txt1.txt[1], txt2.txt[1]);
	}
	else if (DlgInstrumentView::nCurCol >= 3 && DlgInstrumentView::nCurCol <= 13)
	{
		double d1 = std::atof(txt1.txt[DlgInstrumentView::nCurCol]);
		double d2 = std::atof(txt2.txt[DlgInstrumentView::nCurCol]);
		if (d1 < d2)	//降序
			return DlgInstrumentView::bFlag;
		else
			return !DlgInstrumentView::bFlag;
	}
	else
	{
		if (_stricmp(txt1.txt[DlgInstrumentView::nCurCol], txt2.txt[DlgInstrumentView::nCurCol]) < 0)
			return DlgInstrumentView::bFlag;
		else
			return !DlgInstrumentView::bFlag;
	}

	return DlgInstrumentView::bFlag;
}


void DlgInstrumentView::OnLvnColumnclickListSec(NMHDR* pNMHDR, LRESULT* pResult)
{
	LPNMLISTVIEW pNMLV = reinterpret_cast<LPNMLISTVIEW>(pNMHDR);

	if (pNMLV->iSubItem == 5) {
		if (MessageBox("您要重新计算所有当前列的Long Range Atr参数吗？", "提示", MB_YESNO|MB_ICONQUESTION) == IDYES) {
			LabToolsDlg* pApp = (LabToolsDlg*)AfxGetApp()->m_pMainWnd;
			BlockDataPtr blk_ptr = dal::dh().GetBlockData(pApp->_btp.block_name);
			if (blk_ptr == nullptr || blk_ptr->empty()) return;
			for (auto idx: *blk_ptr) {
				std::string label = dal::dh().GetLabel(idx);
				BarSize barsize = BarSize::day;
				if (!dal::dh().IsFutures(label)) {
					barsize = BarSize::week;
				}
				double atr = dal::dh().GetRangeBarAtr(label, barsize, true);
				if (atr > 0.0) {
					dal::InstrumentPtr it_ptr = dal::getInstrument(label);
					it_ptr->_md_ptr->long_range_atr = atr;
				}
			}
		}
		FillFilterResultList();
		return;
	}

	if (pNMLV->iSubItem == 6) {
		if (MessageBox("您要重新计算所有当前列的Short Range Atr参数吗？", "提示", MB_YESNO | MB_ICONQUESTION) == IDYES) {
			LabToolsDlg* pApp = (LabToolsDlg*)AfxGetApp()->m_pMainWnd;
			BlockDataPtr blk_ptr = dal::dh().GetBlockData(pApp->_btp.block_name);
			if (blk_ptr == nullptr || blk_ptr->empty()) return;
			for (auto idx: *blk_ptr) {
				std::string label = dal::dh().GetLabel(idx);
				double atr = dal::dh().GetRangeBarAtr(label, BarSize::range, true);
				if (atr > 0.0) {
					dal::InstrumentPtr it_ptr = dal::getInstrument(label);
					it_ptr->_md_ptr->short_range_atr = atr;
				}
			}
		}
		FillFilterResultList();
		return;
	}

	if (nCurCol != pNMLV->iSubItem) {
		nCurCol = pNMLV->iSubItem;
	}
	else {
		bFlag = !bFlag;
	}

	FillFilterResultList();

	*pResult = 0;
}

//列排序
void DlgInstrumentView::SortColumn()
{
	if (nCurCol < 0 || nCurCol > factorlistCols) return;
#ifndef _DEBUG
	std::sort(_listText.begin(), _listText.end(), compaire_col);
#endif
}

void DlgInstrumentView::OnOK()
{
	//CDialogEx::OnOK();
}


void DlgInstrumentView::OnCancel()
{
	//CDialogEx::OnCancel();
}


void DlgInstrumentView::OnSize(UINT nType, int cx, int cy)
{
	CDialogEx::OnSize(nType, cx, cy);

	if (m_listFilterResult.GetSafeHwnd()) {
		CRect rect;
		this->GetWindowRect(&rect);
		ScreenToClient(rect);
		m_listFilterResult.MoveWindow(rect);
	}
}

std::string DlgInstrumentView::GetPrevLabel()
{
	m_nCurSel--;
	if (m_nCurSel < 0) {
		m_nCurSel = 0;
	}

	if (_labels_sel == 0) {
		CString label = m_listFilterResult.GetItemText(m_nCurSel, 1);
		return std::string((LPCSTR)label);
	}
	else {
		if (m_nCurSel < 0 || m_nCurSel >= _whis.size()) {
			m_nCurSel = 0;
		}

		return _whis[m_nCurSel];
	}

	return "";
}

std::string DlgInstrumentView::GetNextLabel()
{
	m_nCurSel++;
	if (_labels_sel == 0) {
		if (m_nCurSel >= m_listFilterResult.GetItemCount()) {
			if (MessageBox("已经到最后一个，是否从头开始？", "提示", MB_YESNO | MB_ICONQUESTION) == IDYES) {
				m_nCurSel = 0;
			}
			else {
				m_nCurSel = m_listFilterResult.GetItemCount() - 1;
			}
		}

		CString label = m_listFilterResult.GetItemText(m_nCurSel, 1);

		return std::string((LPCSTR)label);
	}
	else {
		if (m_nCurSel < 0 || m_nCurSel >= _whis.size()) {
			m_nCurSel = 0;
		}

		return _whis[m_nCurSel];
	}

	return "";
}

void DlgInstrumentView::OnNMDblclkListFilterResult(NMHDR* pNMHDR, LRESULT* pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	if (_blk_ptr.get() == nullptr) {
		return;
	}

	m_nCurSel = pNMItemActivate->iItem;

	if (m_nCurSel >= 0 && m_nCurSel < _blk_ptr->size()) {
		CString label = m_listFilterResult.GetItemText(m_nCurSel, 1);
		if (pNMItemActivate->iSubItem == 5) {
			if (MessageBox("您需要重新计算Long Range Atr吗？", "提示", MB_YESNO | MB_ICONQUESTION) == IDNO) {
				return;
			}
			BarSize barsize = BarSize::day;
			if (!dal::dh().IsFutures((LPCSTR)label)) {
				barsize = BarSize::week;
			}
			double atr = dal::dh().GetRangeBarAtr((LPCSTR)label, barsize, true);
			if (atr > 0.0) {
				dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
				it_ptr->_md_ptr->long_range_atr = atr;
				FillFilterResultList();
				//dal::dh().SaveBaseDataEx(true);
			}
		}
		else if (pNMItemActivate->iSubItem == 6) {
			if (MessageBox("您需要重新计算Short Range Atr吗？", "提示", MB_YESNO | MB_ICONQUESTION) == IDNO) {
				return;
			}
			double atr = dal::dh().GetRangeBarAtr((LPCSTR)label, BarSize::range, true);
			if (atr > 0.0) {
				dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
				it_ptr->_md_ptr->short_range_atr = atr;
				FillFilterResultList();
				//dal::dh().SaveBaseDataEx(true);
			}
		}
		else if (pNMItemActivate->iSubItem == 9 || pNMItemActivate->iSubItem == 10) {
			if (MessageBox("您需要手动修改Range Atr吗？", "提示", MB_YESNO | MB_ICONQUESTION) == IDNO) {
				return;
			}
			DlgSetRangeValue dlg;
			double range = 0.0;
			dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
			if (it_ptr->type == Futures) {
				if (pNMItemActivate->iSubItem == 10) {
					range = it_ptr->_md_ptr->short_range_atr;
				}
				else {
					range = it_ptr->_md_ptr->long_range_atr;
				}
			}
			else {
				if (pNMItemActivate->iSubItem == 10) {
					range = it_ptr->_md_ptr->short_range_atr;
				}
				else {
					range = it_ptr->_md_ptr->long_range_atr;
				}
			}
			dlg.m_sValue.Format("%.3f", range);
			if (dlg.DoModal() != IDOK) {
				return;
			}

			range = boost::lexical_cast<double>((LPCSTR)dlg.m_sValue);
			if (range < 0.001 && range != 0.0) {
				MessageBox("您设置的Range Bar参数值太小！", "提示", MB_OK | MB_ICONWARNING);
				return;
			}

			if (MessageBox("您确定要修改Range Bar的参数值吗？", "提示", MB_YESNO | MB_ICONQUESTION) == IDYES) {
				dal::InstrumentPtr it_ptr = dal::getInstrument((LPCSTR)label);
				if (it_ptr->type == Futures) {
					if (pNMItemActivate->iSubItem == 10) {
						it_ptr->_md_ptr->short_range_atr = range;
					}
					else {
						it_ptr->_md_ptr->long_range_atr = range;
					}
				}
				else {
					if (pNMItemActivate->iSubItem == 10) {
						it_ptr->_md_ptr->short_range_atr = range;
					}
					else {
						it_ptr->_md_ptr->long_range_atr = range;
					}
				}
				FillFilterResultList();
			}
		}
		else {
			if (label.IsEmpty()) {
				return;
			}
			_labels_sel = 0;
			((LabToolsDlg*)GetParent())->SetTrendChartLabel((LPCSTR)label, boost::bind(&DlgInstrumentView::GetPrevLabel, this), boost::bind(&DlgInstrumentView::GetNextLabel, this));
		}
	}
	*pResult = 0;
}

void DlgInstrumentView::OnNMClickListFilterResult(NMHDR* pNMHDR, LRESULT* pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	m_nCurSel = pNMItemActivate->iItem;

	if (m_nCurSel >= 0 && m_nCurSel < _blk_ptr->size()) {
		((LabToolsDlg*)GetParent())->m_sBindLabel = m_listFilterResult.GetItemText(m_nCurSel, 1);
		((LabToolsDlg*)GetParent())->UpdateData(FALSE);
	}

	*pResult = 0;
}
