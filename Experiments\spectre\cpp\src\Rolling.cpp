#include "Rolling.h"
#include "Utils.h"
#include <cmath>
#include <algorithm>

namespace Spectre {

// Static member initialization
int Rolling::s_split_multiplier = 1;

// ============================================================================
// Rolling Implementation
// ============================================================================

Rolling::Rolling(const torch::Tensor& data, int window, const torch::Tensor& adjustments)
    : m_window(window), m_adjustments(adjustments) {
    
    // Create unfolded data with padding
    m_values = unfold(data, window);
    
    // Initialize splits for memory management
    initialize_splits();
    
    // Handle adjustments if provided
    if (adjustments.defined()) {
        Rolling rolling_adj(m_adjustments, window, torch::Tensor());
        m_adjustments = rolling_adj.m_values;
        // m_adjustment_last would need special handling for last_nonnan
    }
}

torch::Tensor Rolling::unfold(const torch::Tensor& x, int window, float fill_value) {
    // Create padding with NaN values
    torch::Tensor nan_stack = torch::full({x.size(0), window - 1}, fill_value, x.options());
    
    // Concatenate padding with original data
    torch::Tensor padded_x = torch::cat({nan_stack, x}, 1);
    
    // Use unfold to create rolling windows
    return padded_x.unfold(1, window, 1);
}

void Rolling::initialize_splits() {
    // Calculate memory usage and determine split strategy
    auto memory_usage = static_cast<double>(m_values.numel() * m_window) / std::pow(1024.0, 3);
    memory_usage *= s_split_multiplier;
    
    int step = std::max(static_cast<int>(m_values.size(1) / memory_usage), 1);
    
    // Create boundary points
    std::vector<int64_t> boundary;
    for (int64_t i = 0; i < m_values.size(1); i += step) {
        boundary.push_back(i);
    }
    boundary.push_back(m_values.size(1));
    
    // Create split ranges
    m_splits.clear();
    for (size_t i = 0; i < boundary.size() - 1; ++i) {
        m_splits.emplace_back(boundary[i], boundary[i + 1]);
    }
}

torch::Tensor Rolling::adjust(int64_t start, int64_t end) const {
    torch::Tensor slice = m_values.slice(1, start, end);
    
    if (m_adjustments.defined()) {
        torch::Tensor adj_slice = m_adjustments.slice(1, start, end);
        torch::Tensor adj_last_slice = m_adjustment_last.slice(1, start, end);
        return slice * adj_slice / adj_last_slice;
    } else {
        return slice;
    }
}

// ============================================================================
// Aggregation Functions
// ============================================================================

torch::Tensor Rolling::nanmean(int64_t dim) const {
    return agg([dim](const torch::Tensor& x) {
        return Spectre::nanmean(x, dim, false);
    });
}

torch::Tensor Rolling::nansum(int64_t dim) const {
    return agg([dim](const torch::Tensor& x) {
        return Spectre::nansum(x, dim, false);
    });
}

torch::Tensor Rolling::nanstd(int64_t dim, int64_t ddof) const {
    return agg([dim, ddof](const torch::Tensor& x) {
        return Spectre::nanstd(x, dim, false, static_cast<int>(ddof));
    });
}

torch::Tensor Rolling::nanmax(int64_t dim) const {
    return agg([dim](const torch::Tensor& x) {
        return Spectre::nanmax(x, dim, false);
    });
}

torch::Tensor Rolling::nanmin(int64_t dim) const {
    return agg([dim](const torch::Tensor& x) {
        return Spectre::nanmin(x, dim, false);
    });
}

torch::Tensor Rolling::nanprod(int64_t dim) const {
    return agg([dim](const torch::Tensor& x) {
        // PyTorch doesn't have nanprod, implement manually
        torch::Tensor mask = (~x.isnan()).to(x.dtype());
        torch::Tensor zero_nans = x.nan_to_num(1.0);
        return zero_nans.prod(dim);
    });
}

torch::Tensor Rolling::nanvar(int64_t dim, int64_t ddof) const {
    return agg([dim, ddof](const torch::Tensor& x) {
        torch::Tensor mask = ~x.isnan();
        torch::Tensor zero_nans = x.nan_to_num(0.0);
        
        torch::Tensor count_val = mask.sum(dim, true).to(x.dtype());
        torch::Tensor mean_val = zero_nans.sum(dim, true) / count_val.clamp_min(1.0);
        
        torch::Tensor diff = zero_nans - mean_val;
        torch::Tensor var_val = (diff * diff * mask.to(x.dtype())).sum(dim, false) / 
                               (count_val.squeeze(dim) - ddof).clamp_min(1.0);
        
        return var_val;
    });
}

// ============================================================================
// Position-based Access
// ============================================================================

torch::Tensor Rolling::loc(int64_t i) const {
    if (i == -1) {
        // Last doesn't need adjustment, return directly
        return m_values.select(2, -1);
    }
    
    return agg([i](const torch::Tensor& x) {
        return x.select(2, i);
    });
}

torch::Tensor Rolling::first() const {
    return loc(0);
}

torch::Tensor Rolling::last() const {
    return loc(-1);
}

torch::Tensor Rolling::last_nonnan(int offset) const {
    return agg([offset](const torch::Tensor& x) {
        // Find the last non-NaN value in each window
        torch::Tensor mask = ~x.isnan();
        torch::Tensor indices = torch::arange(x.size(2), x.options().dtype(torch::kLong))
                               .unsqueeze(0).unsqueeze(0).expand_as(x);
        
        // Set NaN positions to -1
        indices = indices.masked_fill(~mask, -1);
        
        // Find the maximum index (last non-NaN position) for each window
        auto max_indices = std::get<1>(indices.max(2));
        
        // Apply offset
        max_indices = (max_indices - offset).clamp_min(0);
        
        // Gather the corresponding values
        return x.gather(2, max_indices.unsqueeze(2)).squeeze(2);
    });
}

} // namespace Spectre
