/* Copyright (C) 2013 Interactive Brokers LLC. All rights reserved. This code is subject to the terms
 * and conditions of the IB API Non-Commercial License or the IB API Commercial License, as applicable. */

#include "StdAfx.h"

#include "EClientSocket.h"
#include <IB/TwsSocketClientErrors.h>
#include <IB/EWrapper.h>
#include <boost/enable_shared_from_this.hpp>
//#include <afxsock.h>

///////////////////////////////////////////////////////////
// MySocket definition
/*
class EClientSocket::MySocket //: public CAsyncSocket
{
public:
	MySocket( EClientSocket *pClient);
	void OnConnect( int i);
	void OnReceive( int i);
	void OnSend( int i);
	void OnClose( int i);
private:
	EClientSocket *m_pClient;
};


///////////////////////////////////////////////////////////
// MySocket implementation

EClientSocket::MySocket::MySocket( EClientSocket *pClient)
{
	m_pClient = pClient;
}

void EClientSocket::MySocket::OnConnect( int i)
{
	m_pClient->onConnect( i);
}

void EClientSocket::MySocket::OnReceive( int i)
{
	m_pClient->onReceive( i);
}

void EClientSocket::MySocket::OnSend( int i)
{
	m_pClient->onSend( i);
}

void EClientSocket::MySocket::OnClose( int i)
{
	m_pClient->onClose( i);
}
*/

///////////////////////////////////////////////////////////
// member funcs
EClientSocket::EClientSocket( /*EWrapper *ptr*/) : EClientSocketBase( /*ptr*/)
{
}

EClientSocket::~EClientSocket()
{
	eDisconnect();
}

bool EClientSocket::eConnect( const char *host, UINT port, int clientId, bool extraAuth)
{
	// already connected?
	if( isConnected()) {
		getWrapper()->error( NO_VALID_ID, ALREADY_CONNECTED.code(), ALREADY_CONNECTED.msg());
		return false;
	}

	// init sockets
	//AfxSocketInit();

	// close open connection if there was one
	eDisconnect();

	// create socket
	/*
	m_pSocket.reset(new MySocket(this));
	if( !m_pSocket->Create()) {
		eDisconnect();
		getWrapper()->winError( "Failed to create socket", GetLastError() );
		getWrapper()->error( NO_VALID_ID, FAIL_CREATE_SOCK.code(), FAIL_CREATE_SOCK.msg());
		return false;
	}

	// connect to server
	if( !m_pSocket->Connect(host, port)) {
		DWORD lastError = GetLastError();
		if( lastError != WSAEWOULDBLOCK && !handleSocketError(GetLastError())) {
			return false;
		}
	}
	*/
	// use local machine if no host passed in
	if( !(host && *host)) {
		host = "127.0.0.1";
	}

	try {
		tcp::endpoint end_point(boost::asio::ip::address::from_string(host), port);

		_socket_ptr = boost::shared_ptr<tcp::socket>(new tcp::socket(_io_service));
		_socket_ptr->connect(end_point);
		Start();
	}
	catch (std::exception& e) {
		std::cerr << e.what() << std::endl;
		return false;
	}

	setClientId( clientId);
	setExtraAuth( extraAuth);

	{
		// Wait till we are fully connected (or for an error)
		//CWinThread* pThread = AfxGetThread();
		//while (_socket_ptr.get() && !isConnected()) {
		//	Sleep(10);
		//	//if (!pThread->PumpMessage())
		//	//	return false;
		//}
		//Sleep(60);
	}
	return true;
}

void EClientSocket::eDisconnect()
{
	eDisconnectBase();
	if (_socket_ptr.get() && _socket_ptr->is_open()) {
		_socket_ptr->close();
	}
}

bool EClientSocket::eIsConnected()
{
	return isConnected();
}

void EClientSocket::Start()
{
	_bStop = false;
	_recv_thread = boost::thread(boost::bind(&EClientSocket::RecvData, this));
}

void EClientSocket::Stop()
{
	_bStop = true;
	_recv_thread.join();
}

void EClientSocket::RecvData()
{
	while (!_bStop) {
		boost::array<char, 1024 * 100> buf;
		boost::system::error_code error;

		size_t len = _socket_ptr->read_some(boost::asio::buffer(buf), error);

		if (error == boost::asio::error::eof) {
			//g_logger->info("quotation server connection closed.");
			break;
		}
		else if (error) {
			//g_logger->info() << error.message();
			throw boost::system::system_error(error);
		}
	}
}


int EClientSocket::send(const char* buf, size_t sz)
{
	if( sz <= 0)
		return 0;

	if (_socket_ptr.get() == nullptr || !_socket_ptr->is_open()) {
		return 0;
	}

	int nResult = _socket_ptr->write_some(boost::asio::buffer(buf, sz));
	if( nResult == SOCKET_ERROR && !handleSocketError( GetLastError())) {
		return -1;
	}
	if( nResult <= 0) {
		return 0;
	}
	return nResult;
}

int EClientSocket::receive(char* buf, size_t sz)
{
	if( sz <= 0)
		return 0;

	boost::system::error_code error;
	size_t nResult = _socket_ptr->read_some(boost::asio::buffer(buf, sz), error);

	if (error == boost::asio::error::eof) {
		return -1;
	}
	else if (error) {
		throw boost::system::system_error(error);
		return -1;
	}

	return nResult;
}

///////////////////////////////////////////////////////////
// callbacks from socket

void EClientSocket::onConnect( int i)
{
	if( !handleSocketError( i))
		return;

	onConnectBase();
}

void EClientSocket::onReceive( int i)
{
	if( !handleSocketError( i))
		return;

	checkMessages();
}

void EClientSocket::onSend( int i)
{
	if( !handleSocketError( i))
		return;

	sendBufferedData();
}

void EClientSocket::onClose( int i)
{
	// this function is called when the TWS terminates the connection

	eDisconnect();
	getWrapper()->connectionClosed();
}

///////////////////////////////////////////////////////////
// helper
bool EClientSocket::handleSocketError( int lastError)
{
	if( lastError == ERROR_SUCCESS)
		return true;

	if( lastError == WSAEWOULDBLOCK)
		return false;

	if( lastError == WSAECONNREFUSED) {
		getWrapper()->error( NO_VALID_ID, CONNECT_FAIL.code(), CONNECT_FAIL.msg());
	}
	else {
		char lastErrorStr[512];
		FormatMessage( FORMAT_MESSAGE_FROM_SYSTEM, NULL, lastError, 0,
			lastErrorStr, sizeof(lastErrorStr), NULL);
		getWrapper()->error( NO_VALID_ID, SOCKET_EXCEPTION.code(),
			SOCKET_EXCEPTION.msg() + lastErrorStr);
	}
	eDisconnect();
	return false;
}

bool EClientSocket::isSocketOK() const
{
	return _socket_ptr->is_open();
}
