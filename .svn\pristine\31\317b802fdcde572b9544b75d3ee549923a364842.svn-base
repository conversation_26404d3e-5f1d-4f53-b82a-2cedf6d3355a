#pragma once
#include <list>
#include <TBApi.h>
#include <patterns/singleton.hpp>

class TBApiI : public tb::TBApi, public kits::Singleton<TBApiI>
{
private:
	friend class kits::Singleton<TBApiI>;
	TBApiI(void);

	std::vector<BrokerAccountData> _brk_accounts;
	static std::map<std::string, TradingBrokerPtr> g_tbs;

public:
	~TBApiI(void);

	bool init();
	TradingBrokerPtr get_trading_broker(const std::string& broker_id, BrokerType btype=BrokerType::CTP);
};

