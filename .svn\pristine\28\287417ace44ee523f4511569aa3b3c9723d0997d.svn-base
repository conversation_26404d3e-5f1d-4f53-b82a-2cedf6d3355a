<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="dlltest"
	ProjectGUID="{A7483CE8-2784-46CE-8CB8-8C0C1D27E232}"
	SccLocalPath="."
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(PlatformName)\DLL_Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName=".\dlltest___Win32_Release/dlltest.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="1"
				InlineFunctionExpansion="2"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="2"
				OmitFramePointers="true"
				PreprocessorDefinitions="WIN32;NDEBUG;_CONSOLE;CRYPTOPP_DLL_ONLY"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				EnableEnhancedInstructionSet="1"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				SuppressStartupBanner="true"
				CallingConvention="2"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories=""
				GenerateDebugInformation="true"
				SubSystem="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(PlatformName)\DLL_Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
				TypeLibraryName=".\dlltest___Win32_Release/dlltest.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="1"
				InlineFunctionExpansion="2"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="2"
				OmitFramePointers="true"
				PreprocessorDefinitions="WIN32;NDEBUG;_CONSOLE;CRYPTOPP_DLL_ONLY"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				SuppressStartupBanner="true"
				CallingConvention="2"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories=""
				GenerateDebugInformation="true"
				SubSystem="1"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(PlatformName)\DLL_Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName=".\dlltest___Win32_Debug/dlltest.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				PreprocessorDefinitions="WIN32;_DEBUG;_CONSOLE;CRYPTOPP_DLL_ONLY"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				EnableEnhancedInstructionSet="1"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
				CallingConvention="2"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories=""
				GenerateDebugInformation="true"
				SubSystem="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(PlatformName)\DLL_Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
				TypeLibraryName=".\dlltest___Win32_Debug/dlltest.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				PreprocessorDefinitions="WIN32;_DEBUG;_CONSOLE;CRYPTOPP_DLL_ONLY"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				CallingConvention="2"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories=""
				GenerateDebugInformation="true"
				SubSystem="1"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<File
			RelativePath="dlltest.cpp"
			>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCLCompilerTool"
					Optimization="1"
					PreprocessorDefinitions=""
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|x64"
				>
				<Tool
					Name="VCCLCompilerTool"
					Optimization="1"
					PreprocessorDefinitions=""
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCLCompilerTool"
					Optimization="0"
					PreprocessorDefinitions=""
					BasicRuntimeChecks="3"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Debug|x64"
				>
				<Tool
					Name="VCCLCompilerTool"
					Optimization="0"
					PreprocessorDefinitions=""
					BasicRuntimeChecks="3"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
