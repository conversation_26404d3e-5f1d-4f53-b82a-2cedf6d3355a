#pragma once

#include "Factor.h"
#include "Utils.h"

#include <torch/torch.h>
#include <limits>

namespace Spectre {

// Forward declarations
class FactorEngine;

// ============================================================================
// Statistical Factors - Corresponding to Python's statistical.py
// ============================================================================

// StandardDeviation: Rolling standard deviation
class StandardDeviation : public CustomFactor {
public:
    StandardDeviation(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, int ddof = 0)
        : CustomFactor(win, inputs), m_ddof(ddof) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("StandardDeviation expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            return Spectre::nanstd(inputs[0], 2, false, m_ddof);
        } else {
            throw std::runtime_error("StandardDeviation requires 3D input tensor");
        }
    }

private:
    int m_ddof;
};

// XSStandardDeviation: Cross-sectional standard deviation
class XSStandardDeviation : public CrossSectionFactor {
public:
    XSStandardDeviation(const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, int ddof = 0)
        : CrossSectionFactor(1, inputs), m_ddof(ddof) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("XSStandardDeviation expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        torch::Tensor std_val = Spectre::nanstd(data, c10::nullopt, false, m_ddof);
        return std_val.unsqueeze(-1).expand({data.size(0), data.size(1)});
    }

private:
    int m_ddof;
};

// RollingHigh: Rolling maximum (highest value in window)
class RollingHigh : public CustomFactor {
public:
    RollingHigh(int win = 5, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingHigh expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            return Spectre::nanmax(inputs[0], 2, false);
        } else {
            throw std::runtime_error("RollingHigh requires 3D input tensor");
        }
    }
};

// RollingLow: Rolling minimum (lowest value in window)
class RollingLow : public CustomFactor {
public:
    RollingLow(int win = 5, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingLow expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            return Spectre::nanmin(inputs[0], 2, false);
        } else {
            throw std::runtime_error("RollingLow requires 3D input tensor");
        }
    }
};

// RollingVariance: Rolling variance
class RollingVariance : public CustomFactor {
public:
    RollingVariance(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, int ddof = 0)
        : CustomFactor(win, inputs), m_ddof(ddof) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingVariance expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            torch::Tensor std_val = Spectre::nanstd(inputs[0], 2, false, m_ddof);
            return std_val * std_val;  // variance = std^2
        } else {
            throw std::runtime_error("RollingVariance requires 3D input tensor");
        }
    }

private:
    int m_ddof;
};

// RollingSkewness: Rolling skewness
class RollingSkewness : public CustomFactor {
public:
    RollingSkewness(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 3;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingSkewness expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            torch::Tensor data = inputs[0];
            torch::Tensor mean_val = Spectre::nanmean(data, 2, true);
            torch::Tensor std_val = Spectre::nanstd(data, 2, true);
            
            torch::Tensor centered = data - mean_val;
            torch::Tensor cubed = torch::pow(centered, 3);
            torch::Tensor skew = Spectre::nanmean(cubed, 2, false) / torch::pow(std_val.squeeze(2), 3);
            
            return skew;
        } else {
            throw std::runtime_error("RollingSkewness requires 3D input tensor");
        }
    }
};

// RollingKurtosis: Rolling kurtosis
class RollingKurtosis : public CustomFactor {
public:
    RollingKurtosis(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 4;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingKurtosis expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            torch::Tensor data = inputs[0];
            torch::Tensor mean_val = Spectre::nanmean(data, 2, true);
            torch::Tensor std_val = Spectre::nanstd(data, 2, true);
            
            torch::Tensor centered = data - mean_val;
            torch::Tensor fourth_power = torch::pow(centered, 4);
            torch::Tensor kurt = Spectre::nanmean(fourth_power, 2, false) / torch::pow(std_val.squeeze(2), 4) - 3.0f;
            
            return kurt;
        } else {
            throw std::runtime_error("RollingKurtosis requires 3D input tensor");
        }
    }
};

// Type aliases for convenience (similar to Python)
using STDDEV = StandardDeviation;

} // namespace Spectre
