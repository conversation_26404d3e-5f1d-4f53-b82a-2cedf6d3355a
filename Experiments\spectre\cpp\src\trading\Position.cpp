/**
 * @file Position.cpp
 * @brief Implementation of Position class
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Position.h"
#include "trading/StopModel.h"
#include <cmath>
#include <stdexcept>

namespace Spectre {
namespace Trading {

Position::Position(int64_t shares, double fill_price, double commission, 
                   const TimePoint& dt, std::shared_ptr<StopModel> stop_model)
    : shares_(shares), last_price_(fill_price), realized_(0.0),
      open_dt_(dt), current_dt_(dt), stop_model_(stop_model) {
    
    if (shares == 0) {
        throw std::invalid_argument("Initial shares cannot be zero");
    }
    
    // Calculate average price including commission
    average_price_ = fill_price + commission / static_cast<double>(shares);
    
    // Initialize stop tracker if stop model is provided
    if (stop_model_) {
        initialize_stop_tracker(fill_price);
    }
}

Position::Position(const Position& other)
    : shares_(other.shares_), average_price_(other.average_price_),
      last_price_(other.last_price_), realized_(other.realized_),
      open_dt_(other.open_dt_), current_dt_(other.current_dt_),
      stop_model_(other.stop_model_) {
    
    // Deep copy stop tracker if it exists
    if (other.stop_tracker_ && stop_model_) {
        initialize_stop_tracker(last_price_);
    }
}

Position& Position::operator=(const Position& other) {
    if (this != &other) {
        shares_ = other.shares_;
        average_price_ = other.average_price_;
        last_price_ = other.last_price_;
        realized_ = other.realized_;
        open_dt_ = other.open_dt_;
        current_dt_ = other.current_dt_;
        stop_model_ = other.stop_model_;
        
        // Deep copy stop tracker
        stop_tracker_.reset();
        if (other.stop_tracker_ && stop_model_) {
            initialize_stop_tracker(last_price_);
        }
    }
    return *this;
}

Position::Position(Position&& other) noexcept
    : shares_(other.shares_), average_price_(other.average_price_),
      last_price_(other.last_price_), realized_(other.realized_),
      open_dt_(other.open_dt_), current_dt_(other.current_dt_),
      stop_model_(std::move(other.stop_model_)),
      stop_tracker_(std::move(other.stop_tracker_)) {
    
    // Update tracker's position reference
    if (stop_tracker_) {
        stop_tracker_->set_tracking_position(this);
    }
}

Position& Position::operator=(Position&& other) noexcept {
    if (this != &other) {
        shares_ = other.shares_;
        average_price_ = other.average_price_;
        last_price_ = other.last_price_;
        realized_ = other.realized_;
        open_dt_ = other.open_dt_;
        current_dt_ = other.current_dt_;
        stop_model_ = std::move(other.stop_model_);
        stop_tracker_ = std::move(other.stop_tracker_);
        
        // Update tracker's position reference
        if (stop_tracker_) {
            stop_tracker_->set_tracking_position(this);
        }
    }
    return *this;
}

std::chrono::duration<double> Position::period() const {
    return current_dt_ - open_dt_;
}

double Position::unrealized_percent() const {
    if (average_price_ == 0.0) {
        return 0.0;
    }
    return (last_price_ / average_price_ - 1.0) * sign(static_cast<double>(shares_));
}

void Position::set_last_price(double last_price) {
    last_price_ = last_price;
    if (stop_tracker_) {
        stop_tracker_->update_price(last_price);
    }
}

Position::UpdateResult Position::update(int64_t amount, double fill_price, 
                                       double commission, const TimePoint& dt) {
    if (amount == 0) {
        return std::make_tuple(false, 0.0);
    }
    
    int64_t before_shares = shares_;
    double before_avg_px = average_price_;
    int64_t after_shares = before_shares + amount;
    
    // Handle position reversal (requires two-step process)
    if (after_shares != 0 && sign(static_cast<double>(after_shares)) != sign(static_cast<double>(before_shares))) {
        int64_t fill_1 = amount - after_shares;  // Close existing position
        int64_t fill_2 = amount - fill_1;        // Open new position
        double per_comm = commission / static_cast<double>(amount);
        
        // Step 1: Close existing position
        auto [_, realized] = update(fill_1, fill_price, per_comm * fill_1, dt);
        
        // Step 2: Open new position (reinitialize)
        shares_ = fill_2;
        average_price_ = fill_price + (per_comm * fill_2) / static_cast<double>(fill_2);
        last_price_ = fill_price;
        realized_ = 0.0;  // Reset realized for new position
        open_dt_ = dt;
        current_dt_ = dt;
        
        // Reinitialize stop tracker for new position
        if (stop_model_) {
            initialize_stop_tracker(fill_price);
        }
        
        return std::make_tuple(false, realized);
    }
    
    // Normal position update
    double cum_cost = average_price_ * before_shares + amount * fill_price + commission;
    shares_ = after_shares;
    
    if (after_shares == 0) {
        // Position closed
        average_price_ = 0.0;
        double realized = -cum_cost - realized_;
        realized_ = -cum_cost;
        last_price_ = fill_price;
        stop_tracker_.reset();  // Clear stop tracker
        return std::make_tuple(true, realized);
    } else {
        // Position updated
        average_price_ = cum_cost / static_cast<double>(after_shares);
        double realized = 0.0;
        
        if (std::abs(after_shares) < std::abs(before_shares)) {
            // Partial close - calculate realized P&L
            realized = (before_avg_px - average_price_) * std::abs(after_shares);
        }
        
        realized_ += realized;
        last_price_ = fill_price;
        return std::make_tuple(false, realized);
    }
}

double Position::process_split(double inverse_ratio, double last_price) {
    if (std::isnan(inverse_ratio) || inverse_ratio == 1.0) {
        return 0.0;
    }
    
    double sp = shares_ * inverse_ratio;
    double cash = 0.0;
    
    if (inverse_ratio < 1.0) {
        // Reverse split - fractional shares become cash
        int64_t remaining = shares_ - static_cast<int64_t>(std::round(sp / inverse_ratio));
        if (remaining != 0) {
            cash = remaining * last_price;
        }
    }
    
    shares_ = static_cast<int64_t>(std::round(sp));
    average_price_ = average_price_ / inverse_ratio;
    last_price_ = last_price / inverse_ratio;
    
    if (stop_tracker_) {
        stop_tracker_->process_split(inverse_ratio);
    }
    
    return cash;
}

double Position::process_dividend(double amount, double tax) {
    if (std::isnan(amount) || amount == 0.0) {
        return 0.0;
    }
    
    average_price_ -= amount;
    last_price_ -= (amount + tax);
    
    return shares_ * amount;
}

void Position::initialize_stop_tracker(double fill_price) {
    if (stop_model_) {
        bool is_short = shares_ < 0;
        stop_tracker_ = stop_model_->new_tracker(fill_price, is_short);
        if (stop_tracker_) {
            stop_tracker_->set_tracking_position(this);
        }
    }
}

double Position::sign(double x) {
    return (x > 0.0) ? 1.0 : ((x < 0.0) ? -1.0 : 0.0);
}

// Implementation for check_stop_trigger (remove template for now)
bool Position::check_stop_trigger() {
    if (stop_tracker_) {
        return stop_tracker_->check_trigger();
    }
    return false;
}

} // namespace Trading
} // namespace Spectre
