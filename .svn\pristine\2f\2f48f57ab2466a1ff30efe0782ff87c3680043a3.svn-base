﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6F6CEAE3-0371-379F-8CB8-D50E93F93701}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <Keyword>Win32Proj</Keyword>
    <Platform>x64</Platform>
    <ProjectName>qtunnel</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectName)</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">qtunnel.cp38-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Lab\RoboQuant\src\QuantTunneler\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">qtunneler.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">qtunneler.cp37-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Lab\RoboQuant\src\QuantTunneler\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">qtunneler.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">qtunneler.cp37-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>../CryptUtils;../3rdparty;../3rdparty/cryptopp561;..\3rdParty\Huobi_swap_cpp\include;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <CompileAs>CompileAsCpp</CompileAs>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>_GTEST;WIN32;_CRT_SECURE_NO_WARNINGS;_WINDOWS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Debug";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Debug\";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>gtest_main.lib;datahub.lib;tradingsvr.lib;leveldb.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>$(SolutionDir)..\lib\$(Platform)\$(Configuration)\$(TargetName).lib</ImportLibrary>
      <ProgramDataBaseFile>$(OutDir)$(TargetName).pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>../CryptUtils;../3rdparty;..\3rdParty\Huobi_swap_cpp\include;../3rdparty/cryptopp561;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <CompileAs>CompileAsCpp</CompileAs>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>NOMINMAX;_CRT_SECURE_NO_WARNINGS;_WINDOWS;NDEBUG;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Release";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Release\";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>leveldb.lib;snappy.lib;datahub.lib;tradingsvr.lib;d:\Miniconda3\libs\Python38.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>$(SolutionDir)..\lib\$(Platform)\$(Configuration)\$(TargetName).lib</ImportLibrary>
      <ProgramDataBaseFile>$(OutDir)$(TargetName).pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <CompileAs>CompileAsCpp</CompileAs>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="MinSizeRel";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"MinSizeRel\";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>e:\Lab\RoboQuant\lib\x64\Release\leveldb.lib;e:\Lab\RoboQuant\lib\x64\Release\datahub.lib;e:\Lab\RoboQuant\lib\x64\Release\tradingsvr.lib;D:\Anaconda3\libs\Python37.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>e:/BaseLibrary/boost_1_69_0/lib_x64/lib;e:/BaseLibrary/boost_1_69_0/lib_x64/lib/$(Configuration);e:/Lab/RoboQuant/lib/x64/Release;e:/Lab/RoboQuant/lib/x64/Release/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/Lab/RoboQuant/src/QuantTunneler/build/MinSizeRel/qtunneler.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/Lab/RoboQuant/src/QuantTunneler/build/MinSizeRel/qtunneler.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <CompileAs>CompileAsCpp</CompileAs>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="RelWithDebInfo";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"RelWithDebInfo\";qtunneler_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>e:\BaseLibrary\boost_1_69_0;e:\BaseLibrary\leveldb-windows\include;e:\Lab\RoboQuant\src\include;e:\Lab\RoboQuant\src\packages\spdlog.native.1.0.0\build\native\include;e:\BaseLibrary\jsoncpp\include;e:\BaseLibrary\single_json\include;e:\BaseLibrary\googletest\googletest\include;d:\Anaconda3\include;E:\Lab\RoboQuant\src\QuantTunneler\pybind11\include;D:\Anaconda3\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>e:\Lab\RoboQuant\lib\x64\Release\leveldb.lib;e:\Lab\RoboQuant\lib\x64\Release\datahub.lib;e:\Lab\RoboQuant\lib\x64\Release\tradingsvr.lib;D:\Anaconda3\libs\Python37.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>e:/BaseLibrary/boost_1_69_0/lib_x64/lib;e:/BaseLibrary/boost_1_69_0/lib_x64/lib/$(Configuration);e:/Lab/RoboQuant/lib/x64/Release;e:/Lab/RoboQuant/lib/x64/Release/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/Lab/RoboQuant/src/QuantTunneler/build/RelWithDebInfo/qtunneler.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/Lab/RoboQuant/src/QuantTunneler/build/RelWithDebInfo/qtunneler.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\pybind_api.cpp" />
    <ClCompile Include="QuantTunnel.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\db_test.cc" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\quant_test.cc" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\stdafx.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\core\Backtrace.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\core\Error.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\core\Type.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\core\db.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\core\flags.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\core\logging.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\db\leveldb.cc" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\db\lmdb.cc" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\db\mdb.c">
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">CompileAsC</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">CompileAsC</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">CompileAsC</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\db\midl.c">
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">CompileAsC</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">CompileAsC</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">CompileAsC</CompileAs>
      <CompileAs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">CompileAsC</CompileAs>
    </ClCompile>
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\data\data_api.cpp" />
    <ClCompile Include="E:\Lab\RoboQuant\src\QuantTunneler\quant\quant_api.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\Lab\RoboQuant\src\QuantTunneler\build\ZERO_CHECK.vcxproj">
      <Project>{1A1E2D75-E1DE-36E7-8562-D43CCAFC16F7}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="core\Backtrace.h" />
    <ClInclude Include="core\common.h" />
    <ClInclude Include="core\db.h" />
    <ClInclude Include="core\Error.h" />
    <ClInclude Include="core\Export.h" />
    <ClInclude Include="core\flags.h" />
    <ClInclude Include="core\logging.h" />
    <ClInclude Include="core\logging_glog.h" />
    <ClInclude Include="core\Macros.h" />
    <ClInclude Include="core\Registry.h" />
    <ClInclude Include="core\Type.h" />
    <ClInclude Include="data\data_api.h" />
    <ClInclude Include="db\lmdb.h" />
    <ClInclude Include="db\midl.h" />
    <ClInclude Include="pybind_api.h" />
    <ClInclude Include="quant\quant_api.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets" Condition="Exists('..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets')" />
  </ImportGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets'))" />
  </Target>
</Project>