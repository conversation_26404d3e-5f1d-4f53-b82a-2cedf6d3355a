#pragma once

/**
 * @file Position.h
 * @brief Position class for managing individual asset positions
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include <torch/torch.h>
#include <chrono>
#include <memory>
#include <tuple>

namespace Spectre {
namespace Trading {

// Forward declarations
class StopModel;
class StopTracker;

/**
 * @brief Represents a position in a single asset
 * 
 * This class manages:
 * - Share count and average price calculation
 * - Realized and unrealized P&L tracking
 * - Position updates and closing
 * - Stop loss integration
 * - Corporate actions (splits, dividends)
 */
class Position {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using UpdateResult = std::tuple<bool, double>; // (is_empty, realized_pnl)

    /**
     * @brief Constructor for new position
     * @param shares Initial share count (positive for long, negative for short)
     * @param fill_price Fill price for the initial trade
     * @param commission Commission paid for the initial trade
     * @param dt Timestamp of position opening
     * @param stop_model Optional stop loss model
     */
    Position(int64_t shares, double fill_price, double commission, 
             const TimePoint& dt, std::shared_ptr<StopModel> stop_model = nullptr);

    // Destructor
    ~Position() = default;

    // Copy and move constructors/operators
    Position(const Position& other);
    Position& operator=(const Position& other);
    Position(Position&& other) noexcept;
    Position& operator=(Position&& other) noexcept;

    // Getters
    TimePoint open_dt() const { return open_dt_; }
    std::chrono::duration<double> period() const;
    double value() const { return shares_ * last_price_; }
    int64_t shares() const { return shares_; }
    double average_price() const { return average_price_; }
    double last_price() const { return last_price_; }
    double realized() const { return realized_; }
    double unrealized() const { return (last_price_ - average_price_) * shares_; }
    double unrealized_percent() const;

    // Setters
    void set_last_price(double last_price);
    void set_current_dt(const TimePoint& dt) { current_dt_ = dt; }

    /**
     * @brief Update position with new trade
     * @param amount Share amount to add (positive for buy, negative for sell)
     * @param fill_price Fill price for the trade
     * @param commission Commission for the trade
     * @param dt Timestamp of the trade
     * @return Tuple of (is_position_empty, realized_pnl)
     */
    UpdateResult update(int64_t amount, double fill_price, double commission, const TimePoint& dt);

    /**
     * @brief Process stock split
     * @param inverse_ratio Split ratio (e.g., 0.5 for 2:1 split)
     * @param last_price Price after split
     * @return Cash received from fractional shares
     */
    double process_split(double inverse_ratio, double last_price);

    /**
     * @brief Process dividend payment
     * @param amount Dividend amount per share
     * @param tax Tax rate applied to dividend
     * @return Total cash received from dividend
     */
    double process_dividend(double amount, double tax);

    /**
     * @brief Check if stop loss should trigger
     * @return Stop trigger result (implementation depends on stop model)
     */
    bool check_stop_trigger();

private:
    // Position data
    int64_t shares_;
    double average_price_;
    double last_price_;
    double realized_;
    TimePoint open_dt_;
    TimePoint current_dt_;

    // Stop loss integration
    std::shared_ptr<StopModel> stop_model_;
    std::unique_ptr<StopTracker> stop_tracker_;

    // Helper functions
    static double sign(double x);
    void initialize_stop_tracker(double fill_price);
};

// Forward declaration of sign function (defined in StopModel.h)
double sign(double x);

} // namespace Trading
} // namespace Spectre
