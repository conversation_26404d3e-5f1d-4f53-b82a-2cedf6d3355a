#include "../server_socket_utils.h"

using namespace net;
class serverimpl : public server_socket_utils
{
public:
	serverimpl(int port);
	~serverimpl();

protected:
	void handle_read_data(message& msg, socket_session_ptr pSession){

	}

};

serverimpl::serverimpl(int port)
	: server_socket_utils(port)
{
}

serverimpl::~serverimpl()
{
}
int main(int argc, char* argv[])
{
	serverimpl svr(10005);
	svr.start();
	//svr.get_io_service().run();
	boost::function<void()> f = boost::bind(&boost::asio::io_service::run, &svr.get_io_service());
	boost::thread thrd = boost::thread(f);

	std::string cmd;
	std::cin >> cmd;
	while (cmd != "exit") {
		//std::string ip = "127.0.0.1";
		//svr.m_manager.get_session<sbusiness_type, WORD>(0/*, ip*/)->async_write("from server test data...");
		svr.m_manager.get_session<sbusiness_type, WORD>(0/*, ip*/)->async_write("from server test data...");
		std::cin >> cmd;
	}

	return 0;
}