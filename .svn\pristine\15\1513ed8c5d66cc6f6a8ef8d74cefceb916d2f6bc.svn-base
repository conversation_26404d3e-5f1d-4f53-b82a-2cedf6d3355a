~ 20171010 - 20180302

#弱趋势
头
中
尾

#强趋势
头
中
尾



{
   "barsize" : 6,
   "expr0" : "cross_up(LR_SLOPE_MIDD,LR_SLOPE_SLOW_THRESHOLD) and  factor(STDDEV_SLOW)>factor(STDDEV_THRESHOLD) and factor(TATR)/factor(CLOSE)>0.0025 and factor(HYO_TENKAN_SEN)>factor(HYO_KIJUN_SEN) and factor(STDDEV_SLOW)>0.618*factor(STDDEV_THRESHOLD)",
   "expr1" : "(COST_X_ATR<-3*STDDEV_ATR and factor(TL_FAST)<0) or (COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6 and factor(BAND_GRADIENT)<factor(BAND_GRADIENT_THRESHOLD))",
   "expr2" : "(COST_X_ATR>1 and ((factor_prev(TL_FAST)>factor(TL_FAST) and factor(TL_FAST)<0.618*factor(TL_THRESHOLD) and factor(LR_SLOPE_FAST)<factor(LR_SLOPE_MIDD)) or factor(TL_FAST)<0)) or factor(HYO_TENKAN_SEN)<factor(HYO_KIJUN_SEN)",
   "expr3" : "",
   "expr4" : "",
   "group" : 80,
   "id" : "00170620132623000",
   "name" : "FUT-LRT-R-LONG",
   "remark" : "",
   "used" : "策略"
}

(COST_X_ATR>1 and ((factor_prev(TL_FAST)>factor(TL_FAST) and factor(TL_FAST)<0.618*factor(TL_THRESHOLD) and factor(LR_SLOPE_FAST)<factor(LR_SLOPE_MIDD)) or factor(TL_FAST)<0)) or (COST_X_ATR>0 and factor(BAND_GRADIENT)<factor(BAND_GRADIENT_THRESHOLD) and factor_prev(BAND_GRADIENT)>factor(BAND_GRADIENT) and factor(LR_SLOPE_FAST)<factor(LR_SLOPE_MIDD)) or (COST_X_ATR<0 and factor(BAND_GRADIENT)<0.5*factor(BAND_GRADIENT_THRESHOLD) and factor_prev(BAND_GRADIENT)>factor(BAND_GRADIENT) and factor(LR_SLOPE_FAST)<factor(LR_SLOPE_MIDD) and factor(SQUEEZE_NARROW_BARS)<0) or (COST_X_ATR>0 and long_factor_prev(LR_SLOPE_FAST)>long_factor(LR_SLOPE_FAST) and long_factor(LR_SLOPE_FAST)<long_factor(LR_SLOPE_MIDD))


(COST_X_ATR>1 and ((factor_prev(TL_FAST)<factor(TL_FAST) and factor(TL_FAST)>-0.618*factor(TL_THRESHOLD) and factor(LR_SLOPE_FAST)>factor(LR_SLOPE_MIDD)) or factor(TL_FAST)>0)) or (COST_X_ATR>0 and factor(BAND_GRADIENT)>-1*factor(BAND_GRADIENT_THRESHOLD) and factor_prev(BAND_GRADIENT)<factor(BAND_GRADIENT) and factor(LR_SLOPE_FAST)>factor(LR_SLOPE_MIDD)) or (COST_X_ATR<0 and factor(BAND_GRADIENT)>-0.5*factor(BAND_GRADIENT_THRESHOLD) and factor_prev(BAND_GRADIENT)<factor(BAND_GRADIENT) and factor(LR_SLOPE_FAST)>factor(LR_SLOPE_MIDD) and factor(SQUEEZE_NARROW_BARS)<0) or (COST_X_ATR>0 and long_factor_prev(LR_SLOPE_FAST)<long_factor(LR_SLOPE_FAST) and long_factor(LR_SLOPE_FAST)>long_factor(LR_SLOPE_MIDD))

(COST_X_ATR<-2*STDDEV_ATR and factor(TL_FAST)<-0.2*factor(TL_THRESHOLD)) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and factor(LR_SLOPE_FAST)<factor(LR_SLOPE_SLOW_THRESHOLD) and factor(LR_SLOPE_MIDD)<factor(LR_SLOPE_FAST_THRESHOLD)) or (COST_X_ATR>0.6*STDDEV_ATR and MAXDOWN_ATR>0.2 and factor_prev(LR_SLOPE_FAST)>factor(LR_SLOPE_FAST)) or (factor(BAND_GRADIENT)<0 and factor_prev(BAND_GRADIENT)>factor(BAND_GRADIENT) and factor(NEW)<factor(SQUEEZE_KC_DWL))

(COST_X_ATR<-2*STDDEV_ATR and factor(TL_FAST)>0.2*factor(TL_THRESHOLD)) or (COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<2*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and factor(LR_SLOPE_FAST)>-1*factor(LR_SLOPE_SLOW_THRESHOLD) and factor(LR_SLOPE_MIDD)>-1*factor(LR_SLOPE_FAST_THRESHOLD)) or (COST_X_ATR>0.6*STDDEV_ATR and MAXDOWN_ATR>0.2 and factor_prev(LR_SLOPE_FAST)<factor(LR_SLOPE_FAST)) or (factor(BAND_GRADIENT)>0 and factor_prev(BAND_GRADIENT)<factor(BAND_GRADIENT) and factor(NEW)>factor(SQUEEZE_KC_UPL))

(entry_signal(td_long,ent_lrs_fast) or entry_signal(td_long,ent_lrs_midd)) and  ATR_PREV/CLOSE>0.0025 and trend_level(fd_main,tl_stddev)>=2

#波动性
#STDDEV
#波动性上升
STDDEV_SLOW>STDDEV_THRESHOLD or STDDEV_FAST>STDDEV_SLOW or (STDDEV_FAST<STDDEV_SLOW and STDDEV_SLOW_PREV<STDDEV_SLOW)
#波动性下降
STDDEV_FAST<STDDEV_SLOW and STDDEV_SLOW_PREV>STDDEV_SLOW
#波动性很小
STDDEV_SLOW<0.618*STDDEV_THRESHOLD

#BKGap

#STOP LOSS
LONG
(COST_X_ATR<-4*STDDEV_ATR and TL_FAST<0)
SHORT
(COST_X_ATR<-4*STDDEV_ATR and TL_FAST>0)

(trend_level(fd_main,tl_stddev)<=2 and stop_profit(td_long, sp_trace)) or (trend_level(fd_main,tl_stddev)>2 and stop_profit(td_long,sp_maxdown))

#STOP WIN
LONG
(COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST<LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD<LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>1*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST<LR_SLOPE_MIDD)
SHORT
(COST_X_ATR>0.5 and COST_X_ATR+MAXDOWN_ATR>STDDEV_ATR and (COST_X_ATR<1.6*MAXDOWN_ATR or MAXDOWN_ATR>STDDEV_ATR) and LR_SLOPE_FAST>-1*LR_SLOPE_SLOW_THRESHOLD and LR_SLOPE_MIDD>-1*LR_SLOPE_FAST_THRESHOLD) or (COST_X_ATR>1*STDDEV_ATR and MAXDOWN_ATR>0.2 and LR_SLOPE_FAST>LR_SLOPE_MIDD)

LONG
(COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6 and BAND_GRADIENT<BAND_GRADIENT_THRESHOLD)
SHORT
(COST_X_ATR>3*STDDEV_ATR and MAXDOWN_ATR>0.6 and BAND_GRADIENT>-1*BAND_GRADIENT_THRESHOLD)

#EXIT

LRT
SHORT
(COST_X_ATR>1 and ((TL_FAST_PREV<TL_FAST and TL_FAST>-0.618*TL_THRESHOLD and LR_SLOPE_FAST>LR_SLOPE_MIDD) or TL_FAST>0)) or (COST_X_ATR>0 and BAND_GRADIENT>-1*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV<BAND_GRADIENT and LR_SLOPE_FAST>LR_SLOPE_MIDD) or (COST_X_ATR<0 and BAND_GRADIENT>-0.5*BAND_GRADIENT_THRESHOLD and BAND_GRADIENT_PREV<BAND_GRADIENT and LR_SLOPE_FAST>LR_SLOPE_MIDD and SQUEEZE_NARROW_BARS<0) or (COST_X_ATR>0 and LONG_LR_SLOPE_FAST_PREV<LONG_LR_SLOPE_FAST and LONG_LR_SLOPE_FAST>LONG_LR_SLOPE_MIDD and LR_SLOPE_SLOW_PREV<LR_SLOPE_SLOW)