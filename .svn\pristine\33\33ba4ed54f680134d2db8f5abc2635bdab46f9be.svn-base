﻿// DlgCef.cpp: 实现文件
//
#include "stdafx.h"
#include "resource.h"
#include "pch.h"
#include "DlgCef.h"
#include "afxdialogex.h"

#pragma comment(lib, "libcef_dll_wrapper.lib")
#pragma comment(lib, "libcef.lib")
//#pragma comment(lib, "cef_sandbox.lib")

// DlgCef 对话框

IMPLEMENT_DYNAMIC(DlgCef, CDialogEx)

DlgCef::DlgCef(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_DIALOG_CEF, pParent)
{

}

DlgCef::~DlgCef()
{
}

void DlgCef::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}


BEGIN_MESSAGE_MAP(DlgCef, CDialogEx)
	ON_WM_CLOSE()
END_MESSAGE_MAP()


// DlgCef 消息处理程序


BOOL DlgCef::OnInitDialog()
{
	CDialogEx::OnInitDialog();


	m_handler = CefRefPtr<SimpleHandler>(new SimpleHandler(true));

	RECT rect;
	GetClientRect(&rect);
	CefWindowInfo winInfo;
	winInfo.SetAsChild(GetSafeHwnd(), rect);

	CefBrowserSettings browserSettings;
	CefBrowserHost::CreateBrowser(winInfo, m_handler, "http://dict.youdao.com/?keyfrom=cidian", browserSettings, NULL, NULL);

	return TRUE;
}


void DlgCef::OnClose()
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值

	//m_handler->Release();

	CDialogEx::OnClose();
}
