#pragma once
#include <DataHub.h>
#include <libnet/client_socket_utils.h>
#include <boost/thread.hpp>
#include <condition_variable>
#include <thread>
#include <chrono>
#include <deque>
#include <atomic>
#include <threadsafe_queue.hpp>

namespace dal {
	class SimulationTick : public net::client_socket_utils
	{
	public:
		SimulationTick() : _de_ptr(nullptr), _is_init(false), _wait_times(0), _wait_count(0) {}
		~SimulationTick() {}

		bool init(DataHub* de_ptr);
		void send(const std::string& msg);
		bool connect(std::string& ipport);
		bool isconnect();
		void disconnect();
		long getlasttime() { return _sync_time; }

	protected:

		DataHub* _de_ptr;
		bool _is_init;
		boost::asio::io_service _io_service;
		net::socket_session_ptr _ss_ptr;
		boost::thread _thrd;
		boost::thread _pkg_thrd;

		bool _is_stop;
		threadsafe_queue<net::message> _packages;
		long _trading_day;
		long _sync_time;
		long long _wait_times;
		std::atomic<int> _wait_count;
		BlockDataPtr _blk_ptr;
		std::set<std::string> _labels;
		bool _is_backtest;

		void handle_read_data(net::message& msg, net::socket_session_ptr pSession);
		void flash_new_price(const std::string& label, QuoteData qd);
		void process_tick_data();
		void SimulationTick::update_rangebar();

	};
}