
#  pragma once

//
// AESHelper.h : header file
//
#pragma warning(disable: 4786)
#pragma warning(disable: 4231)

//#define VC_EXTRALEAN		// Exclude rarely-used stuff from Windows headers
//
//#include <afxwin.h>         // MFC core and standard components
//#include <afxext.h>         // MFC extensions
//#include <afxdtctl.h>		// MFC support for Internet Explorer 4 Common Controls
#include <windows.h>
//#ifdef _UNICODE
//#  pragma comment( linker, "/ENTRY:wWinMainCRTStartup")
//#endif

//// Crypto++ Library
//#ifdef _DEBUG
//#  pragma comment ( lib, "cryptlib" )
//#else
//#  pragma comment ( lib, "cryptlib" )
//#endif
//
//const TCHAR DEFAULT_SUBKEY[] = _T("Software\\Code Project\\AES Encrypted");
//
//const TCHAR DEFAULT_STR_VALUE_NAME[] = _T("String Data");
//const TCHAR DEFAULT_STR_VALUE[] = _T("Now is the time for all good men to come to the aide...");
//
//const TCHAR DEFAULT_DWORD_VALUE_NAME[] = _T("DWORD Data");
//const TCHAR DEFAULT_DWORD_VALUE[] = _T("-1999");
//
//const TCHAR DEFAULT_BINARY_VALUE_NAME[] = _T("Binary Data");
//
//const INT MAX_REG_BINARY_SIZE = 2048;

//
// Crypto++ Includes
#pragma warning(push, 3)
#  include "aes.h"
#pragma warning(pop)

/////////////////////////////////////////////////////////////////////////////
// AESKey
struct AESKey {

    AESKey::AESKey( byte b = 0x00 ) {
        ::memset( _cbKey, b, CryptoPP::AES::DEFAULT_KEYLENGTH );
    }

    AESKey::AESKey(const AESKey& rhs) { *this = rhs; }

    AESKey::~AESKey( ) { }

    AESKey& AESKey::operator=(const AESKey& rhs) {
        if( *this == rhs ) { return *this; }
        ::memcpy( _cbKey, rhs._cbKey, CryptoPP::AES::DEFAULT_KEYLENGTH );
        return *this;
    }

    AESKey& AESKey::operator=(const byte* rhs) {
        ::memcpy( _cbKey, rhs, CryptoPP::AES::DEFAULT_KEYLENGTH );
        return *this;
    }

    const byte* AESKey::operator byte*( ) const { return _cbKey; }
    
          byte* AESKey::operator byte*( )       { return _cbKey; }

    BOOL AESKey::operator==(const AESKey& rhs) const {
         return 0 == ::memcmp( _cbKey, rhs._cbKey,
                CryptoPP::AES::DEFAULT_KEYLENGTH ) ? TRUE : FALSE;
    }

    BOOL AESKey::operator!=(const AESKey& rhs) const {
        return !( operator==(rhs) );
    }

    byte _cbKey[ CryptoPP::AES::DEFAULT_KEYLENGTH ];
};

/////////////////////////////////////////////////////////////////////////////
// AESIV
struct AESIV {

    AESIV::AESIV( byte b = 0x00 ) {
        ::memset( _cbIV, b, CryptoPP::AES::BLOCKSIZE );
    };

    AESIV::AESIV(const AESIV& rhs) { *this = rhs; };

    AESIV::~AESIV( ) { }

    AESIV& AESIV::operator=(const AESIV& rhs) {
        if( *this == rhs ) { return *this; }
        ::memcpy( _cbIV, rhs._cbIV, CryptoPP::AES::BLOCKSIZE );
        return *this;
    }

    AESIV& AESIV::operator=(const byte* rhs) {
        ::memcpy( _cbIV, rhs, CryptoPP::AES::BLOCKSIZE );
        return *this;
    }

    const byte* AESIV::operator byte*( ) const { return _cbIV; }
    
          byte* AESIV::operator byte*( )       { return _cbIV; }

    BOOL AESIV::operator==(const AESIV& rhs) const {
        return 0 == ::memcmp( _cbIV, rhs._cbIV,
               CryptoPP::AES::BLOCKSIZE ) ? TRUE : FALSE;
    }

    bool AESIV::operator!=(const AESIV& rhs) const {
        return !( operator==(rhs) );
    }

    byte _cbIV[ CryptoPP::AES::BLOCKSIZE ];
};


