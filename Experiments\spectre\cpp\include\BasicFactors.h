#pragma once

#include "Factor.h"
#include "Utils.h"

#include <torch/torch.h>
#include <limits>

namespace Spectre {

// AbsFactor: 对应Python中的AbsFactor
class AbsFactor : public CustomFactor {
public:
    AbsFactor(const std::shared_ptr<BaseFactor>& input)
        : CustomFactor(1, {input}) {}

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        // 确保只有一个输入
        if (inputs.size() != 1) {
            throw std::runtime_error("AbsFactor expects exactly one input.");
        }
        return inputs[0].abs();
    }
};

// LogFactor: 对应Python中的LogFactor
class LogFactor : public CustomFactor {
public:
    LogFactor(const std::shared_ptr<BaseFactor>& input)
        : CustomFactor(1, {input}) {}

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        // 确保只有一个输入
        if (inputs.size() != 1) {
            throw std::runtime_error("LogFactor expects exactly one input.");
        }
        return inputs[0].log();
    }
};

// ShiftFactor: 对应Python中的ShiftFactor
class ShiftFactor : public CustomFactor {
public:
    ShiftFactor(const std::shared_ptr<BaseFactor>& input, int periods = 1)
        : CustomFactor(1, {input}), m_periods(periods) {}

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("ShiftFactor expects exactly one input.");
        }
        torch::Tensor data = inputs[0];

        // Python版本中对int类型有限制，这里也加上
        if (data.dtype() == torch::kInt || data.dtype() == torch::kLong) {
            throw std::runtime_error("ShiftFactor does not support `int` type, please convert to float.");
        }

        torch::Tensor shifted = data.roll(m_periods, /*dims=*/1);

        // 根据periods的方向填充NaN
        if (m_periods > 0) {
            shifted.slice(/*dim=*/1, /*start=*/0, /*end=*/m_periods).fill_(std::numeric_limits<float>::quiet_NaN());
        } else if (m_periods < 0) {
            shifted.slice(/*dim=*/1, /*start=*/shifted.size(1) + m_periods).fill_(std::numeric_limits<float>::quiet_NaN());
        }
        return shifted;
    }

private:
    int m_periods;
};

// SimpleMovingAverage: Corresponds to Python's SimpleMovingAverage
class SimpleMovingAverage : public CustomFactor {
public:
    SimpleMovingAverage(const std::shared_ptr<BaseFactor>& input, int win)
        : CustomFactor(win, {input}) {
        m_min_win = 2;
        if (win < m_min_win) {
            throw std::runtime_error("SimpleMovingAverage requires win >= 2.");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("SimpleMovingAverage expects exactly one input.");
        }

        // If input is already a Rolling object (from format_input), use it directly
        if (inputs[0].dim() == 3) {
            // This is unfolded data [assets, time_steps, window]
            return Spectre::nanmean(inputs[0], 2, false);
        } else {
            // Create rolling window manually
            torch::Tensor data = inputs[0];
            auto unfolded = data.unfold(1, m_win, 1);
            torch::Tensor result = Spectre::nanmean(unfolded, 2, false);

            // Pad with NaN for the first win-1 positions
            torch::Tensor padded_result = torch::full({data.size(0), data.size(1)},
                                                     std::numeric_limits<float>::quiet_NaN(),
                                                     torch::TensorOptions().dtype(result.dtype()).device(result.device()));
            padded_result.slice(1, m_win - 1).copy_(result);
            return padded_result;
        }
    }
};

// ============================================================================
// More Basic Factors - Corresponding to Python's basic.py
// ============================================================================

// Returns: Calculate returns by tick (not by time)
class Returns : public CustomFactor {
public:
    Returns(const std::shared_ptr<BaseFactor>& close_factor)
        : CustomFactor(2, {close_factor}) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("Returns expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            // Rolling data: [assets, time_steps, window=2]
            torch::Tensor data = inputs[0];
            torch::Tensor last_val = data.select(2, -1);  // Last value
            torch::Tensor first_val = data.select(2, 0);  // First value
            return last_val / first_val - 1.0;
        } else {
            // Regular data: calculate manually
            torch::Tensor data = inputs[0];
            torch::Tensor shifted = data.roll(1, 1);
            shifted.slice(1, 0, 1).fill_(std::numeric_limits<float>::quiet_NaN());
            return data / shifted - 1.0;
        }
    }
};

// LogReturns: Calculate log returns
class LogReturns : public CustomFactor {
public:
    LogReturns(const std::shared_ptr<BaseFactor>& close_factor)
        : CustomFactor(2, {close_factor}) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("LogReturns expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            // Rolling data
            torch::Tensor data = inputs[0];
            torch::Tensor last_val = data.select(2, -1);
            torch::Tensor first_val = data.select(2, 0);
            return (last_val / first_val).log();
        } else {
            // Regular data
            torch::Tensor data = inputs[0];
            torch::Tensor shifted = data.roll(1, 1);
            shifted.slice(1, 0, 1).fill_(std::numeric_limits<float>::quiet_NaN());
            return (data / shifted).log();
        }
    }
};

// VWAP: Volume Weighted Average Price
class VWAP : public CustomFactor {
public:
    VWAP(const std::shared_ptr<BaseFactor>& price_factor,
         const std::shared_ptr<BaseFactor>& volume_factor, int win = 1)
        : CustomFactor(win, {price_factor, volume_factor}) {
        if (win > 1) {
            m_min_win = 2;
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("VWAP expects exactly two inputs (price and volume).");
        }

        torch::Tensor price = inputs[0];
        torch::Tensor volume = inputs[1];

        if (m_win == 1) {
            return price * volume;
        } else {
            // For rolling VWAP
            if (price.dim() == 3 && volume.dim() == 3) {
                torch::Tensor price_volume = price * volume;
                torch::Tensor total_pv = Spectre::nansum(price_volume, 2, false);
                torch::Tensor total_vol = Spectre::nansum(volume, 2, false);
                return total_pv / total_vol;
            } else {
                throw std::runtime_error("Rolling VWAP requires 3D input tensors");
            }
        }
    }
};

// AverageDollarVolume: Average dollar volume
class AverageDollarVolume : public CustomFactor {
public:
    AverageDollarVolume(const std::shared_ptr<BaseFactor>& close_factor,
                       const std::shared_ptr<BaseFactor>& volume_factor, int win = 1)
        : CustomFactor(win, {close_factor, volume_factor}) {
        if (win > 1) {
            m_min_win = 2;
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("AverageDollarVolume expects exactly two inputs.");
        }

        torch::Tensor closes = inputs[0];
        torch::Tensor volumes = inputs[1];

        if (m_win == 1) {
            return closes * volumes;
        } else {
            if (closes.dim() == 3 && volumes.dim() == 3) {
                torch::Tensor dollar_volume = closes * volumes;
                return Spectre::nanmean(dollar_volume, 2, false);
            } else {
                throw std::runtime_error("Rolling AverageDollarVolume requires 3D input tensors");
            }
        }
    }
};

// WeightedAverageValue: Base class for weighted average calculations
class WeightedAverageValue : public CustomFactor {
public:
    WeightedAverageValue(int win = 1, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        if (win > 1) {
            m_min_win = 2;
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("WeightedAverageValue expects exactly two inputs (base and weight).");
        }

        torch::Tensor base = inputs[0];
        torch::Tensor weight = inputs[1];

        if (m_win == 1) {
            return base * weight;
        } else {
            if (base.dim() == 3 && weight.dim() == 3) {
                torch::Tensor weighted_sum = Spectre::nansum(base * weight, 2, false);
                torch::Tensor weight_sum = Spectre::nansum(weight, 2, false);
                return weighted_sum / weight_sum;
            } else {
                throw std::runtime_error("Rolling WeightedAverageValue requires 3D input tensors");
            }
        }
    }
};

// LinearWeightedAverage: Linear weighted moving average
class LinearWeightedAverage : public CustomFactor {
public:
    LinearWeightedAverage(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 2;
        // Create linear weights: 1, 2, 3, ..., win
        m_weights = torch::arange(1, win + 1, torch::TensorOptions().dtype(torch::kFloat32));
        m_weights = m_weights / m_weights.sum();
    }



    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("LinearWeightedAverage expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            torch::Tensor data = inputs[0];
            return Spectre::nansum(data * m_weights.unsqueeze(0).unsqueeze(0), 2, false);
        } else {
            throw std::runtime_error("LinearWeightedAverage requires 3D input tensor");
        }
    }

private:
    torch::Tensor m_weights;
};

// ExponentialWeightedMovingAverage: EWMA implementation
class ExponentialWeightedMovingAverage : public CustomFactor {
public:
    ExponentialWeightedMovingAverage(int span = 0, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {},
                                   bool adjust = false, float half_life = 0.0f)
        : CustomFactor(1, inputs), m_adjust(adjust) {
        m_min_win = 2;

        if (span > 0) {
            m_alpha = 2.0f / (1.0f + span);
            // Length required to achieve 99.97% accuracy
            m_win = static_cast<int>(4.5f * (span + 1));
        } else if (half_life > 0) {
            m_alpha = 1.0f - std::exp(std::log(0.5f) / half_life);
            m_win = static_cast<int>(15 * half_life);
        } else {
            throw std::runtime_error("Either span or half_life must be specified");
        }

        // Create exponential weights
        torch::Tensor arange = torch::arange(m_win - 1, -1, -1, torch::TensorOptions().dtype(torch::kFloat32));
        m_weights = torch::pow(1 - m_alpha, arange);

        if (m_adjust) {
            m_weights = m_weights / m_weights.sum();
        }
    }



    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("ExponentialWeightedMovingAverage expects exactly one input.");
        }

        torch::Tensor data = inputs[0];
        if (data.dim() == 3) {
            torch::Tensor weighted_mean = Spectre::nansum(data * m_weights.unsqueeze(0).unsqueeze(0), 2, false);

            if (m_adjust) {
                return weighted_mean;
            } else {
                torch::Tensor last_val = data.select(2, -1);
                torch::Tensor shifted = last_val.roll(m_win - 1, 1);
                shifted.slice(1, 0, m_win - 1).fill_(0.0f);
                return m_alpha * weighted_mean + shifted * std::pow(1 - m_alpha, m_win);
            }
        } else {
            throw std::runtime_error("ExponentialWeightedMovingAverage requires 3D input tensor");
        }
    }

private:
    float m_alpha;
    bool m_adjust;
    torch::Tensor m_weights;
};

// ElementWiseMax: Element-wise maximum of two factors
class ElementWiseMax : public CustomFactor {
public:
    ElementWiseMax(const std::vector<std::shared_ptr<BaseFactor>>& inputs)
        : CustomFactor(1, inputs) {
        m_min_win = 1;
        if (inputs.size() != 2) {
            throw std::runtime_error("ElementWiseMax requires exactly 2 inputs");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("ElementWiseMax expects exactly two inputs.");
        }

        torch::Tensor a = inputs[0].clone();
        torch::Tensor b = inputs[1].clone();

        // Convert to float if needed
        if (a.dtype() != b.dtype() || a.dtype() != torch::kFloat32) {
            a = a.to(torch::kFloat32);
            b = b.to(torch::kFloat32);
        }

        // Fill NaN with -inf for max operation
        a.masked_fill_(a.isnan(), -std::numeric_limits<float>::infinity());
        b.masked_fill_(b.isnan(), -std::numeric_limits<float>::infinity());

        torch::Tensor result = torch::max(a, b);
        result.masked_fill_(result.isinf(), std::numeric_limits<float>::quiet_NaN());

        return result;
    }
};

// ElementWiseMin: Element-wise minimum of two factors
class ElementWiseMin : public CustomFactor {
public:
    ElementWiseMin(const std::vector<std::shared_ptr<BaseFactor>>& inputs)
        : CustomFactor(1, inputs) {
        m_min_win = 1;
        if (inputs.size() != 2) {
            throw std::runtime_error("ElementWiseMin requires exactly 2 inputs");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 2) {
            throw std::runtime_error("ElementWiseMin expects exactly two inputs.");
        }

        torch::Tensor a = inputs[0].clone();
        torch::Tensor b = inputs[1].clone();

        // Convert to float if needed
        if (a.dtype() != b.dtype() || a.dtype() != torch::kFloat32) {
            a = a.to(torch::kFloat32);
            b = b.to(torch::kFloat32);
        }

        // Fill NaN with +inf for min operation
        a.masked_fill_(a.isnan(), std::numeric_limits<float>::infinity());
        b.masked_fill_(b.isnan(), std::numeric_limits<float>::infinity());

        torch::Tensor result = torch::min(a, b);
        result.masked_fill_(result.isinf(), std::numeric_limits<float>::quiet_NaN());

        return result;
    }
};

// RollingArgMax: Rolling argmax normalized by window size
class RollingArgMax : public CustomFactor {
public:
    RollingArgMax(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingArgMax expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            torch::Tensor data = inputs[0];
            torch::Tensor argmax_indices = data.argmax(2);
            torch::Tensor result = (argmax_indices.to(torch::kFloat32) + 1.0f) / static_cast<float>(m_win);
            return result;
        } else {
            throw std::runtime_error("RollingArgMax requires 3D input tensor");
        }
    }
};

// RollingArgMin: Rolling argmin normalized by window size
class RollingArgMin : public CustomFactor {
public:
    RollingArgMin(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win, inputs) {
        m_min_win = 2;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RollingArgMin expects exactly one input.");
        }

        if (inputs[0].dim() == 3) {
            torch::Tensor data = inputs[0];
            torch::Tensor argmin_indices = data.argmin(2);
            torch::Tensor result = (argmin_indices.to(torch::kFloat32) + 1.0f) / static_cast<float>(m_win);
            return result;
        } else {
            throw std::runtime_error("RollingArgMin requires 3D input tensor");
        }
    }
};

// ConstantsFactor: Factor that returns constant values
class ConstantsFactor : public CustomFactor {
public:
    ConstantsFactor(float value, const std::shared_ptr<BaseFactor>& like_factor)
        : CustomFactor(1, {like_factor}), m_value(value) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("ConstantsFactor expects exactly one input for shape reference.");
        }

        torch::Tensor reference = inputs[0];
        return torch::full(reference.sizes(), m_value,
                          torch::TensorOptions().dtype(reference.dtype()).device(reference.device()));
    }

private:
    float m_value;
};

// Type aliases for convenience (similar to Python)
using MA = SimpleMovingAverage;
using SMA = SimpleMovingAverage;
using EMA = ExponentialWeightedMovingAverage;

} // namespace Spectre
