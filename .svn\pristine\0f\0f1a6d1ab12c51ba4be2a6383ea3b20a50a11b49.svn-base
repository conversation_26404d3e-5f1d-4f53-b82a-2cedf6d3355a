﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release.pub|Win32">
      <Configuration>Release.pub</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release.pub|x64">
      <Configuration>Release.pub</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{14506C7D-8771-41CE-BFC8-62E94B95E941}</ProjectGuid>
    <RootNamespace>STS</RootNamespace>
    <Keyword>MFCProj</Keyword>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccProvider>
    </SccProvider>
    <SccAuxPath>
    </SccAuxPath>
    <ProjectName>XRobot</ProjectName>
    <WindowsTargetPlatformVersion>10.0.20348.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>e:\BaseLibrary\glog-0.3.1\src\windows;e:\BaseLibrary\Visual Leak Detector\include;$(IncludePath)</IncludePath>
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <LibraryPath>e:\BaseLibrary\Visual Leak Detector\lib\Win32;$(LibraryPath)</LibraryPath>
    <GenerateManifest>true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LibraryPath>e:\BaseLibrary\Visual Leak Detector\lib\Win32;$(LibraryPath)</LibraryPath>
    <GenerateManifest>true</GenerateManifest>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>e:\BaseLibrary\glog-0.3.1\src\windows;e:\BaseLibrary\Visual Leak Detector\include;$(IncludePath)</IncludePath>
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>E:\BaseLibrary\boost;e:\BaseLibrary\glog-0.3.1\src\windows;e:\BaseLibrary\Visual Leak Detector\include;$(IncludePath)</IncludePath>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <LibraryPath>e:\BaseLibrary\Visual Leak Detector\lib\Win32;$(LibraryPath)</LibraryPath>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>E:\BaseLibrary\boost;e:\BaseLibrary\glog-0.3.1\src\windows;e:\BaseLibrary\Visual Leak Detector\include;$(IncludePath)</IncludePath>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
    <LibraryPath>e:\BaseLibrary\Visual Leak Detector\lib\Win32;$(LibraryPath)</LibraryPath>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LibraryPath>$(LibraryPath)</LibraryPath>
    <GenerateManifest>false</GenerateManifest>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(IncludePath)</IncludePath>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <OutDir>$(SolutionDir)..\bin\$(Platform)\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|x64'">
    <LibraryPath>e:\BaseLibrary\Visual Leak Detector\lib\Win32;$(LibraryPath)</LibraryPath>
    <GenerateManifest>false</GenerateManifest>
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>E:\BaseLibrary\boost;e:\BaseLibrary\glog-0.3.1\src\windows;e:\BaseLibrary\Visual Leak Detector\include;$(IncludePath)</IncludePath>
    <IntDir>$(SolutionDir)..\obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <OutDir>$(SolutionDir)..\bin\$(Platform)\Release\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_IB_QUOTE_API;WIN32;_WINDOWS;_DEBUG;XLISTCTRLLIB_STATIC;_WINSOCK_DEPRECATED_NO_WARNINGS;%(PreprocessorDefinitions);</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../3rdparty;../3rdparty/cryptopp561;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>CrashRpt1403d.lib;CrashRptProbe1403d.lib;providers.lib;chartctrl.lib;rcf.lib;ws2_32.lib;GameTheory.lib;Utils.lib;Iphlpapi.lib;DataEngine.lib;comsuppw.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <PerUserRedirection>true</PerUserRedirection>
      <ManifestFile>$(OutDir)$(TargetName).manifest</ManifestFile>
      <UACExecutionLevel>AsInvoker</UACExecutionLevel>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_IB_QUOTE_API;WIN32_LEAN_AND_MEAN;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;_WINDOWS;_DEBUG;XLISTCTRLLIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../CryptUtils;..\3rdParty\Huobi_swap_cpp\include;../3rdParty/cryptopp561;../CryptUtils;..\3rdParty\huobi_swap_Cpp\include;../3rdparty;../3rdparty/cryptopp561;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4996;4267;4819;4244;4996;4091</DisableSpecificWarnings>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>spdlog.lib;fmt.lib;leveldb.lib;cryptutils.lib;jsoncpp.lib;broker.lib;QuantLib-x64-mt-gd.lib;CrashRpt1403d.lib;chartctrl.lib;TradingSvr.lib;Utils.lib;Iphlpapi.lib;DataHub.lib;comsuppw.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <PerUserRedirection>true</PerUserRedirection>
      <ManifestFile>$(OutDir)$(TargetName).manifest</ManifestFile>
      <UACExecutionLevel>AsInvoker</UACExecutionLevel>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <EnableDpiAwareness>false</EnableDpiAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_IB_QUOTE_API;WIN32;_WINDOWS;NDEBUG;XLISTCTRLLIB_STATIC;_WINSOCK_DEPRECATED_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../3rdparty;../3rdparty/cryptopp561;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MinimalRebuild>false</MinimalRebuild>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4996;4267;4819;4244;4996</DisableSpecificWarnings>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>CrashRpt1403.lib;CrashRptProbe1403.lib;providers.lib;chartctrl.lib;rcf.lib;ws2_32.lib;GameTheory.lib;Utils.lib;Iphlpapi.lib;DataEngine.lib;comsuppw.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <ManifestFile>$(OutDir)$(TargetName).manifest</ManifestFile>
      <AdditionalManifestDependencies>
      </AdditionalManifestDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <Profile>true</Profile>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <AdditionalManifestFiles>
      </AdditionalManifestFiles>
    </Manifest>
    <Manifest>
      <InputResourceManifests>
      </InputResourceManifests>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_IB_QUOTE_API;WIN32;_WINDOWS;NDEBUG;XLISTCTRLLIB_STATIC;_WINSOCK_DEPRECATED_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../3rdparty;../3rdparty/cryptopp561;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MinimalRebuild>false</MinimalRebuild>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4996;4267;4819;4244;4996</DisableSpecificWarnings>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>CrashRpt1403.lib;CrashRptProbe1403.lib;providers.lib;chartctrl.lib;rcf.lib;ws2_32.lib;GameTheory.lib;Utils.lib;Iphlpapi.lib;DataEngine.lib;comsuppw.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <ManifestFile>$(OutDir)$(TargetName).manifest</ManifestFile>
      <AdditionalManifestDependencies>
      </AdditionalManifestDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <Profile>true</Profile>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <AdditionalManifestFiles>
      </AdditionalManifestFiles>
    </Manifest>
    <Manifest>
      <InputResourceManifests>
      </InputResourceManifests>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_IB_QUOTE_API;WIN32_LEAN_AND_MEAN;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;_WINDOWS;NDEBUG;XLISTCTRLLIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\3rdParty\Huobi_swap_cpp\include;../3rdParty/cryptopp561;../CryptUtils;..\3rdParty\huobi_swap_Cpp\include;../CryptUtils;../3rdparty;../3rdparty/cryptopp561;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4996;4267;4819;4244;4996;4091</DisableSpecificWarnings>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>spdlog.lib;fmt.lib;leveldb.lib;cryptutils.lib;jsoncpp.lib;broker.lib;QuantLib-x64-mt.lib;CrashRpt1403.lib;chartctrl.lib;TradingSvr.lib;Utils.lib;Iphlpapi.lib;DataHub.lib;comsuppw.lib</AdditionalDependencies>
      <UACExecutionLevel>AsInvoker</UACExecutionLevel>
      <ManifestFile>$(OutDir)$(TargetName).manifest</ManifestFile>
      <AdditionalManifestDependencies>
      </AdditionalManifestDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <Profile>true</Profile>
      <OutputFile>$(OutDir)$(TargetName)$(TargetExt)</OutputFile>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <AdditionalManifestFiles>
      </AdditionalManifestFiles>
    </Manifest>
    <Manifest>
      <InputResourceManifests>
      </InputResourceManifests>
      <EnableDpiAwareness>PerMonitorHighDPIAware</EnableDpiAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release.pub|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>PUBLIC_VERSION;_IB_QUOTE_API;WIN32_LEAN_AND_MEAN;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;_WINDOWS;NDEBUG;XLISTCTRLLIB_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../CryptUtils;../3rdparty;../3rdparty/cryptopp561;e:\BaseLibrary\Ta-lib\c\include;$(SolutionDir)\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <DisableSpecificWarnings>4996;4267;4819;4244;4996;4091</DisableSpecificWarnings>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)..\lib\$(Platform)\Release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>spdlog.lib;fmt.lib;cryptutils.lib;jsoncpp.lib;broker.lib;QuantLib-x64-mt.lib;CrashRpt1403.lib;chartctrl.lib;TradingSvr.lib;Utils.lib;Iphlpapi.lib;DataHub.lib;comsuppw.lib</AdditionalDependencies>
      <UACExecutionLevel>AsInvoker</UACExecutionLevel>
      <ManifestFile>$(OutDir)$(TargetName).manifest</ManifestFile>
      <AdditionalManifestDependencies>
      </AdditionalManifestDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <Profile>true</Profile>
      <OutputFile>$(OutDir)QuantRobot$(TargetExt)</OutputFile>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <AdditionalManifestFiles>
      </AdditionalManifestFiles>
    </Manifest>
    <Manifest>
      <InputResourceManifests>
      </InputResourceManifests>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="ReadMe.txt" />
    <None Include="res\DataSvr.ico" />
    <None Include="res\icon1.ico" />
    <None Include="res\STS.ico" />
    <None Include="res\STS.rc2" />
    <None Include="res\finance01.ico" />
    <None Include="res\guard_list.bmp" />
    <None Include="res\rt_manif.bin" />
    <None Include="res\rt_manif2.bin" />
    <None Include="res\Stock01.ico" />
    <None Include="res\Stock02.ico" />
    <None Include="res\toolbar_main.bmp" />
    <None Include="res\toolbar_orders.bmp" />
    <None Include="res\TrayIcon.ico" />
    <None Include="res\wait.wav" />
    <None Include="res\道.ico" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\3rdParty\Slider\SliderBoth.h" />
    <ClInclude Include="..\3rdParty\Slider\SliderBothBtn.h" />
    <ClInclude Include="..\common\ssdhelper.h" />
    <ClInclude Include="AboutDlg.h" />
    <ClInclude Include="DataSourceDlg.h" />
    <ClInclude Include="DlgAccount.h" />
    <ClInclude Include="DlgAccountDetail.h" />
    <ClInclude Include="DlgAccountInformation.h" />
    <ClInclude Include="DlgBlockIndex.h" />
    <ClInclude Include="DlgBlockManagement.h" />
    <ClInclude Include="DlgBroker.h" />
    <ClInclude Include="DlgCodeTable.h" />
    <ClInclude Include="DlgConsole.h" />
    <ClInclude Include="DlgContractManagement.h" />
    <ClInclude Include="DlgCtpQuoteServer.h" />
    <ClInclude Include="DlgExpressionTest.h" />
    <ClInclude Include="DlgFactorExpressionEidtor.h" />
    <ClInclude Include="DlgFinanceDataReport.h" />
    <ClInclude Include="DlgFsMultSetting.h" />
    <ClInclude Include="DlgFuturesStrategy.h" />
    <ClInclude Include="DlgIbNewOrder.h" />
    <ClInclude Include="DlgInstrumentView.h" />
    <ClInclude Include="DlgOpenOrder.h" />
    <ClInclude Include="DlgQuantTimeEditor.h" />
    <ClInclude Include="DlgRealtimeTrading.h" />
    <ClInclude Include="DlgSetRangeValue.h" />
    <ClInclude Include="DlgSettlementInfo.h" />
    <ClInclude Include="DlgSimulation.h" />
    <ClInclude Include="DlgTimerTask.h" />
    <ClInclude Include="DlgTimerTaskSetting.h" />
    <ClInclude Include="QuantScriptHelper.h" />
    <ClInclude Include="STSDlg.h" />
    <ClInclude Include="DlgIbTwsLogon.h" />
    <ClInclude Include="DlgStockStrategyManagement.h" />
    <ClInclude Include="DlgCashflow.h" />
    <ClInclude Include="DlgCashflowManagement.h" />
    <ClInclude Include="DlgCustomBlockSelect.h" />
    <ClInclude Include="DlgGuard.h" />
    <ClInclude Include="DlgLogon.h" />
    <ClInclude Include="DlgOrderInteractive.h" />
    <ClInclude Include="DlgOrderManagement.h" />
    <ClInclude Include="DlgOrders.h" />
    <ClInclude Include="DlgPerferences.h" />
    <ClInclude Include="DlgPortfolio.h" />
    <ClInclude Include="DlgPortfolioSelect.h" />
    <ClInclude Include="DlgPortfoloManagement.h" />
    <ClInclude Include="DlgStatChart.h" />
    <ClInclude Include="DlgStockPool.h" />
    <ClInclude Include="DlgSystemLog.h" />
    <ClInclude Include="DlgTradingStrategy.h" />
    <ClInclude Include="DlgBrokerLog.h" />
    <ClInclude Include="DlgTrendChart.h" />
    <ClInclude Include="DlgYieldCurve.h" />
    <ClInclude Include="Label.h" />
    <ClInclude Include="OptionsDlg.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="TradeNotify.h" />
    <ClInclude Include="TrayIcon.h" />
    <ClInclude Include="XRobot.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\3rdParty\Slider\SliderBoth.cpp" />
    <ClCompile Include="..\3rdParty\Slider\SliderBothBtn.cpp" />
    <ClCompile Include="..\common\ssdhelper.cpp" />
    <ClCompile Include="AboutDlg.cpp" />
    <ClCompile Include="DataSourceDlg.cpp" />
    <ClCompile Include="DlgAccount.cpp" />
    <ClCompile Include="DlgAccountDetail.cpp" />
    <ClCompile Include="DlgAccountInformation.cpp" />
    <ClCompile Include="DlgBlockIndex.cpp" />
    <ClCompile Include="DlgBlockManagement.cpp" />
    <ClCompile Include="DlgBroker.cpp" />
    <ClCompile Include="DlgCodeTable.cpp" />
    <ClCompile Include="DlgConsole.cpp" />
    <ClCompile Include="DlgContractManagement.cpp" />
    <ClCompile Include="DlgCtpQuoteServer.cpp" />
    <ClCompile Include="DlgExpressionTest.cpp" />
    <ClCompile Include="DlgFactorExpressionEidtor.cpp" />
    <ClCompile Include="DlgFinanceDataReport.cpp" />
    <ClCompile Include="DlgFsMultSetting.cpp" />
    <ClCompile Include="DlgFuturesStrategy.cpp" />
    <ClCompile Include="DlgIbNewOrder.cpp" />
    <ClCompile Include="DlgInstrumentView.cpp" />
    <ClCompile Include="DlgOpenOrder.cpp" />
    <ClCompile Include="DlgQuantTimeEditor.cpp" />
    <ClCompile Include="DlgRealtimeTrading.cpp" />
    <ClCompile Include="DlgSetRangeValue.cpp" />
    <ClCompile Include="DlgSettlementInfo.cpp" />
    <ClCompile Include="DlgSimulation.cpp" />
    <ClCompile Include="DlgTimerTask.cpp" />
    <ClCompile Include="DlgTimerTaskSetting.cpp" />
    <ClCompile Include="QuantScriptHelper.cpp" />
    <ClCompile Include="STSDlg.cpp" />
    <ClCompile Include="DlgIbTwsLogon.cpp" />
    <ClCompile Include="DlgStockStrategyManagement.cpp" />
    <ClCompile Include="DlgCashflow.cpp" />
    <ClCompile Include="DlgCashflowManagement.cpp" />
    <ClCompile Include="DlgCustomBlockSelect.cpp" />
    <ClCompile Include="DlgGuard.cpp" />
    <ClCompile Include="DlgLogon.cpp" />
    <ClCompile Include="DlgOrderInteractive.cpp" />
    <ClCompile Include="DlgOrderManagement.cpp" />
    <ClCompile Include="DlgOrders.cpp" />
    <ClCompile Include="DlgPerferences.cpp" />
    <ClCompile Include="DlgPortfolio.cpp" />
    <ClCompile Include="DlgPortfolioSelect.cpp" />
    <ClCompile Include="DlgPortfoloManagement.cpp" />
    <ClCompile Include="DlgStatChart.cpp" />
    <ClCompile Include="DlgStockPool.cpp" />
    <ClCompile Include="DlgSystemLog.cpp" />
    <ClCompile Include="DlgTradingStrategy.cpp" />
    <ClCompile Include="DlgBrokerLog.cpp" />
    <ClCompile Include="DlgTrendChart.cpp" />
    <ClCompile Include="DlgYieldCurve.cpp" />
    <ClCompile Include="Label.cpp" />
    <ClCompile Include="OptionsDlg.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release.pub|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release.pub|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="TrayIcon.cpp" />
    <ClCompile Include="XRobot.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\aicloudmaster.ico" />
    <Image Include="res\bitmap.bmp" />
    <Image Include="res\bitmapftree.bmp" />
    <Image Include="res\bitmap_open_order.bmp" />
    <Image Include="res\qrcode_258.bmp" />
    <Image Include="res\toolbar1.bmp" />
    <Image Include="res\toolbaraccount.bmp" />
    <Image Include="res\toolbar_.bmp" />
    <Image Include="res\toolbar_main2.bmp" />
    <Image Include="res\toolbar_open_order.bmp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="XRobot.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets" Condition="Exists('..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets')" />
  </ImportGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties RESOURCE_FILE="" />
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\nlohmann.json.3.10.2\build\native\nlohmann.json.targets'))" />
  </Target>
</Project>