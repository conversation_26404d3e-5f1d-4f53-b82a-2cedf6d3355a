/* TA-LIB Copyright (c) 1999-2007, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/* List of contributors:
 *
 *  Initial  Name/description
 *  -------------------------------------------------------------------
 *  MF       Mario Fortier
 *
 * Change history:
 *
 *  MMDDYY BY     Description
 *  -------------------------------------------------------------------
 *  121005 MF     First Version
 */

package com.tictactec.ta.lib;

public enum FuncUnstId {
	
	  /* 000 */  Adx,
	  /* 001 */  Adxr,
	  /* 002 */  Atr,
	  /* 003 */  Cmo,
	  /* 004 */  Dx,
	  /* 005 */  Ema,
	  /* 006 */  HtDcPeriod,
	  /* 007 */  HtDcPhase,
	  /* 008 */  HtPhasor,
	  /* 009 */  HtSine,
	  /* 010 */  HtTrendline,
	  /* 011 */  HtTrendMode,
	  /* 012 */  Kama,
	  /* 013 */  Mama,
	  /* 014 */  Mfi,
	  /* 015 */  MinusDI,
	  /* 016 */  MinusDM,
	  /* 017 */  Natr,
	  /* 018 */  PlusDI,
	  /* 019 */  PlusDM,
	  /* 020 */  Rsi,
	  /* 021 */  StochRsi,
	  /* 022 */  T3,
                 All,
                 None
};
