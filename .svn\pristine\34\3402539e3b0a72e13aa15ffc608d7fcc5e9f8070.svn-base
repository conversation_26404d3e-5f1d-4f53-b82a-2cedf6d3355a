#pragma once

#include <BusinessDataDef.h>
#include <Portfolio.h>
#include <patterns/observable.hpp>
#include <json.hpp>
#include <performance.hpp>

namespace bll
{
#ifdef _EXPORT_GTI
#	define DLL_GTI_TS __declspec( dllexport )
#else
#	define DLL_GTI_TS __declspec( dllimport )
#endif

	typedef struct tagFuturesStrategyDataExt
	{
		Portfolio* pf_ptr;
		FactorDataEx fde[2];
		FuturesStrategyData fsd;
		Portfolio* pair_pf_ptr;
		std::map<std::string, int> pair_poss;

		std::string replaceAll(std::string str, const std::string& from, const std::string& to) {
			size_t start_pos = 0;
			while ((start_pos = str.find(from, start_pos)) != std::string::npos) {
				str.replace(start_pos, from.length(), to);
				start_pos += to.length(); // Handles case where 'to' is a substring of 'from'
			}
			return str;
		}

		std::string get_tmp_expr(int ils, int idx) {
			if (ils < 0 || ils > 1 || idx < 0 || idx > 4) {
				return "";
			}
			std::string exprs = fde[ils].expr[idx];
			if (exprs.empty()) {
				return "";
			}
			if (fde[ils].has_mt_model(idx)) {
				exprs = replaceAll(exprs, "MT_MODEL", fmt::format("'{}'", fsd.market_timing_model));
			}
			if (fde[ils].has_mname_ss(idx)) {
				exprs = replaceAll(exprs, "MNAME_SS", fmt::format("'{}'", fsd.mname_short));
			}
			if (fde[ils].has_mname_ls(idx)) {
				exprs = replaceAll(exprs, "MNAME_LS", fmt::format("'{}'", fsd.mname_long));
			}
			if (fde[ils].has_mname_l(idx)) {
				exprs = replaceAll(exprs, "MNAME_L", fmt::format("'{}'", fsd.mname_long));
			}
			if (fde[ils].has_mname_s(idx)) {
				exprs = replaceAll(exprs, "MNAME_S", fmt::format("'{}'", fsd.mname_short));
			}
			if (fde[ils].has_pred_ls(idx)) {
				exprs = replaceAll(exprs, "PRED_LS", fmt::format("{:.3f}", fsd.pred_ls));
			}
			if (fde[ils].has_pred_l(idx)) {
				exprs = replaceAll(exprs, "PRED_L", fmt::format("{:.3f}", fsd.pred_l));
			}
			if (fde[ils].has_pred_s(idx)) {
				exprs = replaceAll(exprs, "PRED_S", fmt::format("{:.3f}", fsd.pred_s));
			}
			if (fde[ils].has_diff(idx)) {
				exprs = replaceAll(exprs, "DIFF", fmt::format("{:.2f}", fsd.diff));
			}
			if (fde[ils].has_stop_rng(idx)) {
				exprs = replaceAll(exprs, "STOP_RNG", fmt::format("{:.2f}", fsd.stop_rng));
			}
			return exprs;
		}

	} FuturesStrategyDataExt;

	typedef struct tagStockStrategyDataExt
	{
		Portfolio* pf_ptr;
		FactorDataEx* long_fde_ptr;
		StockStrategyData fsd;
		BlockDataPtr blk_ptr;
	} StockStrategyDataExt;

	class Strategy
	{
	public:
		virtual bool Init() = 0;
		virtual void OnStrategyStart() = 0;
		virtual void OnStrategyStop() = 0;

		virtual void OnTick(const std::string& label) = 0;
		virtual void OnBar(const std::string& label, BarSize barsize, BarType bartype) = 0;
		virtual int OnManualOpen(const std::string& portfolio_id, const std::string& label, PositionSide side, bool force=false) = 0;
		virtual void RegisterExecOrderInteractiveCallback(const ExecOrderInteractiveCallback& mcb) = 0;
		virtual int GetPositionSize(const std::string& label, Portfolio* pf_ptr, PositionSide side) = 0;
		virtual int GetAllPositionSize(const std::string& label, PositionSide side = PositionSide::None, const std::string& broker_id = "") = 0;
		virtual bool ChangeToMain(const std::string& label, Portfolio* pf_ptr) = 0;
		virtual bool ChangeToReverse(const std::string& label, Portfolio* pf_ptr, bool manual=false) = 0;
		virtual bool ReOpen(const std::string& label, Portfolio* pf_ptr) = 0;

		virtual void CloseAllPosition(const std::string& portfolio_id, const std::string& label, PositionSide ps_side=PositionSide::Long) = 0;
		virtual bool LoadStrategyConfig() = 0;

		virtual void HoldTimeoutCheck() = 0;

		virtual bool IsStart() = 0;
		virtual void ReLoadStrategyData() = 0;

		virtual FactorDataEx* GetFactorDataByPortfolio(const std::string& id, PositionSide side) = 0;
		virtual std::string GetExpression(const std::string& id) = 0;
		virtual BlockDataPtr GetBlockData(const std::string& portfolio_id) = 0;
	};

	DLL_GTI_TS Strategy& ss();
	DLL_GTI_TS Strategy& fs();

}