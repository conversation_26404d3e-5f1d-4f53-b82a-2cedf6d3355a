// The MIT License (MIT)
//
// Copyright (c) 2015-2017 <PERSON> <<EMAIL>>
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.

#ifndef __CPP_REDIS_
#define __CPP_REDIS_

#ifdef _WIN32
#pragma comment( lib, "ws2_32.lib")
#endif /* _WIN32 */

#include <cpp_redis/core/client.hpp>
#include <cpp_redis/core/consumer.hpp>
#include <cpp_redis/core/subscriber.hpp>
#include <cpp_redis/core/reply.hpp>
#include <cpp_redis/misc/error.hpp>
#include <cpp_redis/misc/logger.hpp>
#include <cpp_redis/core/types.hpp>

#endif

#ifndef __CPP_REDIS_USE_CUSTOM_TCP_CLIENT
#include <cpp_redis/network/tcp_client.hpp>
#endif /* __CPP_REDIS_USE_CUSTOM_TCP_CLIENT */
