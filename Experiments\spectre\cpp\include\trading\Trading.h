#pragma once

/**
 * @file Trading.h
 * @brief Main header file for the Spectre Trading module
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, He<PERSON>zh. All rights reserved.
 * @license Apache 2.0
 */

// Core trading components
#include "Position.h"
#include "Portfolio.h"
#include "Event.h"
#include "StopModel.h"
#include "Blotter.h"
#include "Algorithm.h"
#include "Calendar.h"
#include "Metric.h"

// Utility includes
#include <torch/torch.h>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <chrono>

namespace Spectre {
namespace Trading {

/**
 * @brief Main namespace for all trading-related functionality
 * 
 * This namespace contains all classes and functions for:
 * - Position and portfolio management
 * - Event-driven trading system
 * - Order management and execution
 * - Trading algorithms and backtesting
 * - Performance metrics calculation
 */

// Forward declarations
class Position;
class Portfolio;
class BaseBlotter;
class CustomAlgorithm;
class EventManager;
class StopModel;

// Type aliases for convenience
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::duration<double>;
using AssetId = std::string;
using Price = double;
using Shares = int64_t;
using Amount = double;

// Common enums
enum class OrderType {
    Market,
    Limit,
    Stop,
    StopLimit
};

enum class OrderSide {
    Buy,
    Sell
};

enum class OrderStatus {
    Pending,
    Filled,
    PartiallyFilled,
    Cancelled,
    Rejected
};

/**
 * @brief Helper function to run a backtest
 * @param loader Data loader for historical data
 * @param algorithm_factory Function to create algorithm instance
 * @param start Start date for backtest
 * @param end End date for backtest
 * @param delay_factor Whether to apply delay factor for realistic simulation
 * @return Algorithm results after backtest completion
 */
template<typename AlgorithmType>
typename AlgorithmType::Results run_backtest(
    std::shared_ptr<DataLoader> loader,
    std::function<std::unique_ptr<AlgorithmType>(std::shared_ptr<BaseBlotter>)> algorithm_factory,
    const TimePoint& start,
    const TimePoint& end,
    bool delay_factor = true
);

/**
 * @brief Get algorithm data without running full backtest
 * @param loader Data loader for historical data
 * @param algorithm_factory Function to create algorithm instance
 * @param start Start date
 * @param end End date
 * @param delay_factor Whether to apply delay factor
 * @return Algorithm data for analysis
 */
template<typename AlgorithmType>
torch::Tensor get_algorithm_data(
    std::shared_ptr<DataLoader> loader,
    std::function<std::unique_ptr<AlgorithmType>(std::shared_ptr<BaseBlotter>)> algorithm_factory,
    const TimePoint& start,
    const TimePoint& end,
    bool delay_factor = true
);

} // namespace Trading
} // namespace Spectre

// Include template implementations
#include "Trading.tpp"
