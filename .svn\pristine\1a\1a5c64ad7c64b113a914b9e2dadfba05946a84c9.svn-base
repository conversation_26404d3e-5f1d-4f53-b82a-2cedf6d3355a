#pragma once
#include <list>
#include <TBApi.h>
#include <patterns/singleton.hpp>
#include "TradingBroker.h"

class TBApiI : public tb::TBApi, public utils::Singleton<TBApiI>
{
private:
	friend class utils::Singleton<TBApiI>;
	TBApiI(void);
	static TradingBroker g_tb;

	
public:
	~TBApiI(void);

	virtual bool logon(const std::string& broker_id);
	virtual void logout(const std::string& broker_id);
	virtual bool is_logon(const std::string& broker_id);

	virtual bool submit_order(const std::string& broker_id, const Order& order);
	virtual bool submit_order(const std::string& broker_id, const MarketOrder& order);
	virtual bool submit_order(const std::string& broker_id, const LimitOrder& order);
	virtual bool cancel_order(const std::string& broker_id, const Order& order);

	virtual bool is_active(const std::string& label);

	virtual int get_filling_order_size(const std::string& account_id = "") { return g_tb.get_filling_order_size(account_id); }
	virtual bool is_filling_order(const std::string& label) { return g_tb.is_filling_order(label); }

	virtual time_t get_last_trade_time(const std::string& account_id, const std::string& label, POSITION_EFFECT effect);
	virtual int load_last_trade(const std::string& account_id, BlockDataPtr blk_ptr) { return g_tb.load_last_trade(account_id, blk_ptr); }

	virtual bool create_order(const Order& order) { return g_tb.create_order(order); }
	virtual bool update_order(Order* order) { return g_tb.update_order(order); }
	virtual bool delete_open_order(const std::string& order_id, bool force = false) { return g_tb.delete_open_order(order_id, force); }
	virtual void apply_remote_order(const std::string& portfolio_id, Order& order) { g_tb.apply_remote_order(portfolio_id, order); }

	virtual double get_commission(const Trade& trade) const { return g_tb.get_commission(trade); }
	virtual double get_tax(const Trade& trade) const { return g_tb.get_tax(trade); }
	//FIX
	virtual bool query_broker_data(const std::string& broker_id, const std::string& id, const std::string& label, int querytype);

	void RequestAccount(bool subscribe);
	int GetPortfolioDataSize();
	long GetContractPositionSize(const std::string& symbol);
	long GetContractPositionMinutes(const std::string& symbol);

	dal::IBQuoteApi* ibq_api() { return g_tb.get_ib_quote_api(); }
};

