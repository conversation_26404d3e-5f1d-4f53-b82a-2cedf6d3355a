#pragma once

/**
 * @file StopModel.h
 * @brief Stop loss models for position management
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, <PERSON><PERSON>zh. All rights reserved.
 * @license Apache 2.0
 */

#include <functional>
#include <memory>
#include <chrono>
#include <algorithm>
#include <cmath>

namespace Spectre {
namespace Trading {

// Forward declarations
class Position;

/**
 * @brief Helper function to get sign of a number
 */
inline double sign(double x) {
    return (x > 0.0) ? 1.0 : ((x < 0.0) ? -1.0 : 0.0);
}

/**
 * @brief Base class for price tracking
 */
class PriceTracker {
public:
    using Recorder = std::function<double(double, double)>;

    /**
     * @brief Constructor
     * @param current_price Initial price
     * @param recorder Function to record price (default: max)
     */
    PriceTracker(double current_price, Recorder recorder = [](double a, double b) { return std::max(a, b); })
        : last_price_(current_price), recorder_(std::move(recorder)), 
          recorded_price_(current_price), tracking_position_(nullptr) {}

    virtual ~PriceTracker() = default;

    // Getters
    double last_price() const { return last_price_; }
    double recorded_price() const { return recorded_price_; }
    Position* tracking_position() const { return tracking_position_; }

    // Setters
    void set_tracking_position(Position* position) { tracking_position_ = position; }

    /**
     * @brief Update with new price
     * @param last_price New price
     */
    virtual void update_price(double last_price) {
        recorded_price_ = recorder_(recorded_price_, last_price);
        last_price_ = last_price;
    }

    /**
     * @brief Process stock split
     * @param inverse_ratio Split ratio
     */
    virtual void process_split(double inverse_ratio) {
        recorded_price_ /= inverse_ratio;
    }

protected:
    double last_price_;
    Recorder recorder_;
    double recorded_price_;
    Position* tracking_position_;
};

/**
 * @brief Stop loss tracker
 */
class StopTracker : public PriceTracker {
public:
    using Callback = std::function<bool()>;

    /**
     * @brief Constructor
     * @param current_price Current price
     * @param stop_price Stop trigger price
     * @param callback Function to call when stop triggers
     */
    StopTracker(double current_price, double stop_price, Callback callback)
        : PriceTracker(current_price, [](double, double b) { return b; }),
          stop_price_(stop_price), stop_loss_(stop_price < current_price),
          callback_(std::move(callback)) {}

    // Getters
    virtual double stop_price() const { return stop_price_; }
    bool is_stop_loss() const { return stop_loss_; }

    /**
     * @brief Fire the stop callback
     * @return Result of callback execution
     */
    bool fire() {
        if (callback_) {
            return callback_();
        }
        return false;
    }

    /**
     * @brief Check if stop should trigger
     * @return True if stop triggered
     */
    virtual bool check_trigger() {
        if (stop_loss_) {
            if (last_price_ <= stop_price()) {
                return fire();
            }
        } else {
            if (last_price_ >= stop_price()) {
                return fire();
            }
        }
        return false;
    }

protected:
    double stop_price_;
    bool stop_loss_;
    Callback callback_;
};

/**
 * @brief Base class for stop models
 */
class StopModel {
public:
    using Callback = std::function<bool()>;

    /**
     * @brief Constructor
     * @param ratio Stop ratio
     * @param callback Optional callback function
     */
    StopModel(double ratio, Callback callback = nullptr)
        : ratio_(ratio), callback_(std::move(callback)) {}

    virtual ~StopModel() = default;

    // Getters
    double ratio() const { return ratio_; }

    /**
     * @brief Create a new stop tracker
     * @param current_price Current price
     * @param inverse Whether this is for a short position
     * @return Unique pointer to stop tracker
     */
    virtual std::unique_ptr<StopTracker> new_tracker(double current_price, bool inverse) {
        double stop_price;
        if (inverse) {
            stop_price = current_price * (1.0 - ratio_);
        } else {
            stop_price = current_price * (1.0 + ratio_);
        }
        return std::make_unique<StopTracker>(current_price, stop_price, callback_);
    }

protected:
    double ratio_;
    Callback callback_;
};

/**
 * @brief Trailing stop tracker
 */
class TrailingStopTracker : public StopTracker {
public:
    /**
     * @brief Constructor
     * @param current_price Current price
     * @param ratio Trailing ratio
     * @param callback Stop callback
     */
    TrailingStopTracker(double current_price, double ratio, Callback callback)
        : StopTracker(current_price, current_price * (1.0 + ratio), std::move(callback)),
          ratio_(ratio) {
        // Set appropriate recorder based on ratio sign
        if (ratio < 0) {
            recorder_ = [](double a, double b) { return std::max(a, b); };
        } else {
            recorder_ = [](double a, double b) { return std::min(a, b); };
        }
        recorded_price_ = current_price;
    }

    double stop_price() const override {
        return recorded_price_ * (1.0 + ratio_);
    }

private:
    double ratio_;
};

/**
 * @brief Trailing stop model
 */
class TrailingStopModel : public StopModel {
public:
    /**
     * @brief Constructor
     * @param ratio Trailing ratio (negative for stop loss, positive for stop gain)
     * @param callback Optional callback function
     */
    TrailingStopModel(double ratio, Callback callback = nullptr)
        : StopModel(ratio, std::move(callback)) {}

    std::unique_ptr<StopTracker> new_tracker(double current_price, bool inverse) override {
        double ratio = inverse ? -ratio_ : ratio_;
        return std::make_unique<TrailingStopTracker>(current_price, ratio, callback_);
    }
};

/**
 * @brief Base class for decay trailing stop trackers
 */
class DecayTrailingStopTracker : public TrailingStopTracker {
public:
    /**
     * @brief Constructor
     * @param current_price Current price
     * @param ratio Initial ratio
     * @param target Target value for decay calculation
     * @param decay_rate Decay rate
     * @param max_decay Maximum decay factor
     * @param callback Stop callback
     */
    DecayTrailingStopTracker(double current_price, double ratio, double target,
                            double decay_rate, double max_decay, Callback callback)
        : TrailingStopTracker(current_price, ratio, std::move(callback)),
          initial_ratio_(ratio), target_(target), decay_rate_(decay_rate),
          max_decay_(max_decay) {}

    double stop_price() const override {
        double decay = std::max(std::pow(decay_rate_, current() / target_), max_decay_);
        double current_ratio = initial_ratio_ * decay;
        return recorded_price_ * (1.0 + current_ratio);
    }

protected:
    /**
     * @brief Get current value for decay calculation
     * @return Current value (P&L, time, etc.)
     */
    virtual double current() const = 0;

    double initial_ratio_;
    double target_;
    double decay_rate_;
    double max_decay_;
};

/**
 * @brief P&L-based decay trailing stop tracker
 */
class PnLDecayTrailingStopTracker : public DecayTrailingStopTracker {
public:
    using DecayTrailingStopTracker::DecayTrailingStopTracker;

protected:
    double current() const override;
};

/**
 * @brief P&L-based decay trailing stop model
 */
class PnLDecayTrailingStopModel : public StopModel {
public:
    /**
     * @brief Constructor
     * @param ratio Initial stop ratio
     * @param pnl_target Target P&L for decay calculation
     * @param callback Optional callback
     * @param decay_rate Decay rate (default: 0.05)
     * @param max_decay Maximum decay factor (default: 0)
     */
    PnLDecayTrailingStopModel(double ratio, double pnl_target, Callback callback = nullptr,
                             double decay_rate = 0.05, double max_decay = 0.0)
        : StopModel(ratio, std::move(callback)), pnl_target_(pnl_target),
          decay_rate_(decay_rate), max_decay_(max_decay) {}

    std::unique_ptr<StopTracker> new_tracker(double current_price, bool inverse) override {
        double ratio = inverse ? -ratio_ : ratio_;
        return std::make_unique<PnLDecayTrailingStopTracker>(
            current_price, ratio, pnl_target_, decay_rate_, max_decay_, callback_);
    }

private:
    double pnl_target_;
    double decay_rate_;
    double max_decay_;
};

/**
 * @brief Time-based decay trailing stop tracker
 */
class TimeDecayTrailingStopTracker : public DecayTrailingStopTracker {
public:
    using DecayTrailingStopTracker::DecayTrailingStopTracker;

protected:
    double current() const override;
};

/**
 * @brief Time-based decay trailing stop model
 */
class TimeDecayTrailingStopModel : public StopModel {
public:
    using Duration = std::chrono::duration<double>;

    /**
     * @brief Constructor
     * @param ratio Initial stop ratio
     * @param period_target Target period for decay calculation
     * @param callback Optional callback
     * @param decay_rate Decay rate (default: 0.05)
     * @param max_decay Maximum decay factor (default: 0)
     */
    TimeDecayTrailingStopModel(double ratio, const Duration& period_target,
                              Callback callback = nullptr, double decay_rate = 0.05,
                              double max_decay = 0.0)
        : StopModel(ratio, std::move(callback)), period_target_(period_target),
          decay_rate_(decay_rate), max_decay_(max_decay) {}

    std::unique_ptr<StopTracker> new_tracker(double current_price, bool inverse) override {
        double ratio = inverse ? -ratio_ : ratio_;
        return std::make_unique<TimeDecayTrailingStopTracker>(
            current_price, ratio, period_target_.count(), decay_rate_, max_decay_, callback_);
    }

private:
    Duration period_target_;
    double decay_rate_;
    double max_decay_;
};

} // namespace Trading
} // namespace Spectre
