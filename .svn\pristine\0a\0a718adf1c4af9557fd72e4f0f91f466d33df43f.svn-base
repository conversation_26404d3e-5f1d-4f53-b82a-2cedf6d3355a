#pragma once
#include <DataBase.h>
#include <TradingSystem.h>
#include <Providers.h>
#include <Instrument.h>
#include <Portfolio.h>

namespace Executions
{

	class IOrder
	{
	public:
		//EventHandler StatusChangedEvent;
		//ExecutionReportEventHandler ExecutionReportEvent;
		int id;
		std::string orderID;
		std::string clOrdID;
		bll::InstrumentPtr instrument;
		Providers::IExecutionProviderPtr provider;
		PortfolioPtr portfolio;
		bool persistent;
		bool isSent;
		std::vector<ExecutionReport> peports;
	};
	typedef boost::shared_ptr<IOrder> IOrderPtr;

	class SingleOrder : public NewOrderSingle, public IOrder
	{
	public:
		virtual void Send() = 0;
		virtual void Cancel() = 0;
		virtual void Replace() = 0;
	};

	//typedef std::map<std::string, SingleOrder> OrderList;

	class IOrderServer
	{
		virtual void Open(/*Type connectionType, */std::string connectionString) = 0;
		//virtual OrderList Load() = 0;
		virtual void AddOrder(IOrder order) = 0;
		virtual void AddReport(IOrder order, ExecutionReport report) = 0;
		virtual void Remove(IOrder order) = 0;
		virtual void Close() = 0;
	};

	typedef boost::shared_ptr<IOrderServer> IOrderServerPtr;

}