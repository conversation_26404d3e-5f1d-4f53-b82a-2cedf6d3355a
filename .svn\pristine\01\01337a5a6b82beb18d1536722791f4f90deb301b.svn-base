#pragma once

#include <string>
#include <DataDef.h>


typedef std::vector<int> IntSeries;
typedef boost::shared_ptr< IntSeries > IntSeriesPtr;

typedef std::vector<double> RealSeries;
typedef boost::shared_ptr< RealSeries > RealSeriesPtr;

typedef std::vector<ExtDate> DateTimeSeries;
typedef boost::shared_ptr< DateTimeSeries > DateTimeSeriesPtr;

class ISeries
{
public:
	//int _count;					//  Gets the number of values in this series 
	//std::string _symbol;		//  Gets the name of this series 

	//virtual double Item(DATE date, BarData barData) = 0;
	//virtual double Item(int index, BarData barData) = 0;

	virtual double Ago(int n) = 0;			//  Returns n-item-ago value 
	//virtual double Ago(int n, HistBarData barData) = 0;
	virtual bool Contains(ExtDate date) = 0;	//  Checks if this series contains a value with specified time stamp 
	virtual ExtDate GetDateTime(int index) = 0;//  Returns date time by specified index 
	virtual int GetIndex(ExtDate date) = 0;	//  Returns index by specified date time 
};