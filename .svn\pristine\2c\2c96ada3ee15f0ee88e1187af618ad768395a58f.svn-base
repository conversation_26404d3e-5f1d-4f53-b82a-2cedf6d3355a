#ifndef CHANNELS_H
#define CHANNELS_H

#include "HuobiSwap/Enums.h"
#include "../TimeService.h"
#include "JsonWriter.h"

namespace HuobiSwap {

    class Channels {
    public:
        static char* OP_SUB;
        //static char* OP_UNSUB;
        static char* OP_REQ;

        static std::string klineChannel(
                const std::string& symbol, const CandlestickInterval& interval) {

            return klineChannel(OP_SUB, symbol, interval, 0, 0);
        }

        static std::string klineChannel(char*& op,
                const std::string& symbol, const CandlestickInterval& interval) {
            return klineChannel(op, symbol, interval, 0, 0);
        }

        static std::string get_interval_str(const CandlestickInterval& interval)
        {
          switch (interval) {
          case CandlestickInterval::min1:
            return "1min";
          case CandlestickInterval::min5:
            return "5min";
          case CandlestickInterval::min15:
            return "15min";
          case CandlestickInterval::min30:
            return "30min";
          case CandlestickInterval::min60:
            return "60min";
          case CandlestickInterval::hour4:
            return "4hour";
          case CandlestickInterval::day1:
            return "1day";
          case CandlestickInterval::week1:
            return "1week";
          case CandlestickInterval::mon1:
            return "1mon";
          case CandlestickInterval::year1:
            return "1year";
          default:
            break;
          }
          return "";
        }

        static std::string klineChannel(
                char*& op,
                const std::string& symbol,
                const CandlestickInterval& interval,
                long startTime,
                long endTime) {
            JsonWriter writer;
            writer.put(op, "market." + symbol + ".kline." + get_interval_str(interval));
            //writer.put(op, "market." + symbol + ".kline." + interval.getValue());
            writer.put("id", std::to_string(TimeService::getCurrentTimeStamp()));
            if (startTime != 0)
                writer.put("from", startTime);
            if (endTime != 0)
                writer.put("to", endTime);

            return writer.toJsonString();
        }

        static std::string tradeChannel(const std::string& symbol) {

            return tradeChannel(OP_SUB, symbol);
        }

        static std::string tradeChannel(
                char*& op,
                const std::string& symbol) {
            JsonWriter writer;
            writer.put(op, "market." + symbol + ".trade.detail");
            writer.put("id", std::to_string(TimeService::getCurrentTimeStamp()));
            return writer.toJsonString();
        }

        static std::string priceDepthChannel(const std::string& symbol, DepthStep step) {
            return priceDepthChannel(OP_SUB, symbol, step);
        }

        static std::string priceDepthChannel(
                char*& op,
                const std::string& symbol,
                const DepthStep& step) {
            JsonWriter writer;
            writer.put(op, "market." + symbol + ".depth." + step._to_string()/*step.getValue()*/); // DepthStep getValue
            writer.put("id", std::to_string(TimeService::getCurrentTimeStamp()));
            return writer.toJsonString();
        }

        static std::string orderChannel(const std::string& symbol) {
            JsonWriter writer;
            writer.put("op", "sub");
            writer.put("cid", std::to_string(TimeService::getCurrentTimeStamp()));
            writer.put("topic", "orders." + symbol);
            return writer.toJsonString();
        }

        static std::string tradeStatisticsChannel(
                char*& op,
                const std::string& symbol) {
            JsonWriter writer;
            writer.put(op, "market." + symbol + ".detail");
            writer.put("id", std::to_string(TimeService::getCurrentTimeStamp()));
            return writer.toJsonString();
        }

    };
    char* Channels::OP_SUB = "sub";
    //char* Channels::OP_UNSUB = "unsub";
    char* Channels::OP_REQ = "req";
}
#endif /* CHANNELS_H */

