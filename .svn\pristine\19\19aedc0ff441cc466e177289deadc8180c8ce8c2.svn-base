/* Copyright (C) 2013 Interactive Brokers LLC. All rights reserved. This code is subject to the terms
 * and conditions of the IB API Non-Commercial License or the IB API Commercial License, as applicable. */
#include "stdafx.h"
#include "EPosixClientSocket.h"

#include <IB\TwsSocketClientErrors.h>
#include <IB\EWrapper.h>

#include <string.h>

///////////////////////////////////////////////////////////
// member funcs
EPosixClientSocket::EPosixClientSocket( /*EWrapper *ptr*/) //: EClientSocketBase( ptr)
{
}

EPosixClientSocket::~EPosixClientSocket()
{
}

bool EPosixClientSocket::eConnect( const char *host, unsigned int port, int clientId, bool extraAuth)
{
	// already connected?
	if (isConnected()) {
		getWrapper()->error(NO_VALID_ID, ALREADY_CONNECTED.code(), ALREADY_CONNECTED.msg());
		return false;
	}


	if (!(host && *host)) {
		host = "127.0.0.1";
	}

	try {
		tcp::endpoint end_point(boost::asio::ip::address::from_string(host), port);

		_socket_ptr = boost::shared_ptr<tcp::socket>(new tcp::socket(_io_service));
		_socket_ptr->connect(end_point);
	}
	catch (std::exception& e) {
		std::cerr << e.what() << std::endl;
		return false;
	}


	// set client id
	setClientId( clientId);
	setExtraAuth( extraAuth);

	onConnectBase();

	while (_socket_ptr.get() && _socket_ptr->is_open() && !isConnected()) {
		if ( !checkMessages()) {
			// uninitialize Winsock DLL (only for Windows)
			_socket_ptr->close();
			getWrapper()->error( NO_VALID_ID, CONNECT_FAIL.code(), CONNECT_FAIL.msg());
			return false;
		}
	}

	Start();

	// successfully connected
	return true;
}

void EPosixClientSocket::eDisconnect()
{
	if (_socket_ptr.get() && _socket_ptr->is_open()) {
		_socket_ptr->close();
		_socket_ptr.reset();
	}
	eDisconnectBase();

	//if (m_fd >= 0)
	//	// close socket
	//	SocketClose( m_fd);
	//m_fd = -1;
	//// uninitialize Winsock DLL (only for Windows)
	//SocketsDestroy();
	//eDisconnectBase();
}

bool EPosixClientSocket::eIsConnected()
{
	return isConnected();
}

bool EPosixClientSocket::isSocketOK() const
{
	return (_socket_ptr.get() && _socket_ptr->is_open());
	//return ( m_fd >= 0);
}

int EPosixClientSocket::send(const char* buf, size_t sz)
{
	if (sz <= 0)
		return 0;

	if (_socket_ptr.get() == nullptr || !_socket_ptr->is_open()) {
		return 0;
	}

	int nResult = _socket_ptr->write_some(boost::asio::buffer(buf, sz));
	if (nResult == SOCKET_ERROR/* && !handleSocketError(GetLastError())*/) {
		return -1;
	}
	if (nResult <= 0) {
		return 0;
	}
	return nResult;
}

int EPosixClientSocket::receive(char* buf, size_t sz)
{
	if (sz <= 0 || _socket_ptr.get() == nullptr) {
		return 0;
	}

	boost::system::error_code error;
	size_t nResult = _socket_ptr->read_some(boost::asio::buffer(buf, sz), error);

	if (error == boost::asio::error::eof) {
		return -1;
	}
	else if (error) {
		//throw boost::system::system_error(error);
		//AICM_ERROR("IB Socket read error: {}", error.message());
		return -1;
	}

	return nResult;
}

///////////////////////////////////////////////////////////
// callbacks from socket

void EPosixClientSocket::onConnect()
{
	if( !handleSocketError())
		return;

	onConnectBase();
}

void EPosixClientSocket::onReceive()
{
	if( !handleSocketError())
		return;

	checkMessages();
}

void EPosixClientSocket::onSend()
{
	if( !handleSocketError())
		return;

	sendBufferedData();
}

void EPosixClientSocket::onClose()
{
	if( !handleSocketError())
		return;

	eDisconnect();
	getWrapper()->connectionClosed();
}

void EPosixClientSocket::onError()
{
	handleSocketError();
}

///////////////////////////////////////////////////////////
// helper
bool EPosixClientSocket::handleSocketError()
{
	// no error
	if( errno == 0)
		return true;

	// Socket is already connected
	if( errno == EISCONN) {
		return true;
	}

	if( errno == EWOULDBLOCK)
		return false;

	if( errno == ECONNREFUSED) {
		getWrapper()->error( NO_VALID_ID, CONNECT_FAIL.code(), CONNECT_FAIL.msg());
	}
	else {
		getWrapper()->error( NO_VALID_ID, SOCKET_EXCEPTION.code(),
			SOCKET_EXCEPTION.msg() + strerror(errno));
	}
	// reset errno
	errno = 0;
	eDisconnect();
	return false;
}

void EPosixClientSocket::Start()
{
	_bStop = false;
	_recv_thread = std::thread(std::bind(&EPosixClientSocket::RecvData, this));
}

void EPosixClientSocket::Stop()
{
	_bStop = true;
	_recv_thread.join();
}

void EPosixClientSocket::RecvData()
{
	while (!_bStop) {
		checkMessages();
		//boost::array<char, 1024 * 100> buf;
		//boost::system::error_code error;
		//size_t len = _socket_ptr->read_some(boost::asio::buffer(buf), error);
		//if (error == boost::asio::error::eof) {
		//	AICM_INFO("quotation server connection closed.");
		//	break;
		//}
		//else if (error) {
		//	AICM_INFO() << error.message();
		//	throw boost::system::system_error(error);
		//}
	}
}

