#include "WebSocketConnection.h"
#include "TimeService.h"
#include "Utils/ApiSignature.h"
#include "Utils/JsonDocument.h"
#include "Utils/JsonWriter.h"
#include <ctime>
#include <libwebsockets.h>
#include <stdbool.h>
namespace HuobiSwap {

class AutoLock {
public:
  AutoLock(std::mutex &mutex) : mutex_(mutex) { mutex_.lock(); }

  ~AutoLock() { mutex_.unlock(); }

private:
  std::mutex &mutex_;
};

int WebSocketConnection::connectionCounter = 0;

WebSocketConnection::WebSocketConnection(const std::string &apiKey,
                                         const std::string &secretKey,
                                         WebSocketRequest *request,
                                         WebSocketWatchDog *dog,
                                         std::string host)
    : request(request) {
  this->apiKey = apiKey;
  this->secretKey = secretKey;
  this->client = nullptr;
  this->dog = dog;
  this->host = host;
  this->connectionId = connectionCounter++;
  this->subscriptionMarketUrl = "wss://";
  this->subscriptionMarketUrl = this->subscriptionMarketUrl + host + "/linear-swap-ws";// /ws
  this->subscriptionTradingUrl = "wss://";
  this->subscriptionTradingUrl = this->subscriptionTradingUrl + host + "/linear-swap-notification"; // /ws/v1
};

void WebSocketConnection::connect(lws_context *context) {
  if (connectStatus == ConnectionStatus::CONNECTED) {
    Log::WriteLog("[Sub][%d] Already connect", connectionId);
    lwsl_user("Already connect\n");
    std::cout << "-->Already connect." << std::endl;
    return;
  }
  this->context = context;
  lwsl_user("connect_endpoint\n");
  struct lws_client_connect_info ccinfo = {0};
  ccinfo.context = context;
  ccinfo.address = host.c_str();
  ccinfo.port = 443;

  //注意：websocket 订阅访问路径修改此处
  if (request->isNeedSignature == true) {
    ccinfo.path = "/linear-swap-notification";//"/ws";
  } else {
    ccinfo.path = "/linear-swap-ws";//"/ws";
  }
  ccinfo.userdata = (void *)this;
  ccinfo.protocol = "ws";
  ccinfo.origin = "origin";
  ccinfo.host = ccinfo.address;
  ccinfo.ssl_connection = LCCSCF_USE_SSL | LCCSCF_ALLOW_SELFSIGNED |
                          LCCSCF_SKIP_SERVER_CERT_HOSTNAME_CHECK;
  struct lws *conn = lws_client_connect_via_info(&ccinfo);
  lineStatus = LineStatus::LINE_ACTIVE;
  std::cout << "connect_endpoint end." << std::endl;
  lwsl_user("connect_endpoint end\n");
}

void WebSocketConnection::send(const std::string &message) {

  AutoLock lock(mutex);
  sendBufferList.push_back(message);
  lwsl_user("Pre Send: %s \n", message.c_str());
  //std::cout << "send msg: " << message << std::endl;
  if (client != nullptr) {
    lws_callback_on_writable(client);
  }
}

bool WebSocketConnection::flushSendBuffer(lws *ws) {
  AutoLock lock(mutex);
  std::list<std::string>::iterator it = sendBufferList.begin();
  for (; it != sendBufferList.end(); ++it) {

    if (*it == "*") {
      lwsl_user("Get close message\n");
      sendBufferList.clear();
      return false;
    }

    uint8_t buf[LWS_PRE + 1024] = {0};
    int m;
    int n = lws_snprintf((char *)buf + LWS_PRE, 1024, "%s", it->c_str());

    lwsl_user("Sending message %s\n", buf + LWS_PRE);
    //Log::WriteLog("[Sub][%d] Send: %s", connectionId, buf + LWS_PRE);
    m = lws_write(ws, buf + LWS_PRE, n, LWS_WRITE_TEXT);
    if (m < n) {
      Log::WriteLog("[Sub][%d] Sending failed", connectionId);
      lwsl_err("sending failed: %d\n", m);
      sendBufferList.clear();
      return false;
    }
  }
  sendBufferList.clear();
  return true;
}

void WebSocketConnection::onOpen(lws *wsi) {
  lwsl_user("onOpen \n");
  Log::WriteLog("[Sub][%d] Connected", connectionId);
  std::cout << "-->connected." << std::endl;
  connectStatus = ConnectionStatus::CONNECTED;
  lineStatus = LineStatus::LINE_ACTIVE;
  lastReceivedTime = TimeService::getCurrentTimeStamp();
  client = wsi;
  dog->onConnectionCreated(this);
  if (request->isNeedSignature) {
    send(createSignature());
  } else {
    if (request->connectionHandler) {
      request->connectionHandler(this);
    }
  }
}

void WebSocketConnection::onMessage(char *in, size_t len) {

  char *message = in;
  char buf[4096 * 100] = {0};
  unsigned int l = 4096 * 100;
  l = gzDecompress((char *)in, len, buf, l);
  message = buf;

  lwsl_user("RX: %s \n", message);
  //std::cout << "RX: " << message << std::endl;
  //rx log info
  //Log::WriteLog("RX: %s", message);
  lastReceivedTime = TimeService::getCurrentTimeStamp();

  JsonDocument doc;
  JsonWrapper json = doc.parseFromString(message);
  if (json.containKey("ping")) {
    //std::cout << "ping: " << message << std::endl;
    processPingOnMarketLine(json);
  } else if (((json.containKey("status") &&
        strcmp(json.getString("status"), "ok") != 0) &&
       !(json.containKey("op") &&
         strcmp(json.getString("op"), "notify") == 0)) ||
      (json.containKey("err-code") && json.getInt("err-code") != 0)) {
    Log::WriteLog("[Sub] Error: %s", message);
    std::string errorCode =
        json.getStringOrDefault("err-code", "Unknown error code");
    std::string errorMsg =
        json.getStringOrDefault("err-msg", "Unknown error message");
    Error ex;
    ex.errorCode = errorCode;
    ex.errorMsg = errorMsg;
    std::cout << "errcode:" << errorCode << "  errmsg: " << errorMsg << std::endl;
    request->errorHandler(ex);
    close();
  } else if (json.containKey("op")) {
    std::string op = json.getString("op");
    if (op == "notify") { //order update etc.
      Log::WriteLog("notify: %s", message);
      onReceive(json);
    } else if (op == "ping") {
      processPingOnTradingLine(json);
    } else if (op == "auth") {
      if (request->connectionHandler) {
        request->connectionHandler(this);
      }
    } else if (op == "req") {
      onReceive(json);
    }
  } else if (json.containKey("action")) {
    std::string action = json.getString("action");
    lwsl_user("action %s...\n", action.c_str());

    if (json.containKey("ch") && std::string(json.getString("ch")) == "auth") {
      lwsl_user("ch...\n");
      if (json.getInt("code") == 200) {
        if (request->connectionHandler) {
          request->connectionHandler(this);
        }
      } else {
        lwsl_user("RX: %s \n", message);
      }
    } else if (action == "sub") {
      lwsl_user("sub...\n");
    } else if (action == "push") {
      lwsl_user("push...\n");
      onReceive(json);
    } else if (action == "req") {
      lwsl_user("req...\n");
      onReceive(json);
    }

  } else if (json.containKey("ch")) {
    onReceive(json);
  } else if (json.containKey("subbed")) {
    //std::cout << "subbed pass." << std::endl;
  } else if (json.containKey("rep")) {
    onReceive(json);
  } else {
    std::cout << "parse failed" << std::endl;
    Log::WriteLog("parse failed！：%s", message);
  }
}

void WebSocketConnection::onReceive(JsonWrapper &json) {
  lwsl_user("onReceive...\n");
  request->implCallback(json);
}

void WebSocketConnection::processPingOnTradingLine(JsonWrapper &json) {
  lwsl_user("processPingOnTradingLine \n");
  long long ts = json.getLongLong("ts");
  char buf[1024] = {0};
  sprintf(buf, "{\"op\":\"pong\",\"ts\":%lld}", ts);
  std::string message = buf;
  send(message);
}

void WebSocketConnection::processPingOnMarketLine(JsonWrapper &json) {
  long long ts = json.getLongLong("ping");
  char buf[1024] = {0};
  sprintf(buf, "{\"pong\":%lld}", ts);
  std::string message = buf;
  //std::cout << "pong: " << message <<std::endl;
  lwsl_user("processPingOnMarketLine %s\n", message.c_str());
  send(message);
}

/// @brief 
// 交割合约：/notification
// 币本位永续合约：/swap-notification
// USDT本位永续合约：/linear-swap-notification
/// @return 
std::string WebSocketConnection::createSignature() {

  time_t t = time(NULL);
  struct tm *local = gmtime(&t);
  char timeBuf[100] = {0};
  sprintf(timeBuf, "%04d-%02d-%02dT%02d%%3A%02d%%3A%02d", local->tm_year + 1900,
          local->tm_mon + 1, local->tm_mday, local->tm_hour, local->tm_min,
          local->tm_sec);
  char buf[100] = {0};
  sprintf(buf, "%04d-%02d-%02dT%02d:%02d:%02d", local->tm_year + 1900,
          local->tm_mon + 1, local->tm_mday, local->tm_hour, local->tm_min,
          local->tm_sec);
  std::string signa =
      ApiSignature::CreateSignature(host, this->apiKey, this->secretKey,
                                    "/linear-swap-notification", "GET", timeBuf, "");

  JsonWriter writer;
  writer.put("SignatureVersion", "2");
  writer.put("op", "auth");
  writer.put("type", "api");
  writer.put("AccessKeyId", this->apiKey);
  writer.put("Signature", signa.c_str());
  writer.put("SignatureMethod", "HmacSHA256");
  writer.put("Timestamp", buf);
  return writer.toJsonString();
}

long WebSocketConnection::getLastReceivedTime() { return lastReceivedTime; }

void WebSocketConnection::reConnect() {
  if (delayInSecond > 0) {
    delayInSecond--;
  } else {
    lwsl_user("reConnecting...\n");
    std::cout << "reConnecting......." << std::endl;
    this->connect(context);
  }
}

void WebSocketConnection::reConnect(int delayInSecond) {
  Log::WriteLog("[Sub][%d] Reconnecting after %d seconds later", connectionId,
                delayInSecond);
  lwsl_user("Reconnecting after %d seconds later\n", delayInSecond);
  if (client != nullptr) {
    lwsl_user("closing client\n");
    send("*");
    // lws_set_timeout(client, NO_PENDING_TIMEOUT, LWS_TO_KILL_ASYNC);

  } else {
    lwsl_user("client is null\n");
  }

  client = nullptr;
  this->delayInSecond = delayInSecond;
  lineStatus = LineStatus::LINE_DELAY;
}

void WebSocketConnection::disconnect() {
  Log::WriteLog("[Sub][%d] Disconnected", connectionId);
  std::cout << "-->disconnected." << std::endl;
  connectStatus = ConnectionStatus::CLOSED;
  client = nullptr;
  //close();
}

void WebSocketConnection::close() {
  Log::WriteLog("[Sub][%d] Closing normally", connectionId);
  lwsl_user("Closing normally \n");

  //restart crash.
  //if (client != nullptr) {
  //  lws_set_timeout(client, PENDING_TIMEOUT_KILLED_BY_PARENT, LWS_TO_KILL_ASYNC);
  //}
  // lwsl_user("Closing normally 1\n");
  lineStatus = LineStatus::LINE_IDEL;
  // lwsl_user("Closing normally 2\n");
  dog->onClosedNormally(this);
  // lwsl_user("Closing normally 3\n");
}
} // namespace HuobiSwap
