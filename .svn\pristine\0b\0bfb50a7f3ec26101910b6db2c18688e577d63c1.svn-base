/******************************************************************************

$Author$
  
$Modtime$
$Revision$

Description: Interface of the class "CProductKey"
             (helper class for input and editing of product keys)

$Log$

******************************************************************************/

/*** Declaration of class "CProductKey" **************************************/
class CEditProductKey;

class CProductKey: public CObject
{
  public:
  enum TYPE {DEC, HEX, ALPHANUM};

  CProductKey(): m_chDelimiter(_T('-')), m_pFirst(0), m_type(ALPHANUM) {}

  ~CProductKey();

  bool          Copy           () const;
  bool          Cut            () const;
  void          Delete         () const;
  const CString Get            () const;
  TYPE          GetType        () const {return m_type;}
  bool          IsComplete     () const;
  bool          IsEmpty        () const;
  bool          IsValid        (int ch) const;
  void          LimitText      (int nItem = -1, int nChars = 5) const;
  bool          Paste          () const;
  bool          Set            (LPCTSTR pszProductKey) const;
  TCHAR         SetDelimiter   (TCHAR chDelimiter = _T('-'));
  CWnd*         SetFocus       () const;
  TYPE          SetType        (TYPE type);
  bool          SubclassDlgItem(UINT nIDOfFirstItem, CWnd* pParent,
                                int nItemCount = 1, int nChars = 5);

  private:
  void RemoveCustomizedFont();

  TCHAR            m_chDelimiter;
  CEditProductKey* m_pFirst;
  static int       m_nUseCustomizedFont;
  static CString   m_strFontFileName;
  CFont            m_font;
  TYPE             m_type;
};
