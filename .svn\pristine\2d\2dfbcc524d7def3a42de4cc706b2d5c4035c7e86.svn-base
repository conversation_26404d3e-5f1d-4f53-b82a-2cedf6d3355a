/* TA-LIB Copyright (c) 1999-2007, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/* DO NOT MODIFY this file.
 * This file is automatically generated by gen_code.
 *
 * If you are not re-building an interface with SWIG (which is most
 * likely the case), just ignore this file.
 */
 

/*
 * TA_ACOS - Vector Trigonometric ACos
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_ACOS( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_ACOS_Lookback( void );


/*
 * TA_AD - Chaikin A/D Line
 * 
 * Input  = High, Low, Close, Volume
 * Output = double
 * 
 */
TA_RetCode TA_AD( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inHigh */,
                  const double *IN_ARRAY /* inLow */,
                  const double *IN_ARRAY /* inClose */,
                  const double *IN_ARRAY /* inVolume */,
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outReal */ );

int TA_AD_Lookback( void );


/*
 * TA_ADD - Vector Arithmetic Add
 * 
 * Input  = double, double
 * Output = double
 * 
 */
TA_RetCode TA_ADD( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal0 */,
                   const double *IN_ARRAY /* inReal1 */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_ADD_Lookback( void );


/*
 * TA_ADOSC - Chaikin A/D Oscillator
 * 
 * Input  = High, Low, Close, Volume
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInFastPeriod:(From 2 to 100000)
 *    Number of period for the fast MA
 * 
 * optInSlowPeriod:(From 2 to 100000)
 *    Number of period for the slow MA
 * 
 * 
 */
TA_RetCode TA_ADOSC( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inHigh */,
                     const double *IN_ARRAY /* inLow */,
                     const double *IN_ARRAY /* inClose */,
                     const double *IN_ARRAY /* inVolume */,
                     int           OPT_INT /* optInFastPeriod */, /* From 2 to 100000 */
                     int           OPT_INT /* optInSlowPeriod */, /* From 2 to 100000 */
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outReal */ );

int TA_ADOSC_Lookback( int           optInFastPeriod, /* From 2 to 100000 */
                     int           optInSlowPeriod );  /* From 2 to 100000 */


/*
 * TA_ADX - Average Directional Movement Index
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ADX( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   const double *IN_ARRAY /* inClose */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_ADX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_ADXR - Average Directional Movement Index Rating
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ADXR( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inHigh */,
                    const double *IN_ARRAY /* inLow */,
                    const double *IN_ARRAY /* inClose */,
                    int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_ADXR_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_APO - Absolute Price Oscillator
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInFastPeriod:(From 2 to 100000)
 *    Number of period for the fast MA
 * 
 * optInSlowPeriod:(From 2 to 100000)
 *    Number of period for the slow MA
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_APO( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInFastPeriod */, /* From 2 to 100000 */
                   int           OPT_INT /* optInSlowPeriod */, /* From 2 to 100000 */
                   TA_MAType     OPT_MATYPE /* optInMAType */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_APO_Lookback( int           optInFastPeriod, /* From 2 to 100000 */
                   int           optInSlowPeriod, /* From 2 to 100000 */
                   TA_MAType     optInMAType ); 

/*
 * TA_AROON - Aroon
 * 
 * Input  = High, Low
 * Output = double, double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_AROON( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inHigh */,
                     const double *IN_ARRAY /* inLow */,
                     int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outAroonDown */,
                     double       *OUT_ARRAY /* outAroonUp */ );

int TA_AROON_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_AROONOSC - Aroon Oscillator
 * 
 * Input  = High, Low
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_AROONOSC( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_AROONOSC_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_ASIN - Vector Trigonometric ASin
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_ASIN( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_ASIN_Lookback( void );


/*
 * TA_ATAN - Vector Trigonometric ATan
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_ATAN( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_ATAN_Lookback( void );


/*
 * TA_ATR - Average True Range
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ATR( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   const double *IN_ARRAY /* inClose */,
                   int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_ATR_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_AVGPRICE - Average Price
 * 
 * Input  = Open, High, Low, Close
 * Output = double
 * 
 */
TA_RetCode TA_AVGPRICE( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inOpen */,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        const double *IN_ARRAY /* inClose */,
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_AVGPRICE_Lookback( void );


/*
 * TA_BBANDS - Bollinger Bands
 * 
 * Input  = double
 * Output = double, double, double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * optInNbDevUp:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for upper band
 * 
 * optInNbDevDn:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for lower band
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_BBANDS( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inReal */,
                      int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                      double        OPT_REAL /* optInNbDevUp */, /* From TA_REAL_MIN to TA_REAL_MAX */
                      double        OPT_REAL /* optInNbDevDn */, /* From TA_REAL_MIN to TA_REAL_MAX */
                      TA_MAType     OPT_MATYPE /* optInMAType */,
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outRealUpperBand */,
                      double       *OUT_ARRAY /* outRealMiddleBand */,
                      double       *OUT_ARRAY /* outRealLowerBand */ );

int TA_BBANDS_Lookback( int           optInTimePeriod, /* From 2 to 100000 */
                      double        optInNbDevUp, /* From TA_REAL_MIN to TA_REAL_MAX */
                      double        optInNbDevDn, /* From TA_REAL_MIN to TA_REAL_MAX */
                      TA_MAType     optInMAType ); 

/*
 * TA_BBANDSEXT - Bollinger Bands Extend
 * 
 * Input  = double
 * Output = double, double, double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * optInNbDevUp:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for upper band
 * 
 * optInNbDevDn:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for lower band
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_BBANDSEXT( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inReal */,
                         int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                         double        OPT_REAL /* optInNbDevUp */, /* From TA_REAL_MIN to TA_REAL_MAX */
                         double        OPT_REAL /* optInNbDevDn */, /* From TA_REAL_MIN to TA_REAL_MAX */
                         TA_MAType     OPT_MATYPE /* optInMAType */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         double       *OUT_ARRAY /* outRealBandExpand */,
                         double       *OUT_ARRAY /* outRealBandGradient */,
                         double       *OUT_ARRAY /* outRealBandWidth */ );

int TA_BBANDSEXT_Lookback( int           optInTimePeriod, /* From 2 to 100000 */
                         double        optInNbDevUp, /* From TA_REAL_MIN to TA_REAL_MAX */
                         double        optInNbDevDn, /* From TA_REAL_MIN to TA_REAL_MAX */
                         TA_MAType     optInMAType ); 

/*
 * TA_BBANDSPOSITION - Bollinger Bands Position
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * optInNbDevUp:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for upper band
 * 
 * optInNbDevDn:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for lower band
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_BBANDSPOSITION( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inReal */,
                              int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                              double        OPT_REAL /* optInNbDevUp */, /* From TA_REAL_MIN to TA_REAL_MAX */
                              double        OPT_REAL /* optInNbDevDn */, /* From TA_REAL_MIN to TA_REAL_MAX */
                              TA_MAType     OPT_MATYPE /* optInMAType */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              double       *OUT_ARRAY /* outRealBandPosition */ );

int TA_BBANDSPOSITION_Lookback( int           optInTimePeriod, /* From 2 to 100000 */
                              double        optInNbDevUp, /* From TA_REAL_MIN to TA_REAL_MAX */
                              double        optInNbDevDn, /* From TA_REAL_MIN to TA_REAL_MAX */
                              TA_MAType     optInMAType ); 

/*
 * TA_BETA - Beta
 * 
 * Input  = double, double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_BETA( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal0 */,
                    const double *IN_ARRAY /* inReal1 */,
                    int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_BETA_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_BKGAP - Bbands and Keltener Channel Gap
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * optInNbDevUp:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for upper band
 * 
 * optInNbDevDn:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for lower band
 * 
 * optInNbBkgapAtrMult:(From TA_REAL_MIN to TA_REAL_MAX)
 *    BKGap Deviation multiplier for ATR
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_BKGAP( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inHigh */,
                     const double *IN_ARRAY /* inLow */,
                     const double *IN_ARRAY /* inClose */,
                     int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                     double        OPT_REAL /* optInNbDevUp */, /* From TA_REAL_MIN to TA_REAL_MAX */
                     double        OPT_REAL /* optInNbDevDn */, /* From TA_REAL_MIN to TA_REAL_MAX */
                     double        OPT_REAL /* optInNbBkgapAtrMult */, /* From TA_REAL_MIN to TA_REAL_MAX */
                     TA_MAType     OPT_MATYPE /* optInMAType */,
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outRealBKGap */ );

int TA_BKGAP_Lookback( int           optInTimePeriod, /* From 1 to 100000 */
                     double        optInNbDevUp, /* From TA_REAL_MIN to TA_REAL_MAX */
                     double        optInNbDevDn, /* From TA_REAL_MIN to TA_REAL_MAX */
                     double        optInNbBkgapAtrMult, /* From TA_REAL_MIN to TA_REAL_MAX */
                     TA_MAType     optInMAType ); 

/*
 * TA_BOP - Balance Of Power
 * 
 * Input  = Open, High, Low, Close
 * Output = double
 * 
 */
TA_RetCode TA_BOP( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inOpen */,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   const double *IN_ARRAY /* inClose */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_BOP_Lookback( void );


/*
 * TA_CCI - Commodity Channel Index
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_CCI( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   const double *IN_ARRAY /* inClose */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_CCI_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_CDL2CROWS - Two Crows
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL2CROWS( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inOpen */,
                         const double *IN_ARRAY /* inHigh */,
                         const double *IN_ARRAY /* inLow */,
                         const double *IN_ARRAY /* inClose */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         int          *OUT_ARRAY /* outInteger */ );

int TA_CDL2CROWS_Lookback( void );


/*
 * TA_CDL3BLACKCROWS - Three Black Crows
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL3BLACKCROWS( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDL3BLACKCROWS_Lookback( void );


/*
 * TA_CDL3INSIDE - Three Inside Up/Down
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL3INSIDE( int           START_IDX,
                          int           END_IDX,
                          const double *IN_ARRAY /* inOpen */,
                          const double *IN_ARRAY /* inHigh */,
                          const double *IN_ARRAY /* inLow */,
                          const double *IN_ARRAY /* inClose */,
                          int          *BEG_IDX,
                          int          *OUT_SIZE,
                          int          *OUT_ARRAY /* outInteger */ );

int TA_CDL3INSIDE_Lookback( void );


/*
 * TA_CDL3LINESTRIKE - Three-Line Strike 
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL3LINESTRIKE( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDL3LINESTRIKE_Lookback( void );


/*
 * TA_CDL3OUTSIDE - Three Outside Up/Down
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL3OUTSIDE( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDL3OUTSIDE_Lookback( void );


/*
 * TA_CDL3STARSINSOUTH - Three Stars In The South
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL3STARSINSOUTH( int           START_IDX,
                                int           END_IDX,
                                const double *IN_ARRAY /* inOpen */,
                                const double *IN_ARRAY /* inHigh */,
                                const double *IN_ARRAY /* inLow */,
                                const double *IN_ARRAY /* inClose */,
                                int          *BEG_IDX,
                                int          *OUT_SIZE,
                                int          *OUT_ARRAY /* outInteger */ );

int TA_CDL3STARSINSOUTH_Lookback( void );


/*
 * TA_CDL3WHITESOLDIERS - Three Advancing White Soldiers
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDL3WHITESOLDIERS( int           START_IDX,
                                 int           END_IDX,
                                 const double *IN_ARRAY /* inOpen */,
                                 const double *IN_ARRAY /* inHigh */,
                                 const double *IN_ARRAY /* inLow */,
                                 const double *IN_ARRAY /* inClose */,
                                 int          *BEG_IDX,
                                 int          *OUT_SIZE,
                                 int          *OUT_ARRAY /* outInteger */ );

int TA_CDL3WHITESOLDIERS_Lookback( void );


/*
 * TA_CDLABANDONEDBABY - Abandoned Baby
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLABANDONEDBABY( int           START_IDX,
                                int           END_IDX,
                                const double *IN_ARRAY /* inOpen */,
                                const double *IN_ARRAY /* inHigh */,
                                const double *IN_ARRAY /* inLow */,
                                const double *IN_ARRAY /* inClose */,
                                double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                                int          *BEG_IDX,
                                int          *OUT_SIZE,
                                int          *OUT_ARRAY /* outInteger */ );

int TA_CDLABANDONEDBABY_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLADVANCEBLOCK - Advance Block
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLADVANCEBLOCK( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inOpen */,
                               const double *IN_ARRAY /* inHigh */,
                               const double *IN_ARRAY /* inLow */,
                               const double *IN_ARRAY /* inClose */,
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               int          *OUT_ARRAY /* outInteger */ );

int TA_CDLADVANCEBLOCK_Lookback( void );


/*
 * TA_CDLBELTHOLD - Belt-hold
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLBELTHOLD( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDLBELTHOLD_Lookback( void );


/*
 * TA_CDLBREAKAWAY - Breakaway
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLBREAKAWAY( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inOpen */,
                            const double *IN_ARRAY /* inHigh */,
                            const double *IN_ARRAY /* inLow */,
                            const double *IN_ARRAY /* inClose */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            int          *OUT_ARRAY /* outInteger */ );

int TA_CDLBREAKAWAY_Lookback( void );


/*
 * TA_CDLCLOSINGMARUBOZU - Closing Marubozu
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLCLOSINGMARUBOZU( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLCLOSINGMARUBOZU_Lookback( void );


/*
 * TA_CDLCONCEALBABYSWALL - Concealing Baby Swallow
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLCONCEALBABYSWALL( int           START_IDX,
                                   int           END_IDX,
                                   const double *IN_ARRAY /* inOpen */,
                                   const double *IN_ARRAY /* inHigh */,
                                   const double *IN_ARRAY /* inLow */,
                                   const double *IN_ARRAY /* inClose */,
                                   int          *BEG_IDX,
                                   int          *OUT_SIZE,
                                   int          *OUT_ARRAY /* outInteger */ );

int TA_CDLCONCEALBABYSWALL_Lookback( void );


/*
 * TA_CDLCOUNTERATTACK - Counterattack
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLCOUNTERATTACK( int           START_IDX,
                                int           END_IDX,
                                const double *IN_ARRAY /* inOpen */,
                                const double *IN_ARRAY /* inHigh */,
                                const double *IN_ARRAY /* inLow */,
                                const double *IN_ARRAY /* inClose */,
                                int          *BEG_IDX,
                                int          *OUT_SIZE,
                                int          *OUT_ARRAY /* outInteger */ );

int TA_CDLCOUNTERATTACK_Lookback( void );


/*
 * TA_CDLDARKCLOUDCOVER - Dark Cloud Cover
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLDARKCLOUDCOVER( int           START_IDX,
                                 int           END_IDX,
                                 const double *IN_ARRAY /* inOpen */,
                                 const double *IN_ARRAY /* inHigh */,
                                 const double *IN_ARRAY /* inLow */,
                                 const double *IN_ARRAY /* inClose */,
                                 double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                                 int          *BEG_IDX,
                                 int          *OUT_SIZE,
                                 int          *OUT_ARRAY /* outInteger */ );

int TA_CDLDARKCLOUDCOVER_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLDOJI - Doji
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLDOJI( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inOpen */,
                       const double *IN_ARRAY /* inHigh */,
                       const double *IN_ARRAY /* inLow */,
                       const double *IN_ARRAY /* inClose */,
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       int          *OUT_ARRAY /* outInteger */ );

int TA_CDLDOJI_Lookback( void );


/*
 * TA_CDLDOJISTAR - Doji Star
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLDOJISTAR( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDLDOJISTAR_Lookback( void );


/*
 * TA_CDLDRAGONFLYDOJI - Dragonfly Doji
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLDRAGONFLYDOJI( int           START_IDX,
                                int           END_IDX,
                                const double *IN_ARRAY /* inOpen */,
                                const double *IN_ARRAY /* inHigh */,
                                const double *IN_ARRAY /* inLow */,
                                const double *IN_ARRAY /* inClose */,
                                int          *BEG_IDX,
                                int          *OUT_SIZE,
                                int          *OUT_ARRAY /* outInteger */ );

int TA_CDLDRAGONFLYDOJI_Lookback( void );


/*
 * TA_CDLENGULFING - Engulfing Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLENGULFING( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inOpen */,
                            const double *IN_ARRAY /* inHigh */,
                            const double *IN_ARRAY /* inLow */,
                            const double *IN_ARRAY /* inClose */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            int          *OUT_ARRAY /* outInteger */ );

int TA_CDLENGULFING_Lookback( void );


/*
 * TA_CDLEVENINGDOJISTAR - Evening Doji Star
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLEVENINGDOJISTAR( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLEVENINGDOJISTAR_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLEVENINGSTAR - Evening Star
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLEVENINGSTAR( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDLEVENINGSTAR_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLGAPSIDESIDEWHITE - Up/Down-gap side-by-side white lines
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLGAPSIDESIDEWHITE( int           START_IDX,
                                   int           END_IDX,
                                   const double *IN_ARRAY /* inOpen */,
                                   const double *IN_ARRAY /* inHigh */,
                                   const double *IN_ARRAY /* inLow */,
                                   const double *IN_ARRAY /* inClose */,
                                   int          *BEG_IDX,
                                   int          *OUT_SIZE,
                                   int          *OUT_ARRAY /* outInteger */ );

int TA_CDLGAPSIDESIDEWHITE_Lookback( void );


/*
 * TA_CDLGRAVESTONEDOJI - Gravestone Doji
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLGRAVESTONEDOJI( int           START_IDX,
                                 int           END_IDX,
                                 const double *IN_ARRAY /* inOpen */,
                                 const double *IN_ARRAY /* inHigh */,
                                 const double *IN_ARRAY /* inLow */,
                                 const double *IN_ARRAY /* inClose */,
                                 int          *BEG_IDX,
                                 int          *OUT_SIZE,
                                 int          *OUT_ARRAY /* outInteger */ );

int TA_CDLGRAVESTONEDOJI_Lookback( void );


/*
 * TA_CDLHAMMER - Hammer
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHAMMER( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inOpen */,
                         const double *IN_ARRAY /* inHigh */,
                         const double *IN_ARRAY /* inLow */,
                         const double *IN_ARRAY /* inClose */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHAMMER_Lookback( void );


/*
 * TA_CDLHANGINGMAN - Hanging Man
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHANGINGMAN( int           START_IDX,
                             int           END_IDX,
                             const double *IN_ARRAY /* inOpen */,
                             const double *IN_ARRAY /* inHigh */,
                             const double *IN_ARRAY /* inLow */,
                             const double *IN_ARRAY /* inClose */,
                             int          *BEG_IDX,
                             int          *OUT_SIZE,
                             int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHANGINGMAN_Lookback( void );


/*
 * TA_CDLHARAMI - Harami Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHARAMI( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inOpen */,
                         const double *IN_ARRAY /* inHigh */,
                         const double *IN_ARRAY /* inLow */,
                         const double *IN_ARRAY /* inClose */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHARAMI_Lookback( void );


/*
 * TA_CDLHARAMICROSS - Harami Cross Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHARAMICROSS( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHARAMICROSS_Lookback( void );


/*
 * TA_CDLHIGHWAVE - High-Wave Candle
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHIGHWAVE( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHIGHWAVE_Lookback( void );


/*
 * TA_CDLHIKKAKE - Hikkake Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHIKKAKE( int           START_IDX,
                          int           END_IDX,
                          const double *IN_ARRAY /* inOpen */,
                          const double *IN_ARRAY /* inHigh */,
                          const double *IN_ARRAY /* inLow */,
                          const double *IN_ARRAY /* inClose */,
                          int          *BEG_IDX,
                          int          *OUT_SIZE,
                          int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHIKKAKE_Lookback( void );


/*
 * TA_CDLHIKKAKEMOD - Modified Hikkake Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHIKKAKEMOD( int           START_IDX,
                             int           END_IDX,
                             const double *IN_ARRAY /* inOpen */,
                             const double *IN_ARRAY /* inHigh */,
                             const double *IN_ARRAY /* inLow */,
                             const double *IN_ARRAY /* inClose */,
                             int          *BEG_IDX,
                             int          *OUT_SIZE,
                             int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHIKKAKEMOD_Lookback( void );


/*
 * TA_CDLHOMINGPIGEON - Homing Pigeon
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLHOMINGPIGEON( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inOpen */,
                               const double *IN_ARRAY /* inHigh */,
                               const double *IN_ARRAY /* inLow */,
                               const double *IN_ARRAY /* inClose */,
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               int          *OUT_ARRAY /* outInteger */ );

int TA_CDLHOMINGPIGEON_Lookback( void );


/*
 * TA_CDLIDENTICAL3CROWS - Identical Three Crows
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLIDENTICAL3CROWS( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLIDENTICAL3CROWS_Lookback( void );


/*
 * TA_CDLINNECK - In-Neck Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLINNECK( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inOpen */,
                         const double *IN_ARRAY /* inHigh */,
                         const double *IN_ARRAY /* inLow */,
                         const double *IN_ARRAY /* inClose */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         int          *OUT_ARRAY /* outInteger */ );

int TA_CDLINNECK_Lookback( void );


/*
 * TA_CDLINVERTEDHAMMER - Inverted Hammer
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLINVERTEDHAMMER( int           START_IDX,
                                 int           END_IDX,
                                 const double *IN_ARRAY /* inOpen */,
                                 const double *IN_ARRAY /* inHigh */,
                                 const double *IN_ARRAY /* inLow */,
                                 const double *IN_ARRAY /* inClose */,
                                 int          *BEG_IDX,
                                 int          *OUT_SIZE,
                                 int          *OUT_ARRAY /* outInteger */ );

int TA_CDLINVERTEDHAMMER_Lookback( void );


/*
 * TA_CDLKICKING - Kicking
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLKICKING( int           START_IDX,
                          int           END_IDX,
                          const double *IN_ARRAY /* inOpen */,
                          const double *IN_ARRAY /* inHigh */,
                          const double *IN_ARRAY /* inLow */,
                          const double *IN_ARRAY /* inClose */,
                          int          *BEG_IDX,
                          int          *OUT_SIZE,
                          int          *OUT_ARRAY /* outInteger */ );

int TA_CDLKICKING_Lookback( void );


/*
 * TA_CDLKICKINGBYLENGTH - Kicking - bull/bear determined by the longer marubozu
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLKICKINGBYLENGTH( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLKICKINGBYLENGTH_Lookback( void );


/*
 * TA_CDLLADDERBOTTOM - Ladder Bottom
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLLADDERBOTTOM( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inOpen */,
                               const double *IN_ARRAY /* inHigh */,
                               const double *IN_ARRAY /* inLow */,
                               const double *IN_ARRAY /* inClose */,
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               int          *OUT_ARRAY /* outInteger */ );

int TA_CDLLADDERBOTTOM_Lookback( void );


/*
 * TA_CDLLONGLEGGEDDOJI - Long Legged Doji
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLLONGLEGGEDDOJI( int           START_IDX,
                                 int           END_IDX,
                                 const double *IN_ARRAY /* inOpen */,
                                 const double *IN_ARRAY /* inHigh */,
                                 const double *IN_ARRAY /* inLow */,
                                 const double *IN_ARRAY /* inClose */,
                                 int          *BEG_IDX,
                                 int          *OUT_SIZE,
                                 int          *OUT_ARRAY /* outInteger */ );

int TA_CDLLONGLEGGEDDOJI_Lookback( void );


/*
 * TA_CDLLONGLINE - Long Line Candle
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLLONGLINE( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDLLONGLINE_Lookback( void );


/*
 * TA_CDLMARUBOZU - Marubozu
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLMARUBOZU( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDLMARUBOZU_Lookback( void );


/*
 * TA_CDLMATCHINGLOW - Matching Low
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLMATCHINGLOW( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDLMATCHINGLOW_Lookback( void );


/*
 * TA_CDLMATHOLD - Mat Hold
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLMATHOLD( int           START_IDX,
                          int           END_IDX,
                          const double *IN_ARRAY /* inOpen */,
                          const double *IN_ARRAY /* inHigh */,
                          const double *IN_ARRAY /* inLow */,
                          const double *IN_ARRAY /* inClose */,
                          double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                          int          *BEG_IDX,
                          int          *OUT_SIZE,
                          int          *OUT_ARRAY /* outInteger */ );

int TA_CDLMATHOLD_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLMORNINGDOJISTAR - Morning Doji Star
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLMORNINGDOJISTAR( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLMORNINGDOJISTAR_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLMORNINGSTAR - Morning Star
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInPenetration:(From 0 to TA_REAL_MAX)
 *    Percentage of penetration of a candle within another candle
 * 
 * 
 */
TA_RetCode TA_CDLMORNINGSTAR( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              double        OPT_REAL /* optInPenetration */, /* From 0 to TA_REAL_MAX */
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDLMORNINGSTAR_Lookback( double        optInPenetration );  /* From 0 to TA_REAL_MAX */


/*
 * TA_CDLONNECK - On-Neck Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLONNECK( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inOpen */,
                         const double *IN_ARRAY /* inHigh */,
                         const double *IN_ARRAY /* inLow */,
                         const double *IN_ARRAY /* inClose */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         int          *OUT_ARRAY /* outInteger */ );

int TA_CDLONNECK_Lookback( void );


/*
 * TA_CDLPIERCING - Piercing Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLPIERCING( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inOpen */,
                           const double *IN_ARRAY /* inHigh */,
                           const double *IN_ARRAY /* inLow */,
                           const double *IN_ARRAY /* inClose */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outInteger */ );

int TA_CDLPIERCING_Lookback( void );


/*
 * TA_CDLRICKSHAWMAN - Rickshaw Man
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLRICKSHAWMAN( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDLRICKSHAWMAN_Lookback( void );


/*
 * TA_CDLRISEFALL3METHODS - Rising/Falling Three Methods
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLRISEFALL3METHODS( int           START_IDX,
                                   int           END_IDX,
                                   const double *IN_ARRAY /* inOpen */,
                                   const double *IN_ARRAY /* inHigh */,
                                   const double *IN_ARRAY /* inLow */,
                                   const double *IN_ARRAY /* inClose */,
                                   int          *BEG_IDX,
                                   int          *OUT_SIZE,
                                   int          *OUT_ARRAY /* outInteger */ );

int TA_CDLRISEFALL3METHODS_Lookback( void );


/*
 * TA_CDLSEPARATINGLINES - Separating Lines
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLSEPARATINGLINES( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLSEPARATINGLINES_Lookback( void );


/*
 * TA_CDLSHOOTINGSTAR - Shooting Star
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLSHOOTINGSTAR( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inOpen */,
                               const double *IN_ARRAY /* inHigh */,
                               const double *IN_ARRAY /* inLow */,
                               const double *IN_ARRAY /* inClose */,
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               int          *OUT_ARRAY /* outInteger */ );

int TA_CDLSHOOTINGSTAR_Lookback( void );


/*
 * TA_CDLSHORTLINE - Short Line Candle
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLSHORTLINE( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inOpen */,
                            const double *IN_ARRAY /* inHigh */,
                            const double *IN_ARRAY /* inLow */,
                            const double *IN_ARRAY /* inClose */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            int          *OUT_ARRAY /* outInteger */ );

int TA_CDLSHORTLINE_Lookback( void );


/*
 * TA_CDLSPINNINGTOP - Spinning Top
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLSPINNINGTOP( int           START_IDX,
                              int           END_IDX,
                              const double *IN_ARRAY /* inOpen */,
                              const double *IN_ARRAY /* inHigh */,
                              const double *IN_ARRAY /* inLow */,
                              const double *IN_ARRAY /* inClose */,
                              int          *BEG_IDX,
                              int          *OUT_SIZE,
                              int          *OUT_ARRAY /* outInteger */ );

int TA_CDLSPINNINGTOP_Lookback( void );


/*
 * TA_CDLSTALLEDPATTERN - Stalled Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLSTALLEDPATTERN( int           START_IDX,
                                 int           END_IDX,
                                 const double *IN_ARRAY /* inOpen */,
                                 const double *IN_ARRAY /* inHigh */,
                                 const double *IN_ARRAY /* inLow */,
                                 const double *IN_ARRAY /* inClose */,
                                 int          *BEG_IDX,
                                 int          *OUT_SIZE,
                                 int          *OUT_ARRAY /* outInteger */ );

int TA_CDLSTALLEDPATTERN_Lookback( void );


/*
 * TA_CDLSTICKSANDWICH - Stick Sandwich
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLSTICKSANDWICH( int           START_IDX,
                                int           END_IDX,
                                const double *IN_ARRAY /* inOpen */,
                                const double *IN_ARRAY /* inHigh */,
                                const double *IN_ARRAY /* inLow */,
                                const double *IN_ARRAY /* inClose */,
                                int          *BEG_IDX,
                                int          *OUT_SIZE,
                                int          *OUT_ARRAY /* outInteger */ );

int TA_CDLSTICKSANDWICH_Lookback( void );


/*
 * TA_CDLTAKURI - Takuri (Dragonfly Doji with very long lower shadow)
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLTAKURI( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inOpen */,
                         const double *IN_ARRAY /* inHigh */,
                         const double *IN_ARRAY /* inLow */,
                         const double *IN_ARRAY /* inClose */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         int          *OUT_ARRAY /* outInteger */ );

int TA_CDLTAKURI_Lookback( void );


/*
 * TA_CDLTASUKIGAP - Tasuki Gap
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLTASUKIGAP( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inOpen */,
                            const double *IN_ARRAY /* inHigh */,
                            const double *IN_ARRAY /* inLow */,
                            const double *IN_ARRAY /* inClose */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            int          *OUT_ARRAY /* outInteger */ );

int TA_CDLTASUKIGAP_Lookback( void );


/*
 * TA_CDLTHRUSTING - Thrusting Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLTHRUSTING( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inOpen */,
                            const double *IN_ARRAY /* inHigh */,
                            const double *IN_ARRAY /* inLow */,
                            const double *IN_ARRAY /* inClose */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            int          *OUT_ARRAY /* outInteger */ );

int TA_CDLTHRUSTING_Lookback( void );


/*
 * TA_CDLTRISTAR - Tristar Pattern
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLTRISTAR( int           START_IDX,
                          int           END_IDX,
                          const double *IN_ARRAY /* inOpen */,
                          const double *IN_ARRAY /* inHigh */,
                          const double *IN_ARRAY /* inLow */,
                          const double *IN_ARRAY /* inClose */,
                          int          *BEG_IDX,
                          int          *OUT_SIZE,
                          int          *OUT_ARRAY /* outInteger */ );

int TA_CDLTRISTAR_Lookback( void );


/*
 * TA_CDLUNIQUE3RIVER - Unique 3 River
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLUNIQUE3RIVER( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inOpen */,
                               const double *IN_ARRAY /* inHigh */,
                               const double *IN_ARRAY /* inLow */,
                               const double *IN_ARRAY /* inClose */,
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               int          *OUT_ARRAY /* outInteger */ );

int TA_CDLUNIQUE3RIVER_Lookback( void );


/*
 * TA_CDLUPSIDEGAP2CROWS - Upside Gap Two Crows
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLUPSIDEGAP2CROWS( int           START_IDX,
                                  int           END_IDX,
                                  const double *IN_ARRAY /* inOpen */,
                                  const double *IN_ARRAY /* inHigh */,
                                  const double *IN_ARRAY /* inLow */,
                                  const double *IN_ARRAY /* inClose */,
                                  int          *BEG_IDX,
                                  int          *OUT_SIZE,
                                  int          *OUT_ARRAY /* outInteger */ );

int TA_CDLUPSIDEGAP2CROWS_Lookback( void );


/*
 * TA_CDLXSIDEGAP3METHODS - Upside/Downside Gap Three Methods
 * 
 * Input  = Open, High, Low, Close
 * Output = int
 * 
 */
TA_RetCode TA_CDLXSIDEGAP3METHODS( int           START_IDX,
                                   int           END_IDX,
                                   const double *IN_ARRAY /* inOpen */,
                                   const double *IN_ARRAY /* inHigh */,
                                   const double *IN_ARRAY /* inLow */,
                                   const double *IN_ARRAY /* inClose */,
                                   int          *BEG_IDX,
                                   int          *OUT_SIZE,
                                   int          *OUT_ARRAY /* outInteger */ );

int TA_CDLXSIDEGAP3METHODS_Lookback( void );


/*
 * TA_CEIL - Vector Ceil
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_CEIL( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_CEIL_Lookback( void );


/*
 * TA_CMO - Chande Momentum Oscillator
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_CMO( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_CMO_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_CORREL - Pearson's Correlation Coefficient (r)
 * 
 * Input  = double, double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_CORREL( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inReal0 */,
                      const double *IN_ARRAY /* inReal1 */,
                      int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outReal */ );

int TA_CORREL_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_COS - Vector Trigonometric Cos
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_COS( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_COS_Lookback( void );


/*
 * TA_COSH - Vector Trigonometric Cosh
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_COSH( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_COSH_Lookback( void );


/*
 * TA_CR - CR
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_CR( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inHigh */,
                  const double *IN_ARRAY /* inLow */,
                  const double *IN_ARRAY /* inClose */,
                  int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outReal */ );

int TA_CR_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_DEMA - Double Exponential Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_DEMA( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_DEMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_DIV - Vector Arithmetic Div
 * 
 * Input  = double, double
 * Output = double
 * 
 */
TA_RetCode TA_DIV( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal0 */,
                   const double *IN_ARRAY /* inReal1 */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_DIV_Lookback( void );


/*
 * TA_DX - Directional Movement Index
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_DX( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inHigh */,
                  const double *IN_ARRAY /* inLow */,
                  const double *IN_ARRAY /* inClose */,
                  int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outReal */ );

int TA_DX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_EMA - Exponential Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_EMA( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_EMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_EMV - Ease of Movement Value
 * 
 * Input  = High, Low, Close, Volume
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_EMV( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   const double *IN_ARRAY /* inClose */,
                   const double *IN_ARRAY /* inVolume */,
                   int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_EMV_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_EXP - Vector Arithmetic Exp
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_EXP( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_EXP_Lookback( void );


/*
 * TA_FLOOR - Vector Floor
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_FLOOR( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inReal */,
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outReal */ );

int TA_FLOOR_Lookback( void );


/*
 * TA_HT_DCPERIOD - Hilbert Transform - Dominant Cycle Period
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_HT_DCPERIOD( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inReal */,
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           double       *OUT_ARRAY /* outReal */ );

int TA_HT_DCPERIOD_Lookback( void );


/*
 * TA_HT_DCPHASE - Hilbert Transform - Dominant Cycle Phase
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_HT_DCPHASE( int           START_IDX,
                          int           END_IDX,
                          const double *IN_ARRAY /* inReal */,
                          int          *BEG_IDX,
                          int          *OUT_SIZE,
                          double       *OUT_ARRAY /* outReal */ );

int TA_HT_DCPHASE_Lookback( void );


/*
 * TA_HT_PHASOR - Hilbert Transform - Phasor Components
 * 
 * Input  = double
 * Output = double, double
 * 
 */
TA_RetCode TA_HT_PHASOR( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inReal */,
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         double       *OUT_ARRAY /* outInPhase */,
                         double       *OUT_ARRAY /* outQuadrature */ );

int TA_HT_PHASOR_Lookback( void );


/*
 * TA_HT_SINE - Hilbert Transform - SineWave
 * 
 * Input  = double
 * Output = double, double
 * 
 */
TA_RetCode TA_HT_SINE( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inReal */,
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       double       *OUT_ARRAY /* outSine */,
                       double       *OUT_ARRAY /* outLeadSine */ );

int TA_HT_SINE_Lookback( void );


/*
 * TA_HT_TRENDLINE - Hilbert Transform - Instantaneous Trendline
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_HT_TRENDLINE( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inReal */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            double       *OUT_ARRAY /* outReal */ );

int TA_HT_TRENDLINE_Lookback( void );


/*
 * TA_HT_TRENDMODE - Hilbert Transform - Trend vs Cycle Mode
 * 
 * Input  = double
 * Output = int
 * 
 */
TA_RetCode TA_HT_TRENDMODE( int           START_IDX,
                            int           END_IDX,
                            const double *IN_ARRAY /* inReal */,
                            int          *BEG_IDX,
                            int          *OUT_SIZE,
                            int          *OUT_ARRAY /* outInteger */ );

int TA_HT_TRENDMODE_Lookback( void );


/*
 * TA_KAMA - Kaufman Adaptive Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_KAMA( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_KAMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_KC - Keltener Channel
 * 
 * Input  = High, Low, Close
 * Output = double, double, double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * optInNbAtrMult:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Deviation multiplier for ATR
 * 
 * 
 */
TA_RetCode TA_KC( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inHigh */,
                  const double *IN_ARRAY /* inLow */,
                  const double *IN_ARRAY /* inClose */,
                  int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                  double        OPT_REAL /* optInNbAtrMult */, /* From TA_REAL_MIN to TA_REAL_MAX */
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outRealUpperKC */,
                  double       *OUT_ARRAY /* outRealMiddleKC */,
                  double       *OUT_ARRAY /* outRealLowerKC */ );

int TA_KC_Lookback( int           optInTimePeriod, /* From 1 to 100000 */
                  double        optInNbAtrMult );  /* From TA_REAL_MIN to TA_REAL_MAX */


/*
 * TA_LINEARREG - Linear Regression
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_LINEARREG( int           START_IDX,
                         int           END_IDX,
                         const double *IN_ARRAY /* inReal */,
                         int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                         int          *BEG_IDX,
                         int          *OUT_SIZE,
                         double       *OUT_ARRAY /* outReal */ );

int TA_LINEARREG_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_LINEARREG_ANGLE - Linear Regression Angle
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_LINEARREG_ANGLE( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inReal */,
                               int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               double       *OUT_ARRAY /* outReal */ );

int TA_LINEARREG_ANGLE_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_LINEARREG_INTERCEPT - Linear Regression Intercept
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_LINEARREG_INTERCEPT( int           START_IDX,
                                   int           END_IDX,
                                   const double *IN_ARRAY /* inReal */,
                                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                                   int          *BEG_IDX,
                                   int          *OUT_SIZE,
                                   double       *OUT_ARRAY /* outReal */ );

int TA_LINEARREG_INTERCEPT_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_LINEARREG_SLOPE - Linear Regression Slope
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_LINEARREG_SLOPE( int           START_IDX,
                               int           END_IDX,
                               const double *IN_ARRAY /* inReal */,
                               int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                               int          *BEG_IDX,
                               int          *OUT_SIZE,
                               double       *OUT_ARRAY /* outReal */ );

int TA_LINEARREG_SLOPE_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_LN - Vector Log Natural
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_LN( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inReal */,
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outReal */ );

int TA_LN_Lookback( void );


/*
 * TA_LOG10 - Vector Log10
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_LOG10( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inReal */,
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outReal */ );

int TA_LOG10_Lookback( void );


/*
 * TA_MA - Moving average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_MA( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inReal */,
                  int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                  TA_MAType     OPT_MATYPE /* optInMAType */,
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outReal */ );

int TA_MA_Lookback( int           optInTimePeriod, /* From 1 to 100000 */
                  TA_MAType     optInMAType ); 

/*
 * TA_MACD - Moving Average Convergence/Divergence
 * 
 * Input  = double
 * Output = double, double, double
 * 
 * Optional Parameters
 * -------------------
 * optInFastPeriod:(From 2 to 100000)
 *    Number of period for the fast MA
 * 
 * optInSlowPeriod:(From 2 to 100000)
 *    Number of period for the slow MA
 * 
 * optInSignalPeriod:(From 1 to 100000)
 *    Smoothing for the signal line (nb of period)
 * 
 * 
 */
TA_RetCode TA_MACD( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInFastPeriod */, /* From 2 to 100000 */
                    int           OPT_INT /* optInSlowPeriod */, /* From 2 to 100000 */
                    int           OPT_INT /* optInSignalPeriod */, /* From 1 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outMACD */,
                    double       *OUT_ARRAY /* outMACDSignal */,
                    double       *OUT_ARRAY /* outMACDHist */ );

int TA_MACD_Lookback( int           optInFastPeriod, /* From 2 to 100000 */
                    int           optInSlowPeriod, /* From 2 to 100000 */
                    int           optInSignalPeriod );  /* From 1 to 100000 */


/*
 * TA_MACDEXT - MACD with controllable MA type
 * 
 * Input  = double
 * Output = double, double, double
 * 
 * Optional Parameters
 * -------------------
 * optInFastPeriod:(From 2 to 100000)
 *    Number of period for the fast MA
 * 
 * optInFastMAType:
 *    Type of Moving Average for fast MA
 * 
 * optInSlowPeriod:(From 2 to 100000)
 *    Number of period for the slow MA
 * 
 * optInSlowMAType:
 *    Type of Moving Average for slow MA
 * 
 * optInSignalPeriod:(From 1 to 100000)
 *    Smoothing for the signal line (nb of period)
 * 
 * optInSignalMAType:
 *    Type of Moving Average for signal line
 * 
 * 
 */
TA_RetCode TA_MACDEXT( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inReal */,
                       int           OPT_INT /* optInFastPeriod */, /* From 2 to 100000 */
                       TA_MAType     OPT_MATYPE /* optInFastMAType */,
                       int           OPT_INT /* optInSlowPeriod */, /* From 2 to 100000 */
                       TA_MAType     OPT_MATYPE /* optInSlowMAType */,
                       int           OPT_INT /* optInSignalPeriod */, /* From 1 to 100000 */
                       TA_MAType     OPT_MATYPE /* optInSignalMAType */,
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       double       *OUT_ARRAY /* outMACD */,
                       double       *OUT_ARRAY /* outMACDSignal */,
                       double       *OUT_ARRAY /* outMACDHist */ );

int TA_MACDEXT_Lookback( int           optInFastPeriod, /* From 2 to 100000 */
                       TA_MAType     optInFastMAType,
                       int           optInSlowPeriod, /* From 2 to 100000 */
                       TA_MAType     optInSlowMAType,
                       int           optInSignalPeriod, /* From 1 to 100000 */
                       TA_MAType     optInSignalMAType ); 

/*
 * TA_MACDFIX - Moving Average Convergence/Divergence Fix 12/26
 * 
 * Input  = double
 * Output = double, double, double
 * 
 * Optional Parameters
 * -------------------
 * optInSignalPeriod:(From 1 to 100000)
 *    Smoothing for the signal line (nb of period)
 * 
 * 
 */
TA_RetCode TA_MACDFIX( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inReal */,
                       int           OPT_INT /* optInSignalPeriod */, /* From 1 to 100000 */
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       double       *OUT_ARRAY /* outMACD */,
                       double       *OUT_ARRAY /* outMACDSignal */,
                       double       *OUT_ARRAY /* outMACDHist */ );

int TA_MACDFIX_Lookback( int           optInSignalPeriod );  /* From 1 to 100000 */


/*
 * TA_MAMA - MESA Adaptive Moving Average
 * 
 * Input  = double
 * Output = double, double
 * 
 * Optional Parameters
 * -------------------
 * optInFastLimit:(From 0.01 to 0.99)
 *    Upper limit use in the adaptive algorithm
 * 
 * optInSlowLimit:(From 0.01 to 0.99)
 *    Lower limit use in the adaptive algorithm
 * 
 * 
 */
TA_RetCode TA_MAMA( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    double        OPT_REAL /* optInFastLimit */, /* From 0.01 to 0.99 */
                    double        OPT_REAL /* optInSlowLimit */, /* From 0.01 to 0.99 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outMAMA */,
                    double       *OUT_ARRAY /* outFAMA */ );

int TA_MAMA_Lookback( double        optInFastLimit, /* From 0.01 to 0.99 */
                    double        optInSlowLimit );  /* From 0.01 to 0.99 */


/*
 * TA_MAVP - Moving average with variable period
 * 
 * Input  = double, double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInMinPeriod:(From 2 to 100000)
 *    Value less than minimum will be changed to Minimum period
 * 
 * optInMaxPeriod:(From 2 to 100000)
 *    Value higher than maximum will be changed to Maximum period
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_MAVP( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    const double *IN_ARRAY /* inPeriods */,
                    int           OPT_INT /* optInMinPeriod */, /* From 2 to 100000 */
                    int           OPT_INT /* optInMaxPeriod */, /* From 2 to 100000 */
                    TA_MAType     OPT_MATYPE /* optInMAType */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_MAVP_Lookback( int           optInMinPeriod, /* From 2 to 100000 */
                    int           optInMaxPeriod, /* From 2 to 100000 */
                    TA_MAType     optInMAType ); 

/*
 * TA_MAX - Highest value over a specified period
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MAX( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_MAX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MAXINDEX - Index of highest value over a specified period
 * 
 * Input  = double
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MAXINDEX( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inReal */,
                        int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        int          *OUT_ARRAY /* outInteger */ );

int TA_MAXINDEX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MEDPRICE - Median Price
 * 
 * Input  = High, Low
 * Output = double
 * 
 */
TA_RetCode TA_MEDPRICE( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_MEDPRICE_Lookback( void );


/*
 * TA_MFI - Money Flow Index
 * 
 * Input  = High, Low, Close, Volume
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MFI( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   const double *IN_ARRAY /* inClose */,
                   const double *IN_ARRAY /* inVolume */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_MFI_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MIDPOINT - MidPoint over period
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MIDPOINT( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inReal */,
                        int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_MIDPOINT_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MIDPRICE - Midpoint Price over period
 * 
 * Input  = High, Low
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MIDPRICE( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_MIDPRICE_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MIN - Lowest value over a specified period
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MIN( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_MIN_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MININDEX - Index of lowest value over a specified period
 * 
 * Input  = double
 * Output = int
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MININDEX( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inReal */,
                        int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        int          *OUT_ARRAY /* outInteger */ );

int TA_MININDEX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MINMAX - Lowest and highest values over a specified period
 * 
 * Input  = double
 * Output = double, double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MINMAX( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inReal */,
                      int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outMin */,
                      double       *OUT_ARRAY /* outMax */ );

int TA_MINMAX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MINMAXINDEX - Indexes of lowest and highest values over a specified period
 * 
 * Input  = double
 * Output = int, int
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MINMAXINDEX( int           START_IDX,
                           int           END_IDX,
                           const double *IN_ARRAY /* inReal */,
                           int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                           int          *BEG_IDX,
                           int          *OUT_SIZE,
                           int          *OUT_ARRAY /* outMinIdx */,
                           int          *OUT_ARRAY /* outMaxIdx */ );

int TA_MINMAXINDEX_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_MINUS_DI - Minus Directional Indicator
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MINUS_DI( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        const double *IN_ARRAY /* inClose */,
                        int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_MINUS_DI_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_MINUS_DM - Minus Directional Movement
 * 
 * Input  = High, Low
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MINUS_DM( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_MINUS_DM_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_MOM - Momentum
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_MOM( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_MOM_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_MULT - Vector Arithmetic Mult
 * 
 * Input  = double, double
 * Output = double
 * 
 */
TA_RetCode TA_MULT( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal0 */,
                    const double *IN_ARRAY /* inReal1 */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_MULT_Lookback( void );


/*
 * TA_NATR - Normalized Average True Range
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_NATR( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inHigh */,
                    const double *IN_ARRAY /* inLow */,
                    const double *IN_ARRAY /* inClose */,
                    int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_NATR_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_OBV - On Balance Volume
 * 
 * Input  = double, Volume
 * Output = double
 * 
 */
TA_RetCode TA_OBV( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   const double *IN_ARRAY /* inVolume */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_OBV_Lookback( void );


/*
 * TA_PIVOT - Pivot a specified period
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_PIVOT( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inReal */,
                     int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outReal */ );

int TA_PIVOT_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_PLUS_DI - Plus Directional Indicator
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_PLUS_DI( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inHigh */,
                       const double *IN_ARRAY /* inLow */,
                       const double *IN_ARRAY /* inClose */,
                       int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       double       *OUT_ARRAY /* outReal */ );

int TA_PLUS_DI_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_PLUS_DM - Plus Directional Movement
 * 
 * Input  = High, Low
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_PLUS_DM( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inHigh */,
                       const double *IN_ARRAY /* inLow */,
                       int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       double       *OUT_ARRAY /* outReal */ );

int TA_PLUS_DM_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_PPO - Percentage Price Oscillator
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInFastPeriod:(From 2 to 100000)
 *    Number of period for the fast MA
 * 
 * optInSlowPeriod:(From 2 to 100000)
 *    Number of period for the slow MA
 * 
 * optInMAType:
 *    Type of Moving Average
 * 
 * 
 */
TA_RetCode TA_PPO( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInFastPeriod */, /* From 2 to 100000 */
                   int           OPT_INT /* optInSlowPeriod */, /* From 2 to 100000 */
                   TA_MAType     OPT_MATYPE /* optInMAType */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_PPO_Lookback( int           optInFastPeriod, /* From 2 to 100000 */
                   int           optInSlowPeriod, /* From 2 to 100000 */
                   TA_MAType     optInMAType ); 

/*
 * TA_PVT - Positive Volume Trend
 * 
 * Input  = Close, Volume
 * Output = double
 * 
 */
TA_RetCode TA_PVT( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inClose */,
                   const double *IN_ARRAY /* inVolume */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_PVT_Lookback( void );


/*
 * TA_ROC - Rate of change : ((price/prevPrice)-1)*100
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ROC( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_ROC_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_ROCP - Rate of change Percentage: (price-prevPrice)/prevPrice
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ROCP( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_ROCP_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_ROCR - Rate of change ratio: (price/prevPrice)
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ROCR( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_ROCR_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_ROCR100 - Rate of change ratio 100 scale: (price/prevPrice)*100
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_ROCR100( int           START_IDX,
                       int           END_IDX,
                       const double *IN_ARRAY /* inReal */,
                       int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                       int          *BEG_IDX,
                       int          *OUT_SIZE,
                       double       *OUT_ARRAY /* outReal */ );

int TA_ROCR100_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_RSI - Relative Strength Index
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_RSI( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_RSI_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_SAR - Parabolic SAR
 * 
 * Input  = High, Low
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInAcceleration:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor used up to the Maximum value
 * 
 * optInMaximum:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor Maximum value
 * 
 * 
 */
TA_RetCode TA_SAR( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inHigh */,
                   const double *IN_ARRAY /* inLow */,
                   double        OPT_REAL /* optInAcceleration */, /* From 0 to TA_REAL_MAX */
                   double        OPT_REAL /* optInMaximum */, /* From 0 to TA_REAL_MAX */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_SAR_Lookback( double        optInAcceleration, /* From 0 to TA_REAL_MAX */
                   double        optInMaximum );  /* From 0 to TA_REAL_MAX */


/*
 * TA_SAREXT - Parabolic SAR - Extended
 * 
 * Input  = High, Low
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInStartValue:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Start value and direction. 0 for Auto, >0 for Long, <0 for Short
 * 
 * optInOffsetOnReverse:(From 0 to TA_REAL_MAX)
 *    Percent offset added/removed to initial stop on short/long reversal
 * 
 * optInAccelerationInitLong:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor initial value for the Long direction
 * 
 * optInAccelerationLong:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor for the Long direction
 * 
 * optInAccelerationMaxLong:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor maximum value for the Long direction
 * 
 * optInAccelerationInitShort:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor initial value for the Short direction
 * 
 * optInAccelerationShort:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor for the Short direction
 * 
 * optInAccelerationMaxShort:(From 0 to TA_REAL_MAX)
 *    Acceleration Factor maximum value for the Short direction
 * 
 * 
 */
TA_RetCode TA_SAREXT( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inHigh */,
                      const double *IN_ARRAY /* inLow */,
                      double        OPT_REAL /* optInStartValue */, /* From TA_REAL_MIN to TA_REAL_MAX */
                      double        OPT_REAL /* optInOffsetOnReverse */, /* From 0 to TA_REAL_MAX */
                      double        OPT_REAL /* optInAccelerationInitLong */, /* From 0 to TA_REAL_MAX */
                      double        OPT_REAL /* optInAccelerationLong */, /* From 0 to TA_REAL_MAX */
                      double        OPT_REAL /* optInAccelerationMaxLong */, /* From 0 to TA_REAL_MAX */
                      double        OPT_REAL /* optInAccelerationInitShort */, /* From 0 to TA_REAL_MAX */
                      double        OPT_REAL /* optInAccelerationShort */, /* From 0 to TA_REAL_MAX */
                      double        OPT_REAL /* optInAccelerationMaxShort */, /* From 0 to TA_REAL_MAX */
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outReal */ );

int TA_SAREXT_Lookback( double        optInStartValue, /* From TA_REAL_MIN to TA_REAL_MAX */
                      double        optInOffsetOnReverse, /* From 0 to TA_REAL_MAX */
                      double        optInAccelerationInitLong, /* From 0 to TA_REAL_MAX */
                      double        optInAccelerationLong, /* From 0 to TA_REAL_MAX */
                      double        optInAccelerationMaxLong, /* From 0 to TA_REAL_MAX */
                      double        optInAccelerationInitShort, /* From 0 to TA_REAL_MAX */
                      double        optInAccelerationShort, /* From 0 to TA_REAL_MAX */
                      double        optInAccelerationMaxShort );  /* From 0 to TA_REAL_MAX */


/*
 * TA_SIN - Vector Trigonometric Sin
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_SIN( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_SIN_Lookback( void );


/*
 * TA_SINH - Vector Trigonometric Sinh
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_SINH( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_SINH_Lookback( void );


/*
 * TA_SMA - Simple Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_SMA( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_SMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_SQRT - Vector Square Root
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_SQRT( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_SQRT_Lookback( void );


/*
 * TA_STDDEV - Standard Deviation
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * optInNbDev:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Nb of deviations
 * 
 * 
 */
TA_RetCode TA_STDDEV( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inReal */,
                      int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                      double        OPT_REAL /* optInNbDev */, /* From TA_REAL_MIN to TA_REAL_MAX */
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outReal */ );

int TA_STDDEV_Lookback( int           optInTimePeriod, /* From 2 to 100000 */
                      double        optInNbDev );  /* From TA_REAL_MIN to TA_REAL_MAX */


/*
 * TA_STOCH - Stochastic
 * 
 * Input  = High, Low, Close
 * Output = double, double
 * 
 * Optional Parameters
 * -------------------
 * optInFastK_Period:(From 1 to 100000)
 *    Time period for building the Fast-K line
 * 
 * optInSlowK_Period:(From 1 to 100000)
 *    Smoothing for making the Slow-K line. Usually set to 3
 * 
 * optInSlowK_MAType:
 *    Type of Moving Average for Slow-K
 * 
 * optInSlowD_Period:(From 1 to 100000)
 *    Smoothing for making the Slow-D line
 * 
 * optInSlowD_MAType:
 *    Type of Moving Average for Slow-D
 * 
 * 
 */
TA_RetCode TA_STOCH( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inHigh */,
                     const double *IN_ARRAY /* inLow */,
                     const double *IN_ARRAY /* inClose */,
                     int           OPT_INT /* optInFastK_Period */, /* From 1 to 100000 */
                     int           OPT_INT /* optInSlowK_Period */, /* From 1 to 100000 */
                     TA_MAType     OPT_MATYPE /* optInSlowK_MAType */,
                     int           OPT_INT /* optInSlowD_Period */, /* From 1 to 100000 */
                     TA_MAType     OPT_MATYPE /* optInSlowD_MAType */,
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outSlowK */,
                     double       *OUT_ARRAY /* outSlowD */ );

int TA_STOCH_Lookback( int           optInFastK_Period, /* From 1 to 100000 */
                     int           optInSlowK_Period, /* From 1 to 100000 */
                     TA_MAType     optInSlowK_MAType,
                     int           optInSlowD_Period, /* From 1 to 100000 */
                     TA_MAType     optInSlowD_MAType ); 

/*
 * TA_STOCHF - Stochastic Fast
 * 
 * Input  = High, Low, Close
 * Output = double, double
 * 
 * Optional Parameters
 * -------------------
 * optInFastK_Period:(From 1 to 100000)
 *    Time period for building the Fast-K line
 * 
 * optInFastD_Period:(From 1 to 100000)
 *    Smoothing for making the Fast-D line. Usually set to 3
 * 
 * optInFastD_MAType:
 *    Type of Moving Average for Fast-D
 * 
 * 
 */
TA_RetCode TA_STOCHF( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inHigh */,
                      const double *IN_ARRAY /* inLow */,
                      const double *IN_ARRAY /* inClose */,
                      int           OPT_INT /* optInFastK_Period */, /* From 1 to 100000 */
                      int           OPT_INT /* optInFastD_Period */, /* From 1 to 100000 */
                      TA_MAType     OPT_MATYPE /* optInFastD_MAType */,
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outFastK */,
                      double       *OUT_ARRAY /* outFastD */ );

int TA_STOCHF_Lookback( int           optInFastK_Period, /* From 1 to 100000 */
                      int           optInFastD_Period, /* From 1 to 100000 */
                      TA_MAType     optInFastD_MAType ); 

/*
 * TA_STOCHRSI - Stochastic Relative Strength Index
 * 
 * Input  = double
 * Output = double, double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * optInFastK_Period:(From 1 to 100000)
 *    Time period for building the Fast-K line
 * 
 * optInFastD_Period:(From 1 to 100000)
 *    Smoothing for making the Fast-D line. Usually set to 3
 * 
 * optInFastD_MAType:
 *    Type of Moving Average for Fast-D
 * 
 * 
 */
TA_RetCode TA_STOCHRSI( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inReal */,
                        int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                        int           OPT_INT /* optInFastK_Period */, /* From 1 to 100000 */
                        int           OPT_INT /* optInFastD_Period */, /* From 1 to 100000 */
                        TA_MAType     OPT_MATYPE /* optInFastD_MAType */,
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outFastK */,
                        double       *OUT_ARRAY /* outFastD */ );

int TA_STOCHRSI_Lookback( int           optInTimePeriod, /* From 2 to 100000 */
                        int           optInFastK_Period, /* From 1 to 100000 */
                        int           optInFastD_Period, /* From 1 to 100000 */
                        TA_MAType     optInFastD_MAType ); 

/*
 * TA_SUB - Vector Arithmetic Substraction
 * 
 * Input  = double, double
 * Output = double
 * 
 */
TA_RetCode TA_SUB( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal0 */,
                   const double *IN_ARRAY /* inReal1 */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_SUB_Lookback( void );


/*
 * TA_SUM - Summation
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_SUM( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_SUM_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_T3 - Triple Exponential Moving Average (T3)
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * optInVFactor:(From 0 to 1)
 *    Volume Factor
 * 
 * 
 */
TA_RetCode TA_T3( int           START_IDX,
                  int           END_IDX,
                  const double *IN_ARRAY /* inReal */,
                  int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                  double        OPT_REAL /* optInVFactor */, /* From 0 to 1 */
                  int          *BEG_IDX,
                  int          *OUT_SIZE,
                  double       *OUT_ARRAY /* outReal */ );

int TA_T3_Lookback( int           optInTimePeriod, /* From 2 to 100000 */
                  double        optInVFactor );  /* From 0 to 1 */


/*
 * TA_TAN - Vector Trigonometric Tan
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_TAN( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_TAN_Lookback( void );


/*
 * TA_TANH - Vector Trigonometric Tanh
 * 
 * Input  = double
 * Output = double
 * 
 */
TA_RetCode TA_TANH( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_TANH_Lookback( void );


/*
 * TA_TEMA - Triple Exponential Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_TEMA( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_TEMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_TRANGE - True Range
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 */
TA_RetCode TA_TRANGE( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inHigh */,
                      const double *IN_ARRAY /* inLow */,
                      const double *IN_ARRAY /* inClose */,
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outReal */ );

int TA_TRANGE_Lookback( void );


/*
 * TA_TRIMA - Triangular Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_TRIMA( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inReal */,
                     int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outReal */ );

int TA_TRIMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_TRIX - 1-day Rate-Of-Change (ROC) of a Triple Smooth EMA
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_TRIX( int           START_IDX,
                    int           END_IDX,
                    const double *IN_ARRAY /* inReal */,
                    int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                    int          *BEG_IDX,
                    int          *OUT_SIZE,
                    double       *OUT_ARRAY /* outReal */ );

int TA_TRIX_Lookback( int           optInTimePeriod );  /* From 1 to 100000 */


/*
 * TA_TSF - Time Series Forecast
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_TSF( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_TSF_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_TYPPRICE - Typical Price
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 */
TA_RetCode TA_TYPPRICE( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        const double *IN_ARRAY /* inClose */,
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_TYPPRICE_Lookback( void );


/*
 * TA_ULTOSC - Ultimate Oscillator
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod1:(From 1 to 100000)
 *    Number of bars for 1st period.
 * 
 * optInTimePeriod2:(From 1 to 100000)
 *    Number of bars fro 2nd period
 * 
 * optInTimePeriod3:(From 1 to 100000)
 *    Number of bars for 3rd period
 * 
 * 
 */
TA_RetCode TA_ULTOSC( int           START_IDX,
                      int           END_IDX,
                      const double *IN_ARRAY /* inHigh */,
                      const double *IN_ARRAY /* inLow */,
                      const double *IN_ARRAY /* inClose */,
                      int           OPT_INT /* optInTimePeriod1 */, /* From 1 to 100000 */
                      int           OPT_INT /* optInTimePeriod2 */, /* From 1 to 100000 */
                      int           OPT_INT /* optInTimePeriod3 */, /* From 1 to 100000 */
                      int          *BEG_IDX,
                      int          *OUT_SIZE,
                      double       *OUT_ARRAY /* outReal */ );

int TA_ULTOSC_Lookback( int           optInTimePeriod1, /* From 1 to 100000 */
                      int           optInTimePeriod2, /* From 1 to 100000 */
                      int           optInTimePeriod3 );  /* From 1 to 100000 */


/*
 * TA_VAR - Variance
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 1 to 100000)
 *    Number of period
 * 
 * optInNbDev:(From TA_REAL_MIN to TA_REAL_MAX)
 *    Nb of deviations
 * 
 * 
 */
TA_RetCode TA_VAR( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 1 to 100000 */
                   double        OPT_REAL /* optInNbDev */, /* From TA_REAL_MIN to TA_REAL_MAX */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_VAR_Lookback( int           optInTimePeriod, /* From 1 to 100000 */
                   double        optInNbDev );  /* From TA_REAL_MIN to TA_REAL_MAX */


/*
 * TA_WCLPRICE - Weighted Close Price
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 */
TA_RetCode TA_WCLPRICE( int           START_IDX,
                        int           END_IDX,
                        const double *IN_ARRAY /* inHigh */,
                        const double *IN_ARRAY /* inLow */,
                        const double *IN_ARRAY /* inClose */,
                        int          *BEG_IDX,
                        int          *OUT_SIZE,
                        double       *OUT_ARRAY /* outReal */ );

int TA_WCLPRICE_Lookback( void );


/*
 * TA_WILLR - Williams' %R
 * 
 * Input  = High, Low, Close
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_WILLR( int           START_IDX,
                     int           END_IDX,
                     const double *IN_ARRAY /* inHigh */,
                     const double *IN_ARRAY /* inLow */,
                     const double *IN_ARRAY /* inClose */,
                     int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                     int          *BEG_IDX,
                     int          *OUT_SIZE,
                     double       *OUT_ARRAY /* outReal */ );

int TA_WILLR_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/*
 * TA_WMA - Weighted Moving Average
 * 
 * Input  = double
 * Output = double
 * 
 * Optional Parameters
 * -------------------
 * optInTimePeriod:(From 2 to 100000)
 *    Number of period
 * 
 * 
 */
TA_RetCode TA_WMA( int           START_IDX,
                   int           END_IDX,
                   const double *IN_ARRAY /* inReal */,
                   int           OPT_INT /* optInTimePeriod */, /* From 2 to 100000 */
                   int          *BEG_IDX,
                   int          *OUT_SIZE,
                   double       *OUT_ARRAY /* outReal */ );

int TA_WMA_Lookback( int           optInTimePeriod );  /* From 2 to 100000 */


/* Some TA functions takes a certain amount of input data
 * before stabilizing and outputing meaningful data. This is
 * a behavior pertaining to the algo of some TA functions and
 * is not particular to the TA-Lib implementation.
 * TA-Lib allows you to automatically strip off these unstabl
 * data from your output and from any internal processing.
 * (See documentation for more info)
 *
 * Examples:
 *      TA_SetUnstablePeriod( TA_FUNC_UNST_EMA, 30 );
 *           Always strip off 30 price bar for the TA_EMA function.
 *
 *      TA_SetUnstablePeriod( TA_FUNC_UNST_ALL, 30 );
 *           Always strip off 30 price bar from ALL functions
 *           having an unstable period.
 *
 * See ta_defs.h for the enumeration TA_FuncUnstId
 */

TA_RetCode TA_SetUnstablePeriod( TA_FuncUnstId id,
                                 unsigned int  unstablePeriod );

unsigned int TA_GetUnstablePeriod( TA_FuncUnstId id );

/* You can change slightly the behavior of the TA functions
 * by requesting compatibiliy with some existing software.
 *
 * By default, the behavior is as close as the original 
 * author of the TA functions intend it to be.
 *
 * See ta_defs.h for the enumeration TA_Compatibility.
 */
TA_RetCode TA_SetCompatibility( TA_Compatibility value );
TA_Compatibility TA_GetCompatibility( void );

/* Candlesticks struct and functions
 * Because candlestick patterns are subjective, it is necessary 
 * to allow the user to specify what should be the meaning of 
 * 'long body', 'short shadows', etc.
 */

/* Call TA_SetCandleSettings to set that when comparing a candle 
 * basing on settingType it must be compared with the average 
 * of the last avgPeriod candles' rangeType multiplied by factor.
 * This setting is valid until TA_RestoreCandleDefaultSettings is called
 */
TA_RetCode TA_SetCandleSettings( TA_CandleSettingType settingType, 
                                 TA_RangeType rangeType, 
                                 int avgPeriod, 
                                 double factor );

/* Call TA_RestoreCandleDefaultSettings after using custom settings 
 * to restore the default settings for the specified settingType
 */
TA_RetCode TA_RestoreCandleDefaultSettings( TA_CandleSettingType settingType );

/***************
 * End of File *
 ***************/
