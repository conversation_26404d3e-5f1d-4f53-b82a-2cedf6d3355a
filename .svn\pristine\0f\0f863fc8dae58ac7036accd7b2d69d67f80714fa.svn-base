#pragma once
#include <Instrument.h>
#include <Strategy.h>
#include <TradeSystem.h>
#include <patterns/singleton.hpp>
#include <slog.hpp>
namespace bll {
	class SimulationStrategyImpl : public SimulationStrategy, public kits::Singleton<SimulationStrategyImpl>
	{
		friend class kits::Singleton < SimulationStrategyImpl >;
		SimulationStrategyImpl();
	public:
		~SimulationStrategyImpl();
		bool Init(PORTFOLIO_TYPE type, double units = 300000, double amount = 30000);

		void OnStrategyStart();
		void OnStrategyStop();
		bool IsStart() { return _bStart; }

		void OnTick(const std::string& label);
		void OnBar(const std::string& label);
		int GetPositionSize(const std::string& label, Portfolio* pf_ptr, PositionSide side);

		void SetBacktestParameter(BacktestParameter& param);
		void SetFactorDataEx(BlockDataPtr blk_ptr, FactorDataEx* long_fde_ptr, FactorDataEx* short_fde_ptr = nullptr);
		Portfolio* GetPortfolio() { return _pf_ptr; }

		void ClearDb();

	private:

		void Execute(dal::InstrumentPtr itm_ptr);

		bool IsLongEntry(dal::InstrumentPtr itm_ptr);
		bool IsLongStop(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);
		bool IsLongExit(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);
		bool IsLongAdd(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);
		bool IsLongReduce(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);

		bool IsShortEntry(dal::InstrumentPtr itm_ptr);
		bool IsShortStop(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);
		bool IsShortExit(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);
		bool IsShortAdd(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);
		bool IsShortReduce(dal::InstrumentPtr itm_ptr, ExprContextPtr ctxt);

		int IsOrderExecTime(const std::string& label, int stylelevel, int exectype, StrategyOrderType sot);
		bool IsEnableNewOrder(const std::string& label, POSITION_EFFECT effect = POSITION_OPEN);
		bool IsExecTime(const std::string& label, int exectype, PositionSide side);

		int BuyLong(dal::InstrumentPtr itm_ptr, int qty = 0);
		int BuyShort(dal::InstrumentPtr itm_ptr, int qty = 0);
		bool ClosePosition(dal::InstrumentPtr itm_ptr, const std::string& remark, bool force = false, int qty = 0);

		void UpdateNotify(int id, const std::string& label);
		void OnEvent(const Events& event);

		Portfolio* _pf_ptr;
		BacktestParameter _bp;

		FactorDataEx* _long_fde_ptr;
		FactorDataEx* _short_fde_ptr;
		BlockDataPtr _bind_blk_ptr;
		std::string _bindname;
		double _amount;
		bool _is_on_tick;

		bool _entryEnabled;
		bool _exitOnBarOpen;
		PositionSide _side;

		std::vector<boost::shared_ptr<boost::mutex>> _mutexs;
		std::set<std::string> _fut_accounts;

		static bool _bStart;
		BlockDataPtr _hold_blk_ptr;
		std::shared_ptr<spdlog::logger> _sims_log;
	};

}
