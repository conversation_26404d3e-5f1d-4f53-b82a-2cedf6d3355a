#include <libwebsockets.h>
#include <string>
#include <vector>

#include "InputChecker.h"
#include "TimeService.h"
#include "Utils/ChannelParser.h"
#include "Utils/Channels.h"
#include "WebSocketApiImpl.h"
#include "WebSocketConnection.h"
#include "WebSocketRequest.h"

namespace HuobiSwap {

WebSocketRequest *WebSocketApiImpl::subscribeTradeDetailEvent(
    const std::list<std::string> &symbols,
    const std::function<void(const TradeDetailEvent &)> &callback,
    const std::function<void(Error &)> &errorHandler) {

  InputChecker::checker()->checkCallback(callback);
  auto req = new WebSocketRequestImpl<TradeDetailEvent>();
  req->connectionHandler = [symbols](WebSocketConnection *connection) {
    for (std::string symbol : symbols) {
      connection->send(Channels::tradeChannel(symbol));
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
  };
  req->JsonParser = [](const JsonWrapper &json) {
    ChannelParser parser = ChannelParser(json.getString("ch"));
    TradeDetailEvent event;
    event.symbol = parser.getSymbol();
    event.timestamp = json.getLongLong("ts");
    JsonWrapper tick = json.getJsonObjectOrArray("tick");
    JsonWrapper datas = tick.getJsonObjectOrArray("data");
    size_t size = datas.size();
    std::vector<TradeDetail> tds;
    for (int i = 0; i < size; i++) {
      JsonWrapper item = datas.getJsonObjectAt(i);
      TradeDetail data;
      data.timestamp = item.getLongLong("ts");
      data.id = item.getLong("tradeId");
      data.volume = item.getDecimal("amount");
      data.price = item.getDecimal("price");
      data.direction = item.getString("direction");
      event.datas.push_back(data);
    }
    return event;
  };
  req->isNeedSignature = false;
  req->Callback = callback;
  req->errorHandler = errorHandler;
  return req;
}

WebSocketRequest *WebSocketApiImpl::subscribeCandlestickEvent(
    const std::list<std::string> &symbols, CandlestickInterval interval,
    const std::function<void(const CandlestickEvent &)> &callback,
    const std::function<void(Error &)> &errorHandler) {

  InputChecker::checker()
      ->shouldNotNull(+interval._to_string()/*interval.getValue()*/, "interval")
      ->checkCallback(callback);
  auto req = new WebSocketRequestImpl<CandlestickEvent>();
  req->connectionHandler = [symbols,
                            interval](WebSocketConnection *connection) {
    for (std::string symbol : symbols) {
      connection->send(Channels::klineChannel(symbol, interval));
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
  };
  req->JsonParser = [interval](const JsonWrapper &json) {
    ChannelParser parser = ChannelParser(json.getString("ch"));
    CandlestickEvent event;
    event.symbol = parser.getSymbol();
    event.interval = interval;
    event.timestamp = json.getLongLong("ts");
    JsonWrapper tick = json.getJsonObjectOrArray("tick");
    Candlestick data;
    data.timestamp = tick.getLong("id");
    data.id = tick.getLong("id");
    data.amount = tick.getDecimal("amount");
    data.close = tick.getDecimal("close");
    data.high = tick.getDecimal("high");
    data.low = tick.getDecimal("low");
    data.open = tick.getDecimal("open");
    data.volume = tick.getDecimal("vol");
    data.count = tick.getLong("count");
    event.data = data;
    return event;
  };
  req->isNeedSignature = false;
  req->Callback = callback;
  req->errorHandler = errorHandler;
  return req;
}

WebSocketRequest *WebSocketApiImpl::subscribeOrderUpdateEvent(
    const std::list<std::string> &symbols,
    const std::function<void(const OrderUpdateEvent &)> &callback,
    const std::function<void(Error &)> &errorHandler) {
  InputChecker::checker()->checkCallback(callback);

  auto req = new WebSocketRequestImpl<OrderUpdateEvent>();

  req->connectionHandler = [symbols](WebSocketConnection *connection) {
    for (std::string symbol : symbols) {
      connection->send(Channels::orderChannel(symbol));
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
  };

  req->JsonParser = [this](const JsonWrapper &json) {
    Log::WriteLog("[OrderUpdate...]");
    ChannelParser parser = ChannelParser(json.getString("topic"));
    OrderUpdateEvent orderUpdateEvent;
    orderUpdateEvent.symbol = parser.getSymbol();
    orderUpdateEvent.timestamp = json.getLongLong("ts");
    Order order;
    order.symbol = parser.getSymbol();
    order.volume = json.getDecimal("volume");
    order.price = json.getDecimal("price");
    // SegementFault if below code is removed.
    order.direction = OrderSide::buy;
    auto direction = OrderSide::_from_string_nocase_nothrow(json.getString("direction"));//OrderSide::lookup(json.getString("direction"));
    if (direction) {
      order.direction = direction.value();
    } else {
      std::cout << "parser error: " << json.getString("direction") << std::endl;
    }
    auto offset = TradeOffset::_from_string_nothrow(json.getString("offset"));
    if (offset)
      order.offset = offset.value();
    auto status = OrderStatus::_from_index_nothrow(json.getInt("status"));//_from_string_nocase_nothrow(json.getString("status"));
    if (status) order.status = status.value();
    auto order_price_type = OrderPriceType::_from_string_nothrow(json.getString("order_price_type"));
    if (order_price_type) order.order_price_type = order_price_type.value();
    order.order_id = json.getLongLong("order_id");
    order.client_order_id = json.getLongOrDefault("client_order_id", 0);
    auto source = OrderSource::_from_string_nocase_nothrow(json.getString("order_source"));
    if (source) order.source = source.value();
    auto order_type = OrderType::_from_index_nothrow(json.getInt("order_type"));//_from_string_nothrow(json.getString("order_type"));
    if (order.order_type) order.order_type = order_type.value();
    order.created_at = json.getLongLong("created_at");
    order.trade_volume = json.getDecimal("trade_volume");
    order.margin_frozen = json.getDecimal("margin_frozen");
    order.profit = json.getDecimal("profit");
    order.fee = json.getDecimalOrDefault("fee", 0);
    order.trade_turnover = json.getDecimalOrDefault("trade_turnover", 0);
    order.trade_avg_price = json.getDecimal("trade_avg_price");
    auto liquidation_type =
        LiquidationType::_from_index_nothrow(std::atoi(json.getString("liquidation_type")));
    if (liquidation_type)
      order.liquidation_type = liquidation_type.value();

    JsonWrapper trades = json.getJsonObjectOrArray("trade");
    size_t size = trades.size();
    for (int i = 0; i < size; i++) {
      JsonWrapper item = trades.getJsonObjectAt(i);
      Trade trade;
      trade.id = item.getString("id");
      trade.trade_volume = item.getDecimal("trade_volume");
      trade.trade_price = item.getDecimal("trade_price");
      trade.trade_fee = item.getDecimal("trade_fee");
      trade.trade_turnover = item.getDecimal("trade_turnover");
      trade.created_at = item.getLongLong("created_at");
      auto role = DealRole::_from_string_nocase_nothrow(item.getString("role"));
      if (role) trade.role = role.value();
      order.trades.push_back(trade);
    }
    orderUpdateEvent.data = order;
    // Log::WriteLog("clientorderid: %s \n", orderUpdateEvent.data.order_id);

    orderUpdateEvent.client_order_id =
        json.getLongOrDefault("client_order_id", 0);
    orderUpdateEvent.unfilled_volume =
        json.getDecimal("volume") - json.getDecimal("trade_volume");

    return orderUpdateEvent;
  };
  req->isNeedSignature = true;
  req->Callback = callback;
  req->errorHandler = errorHandler;
  return req;
}

WebSocketRequest *WebSocketApiImpl::requestCandlestickEvent(
    bool autoClose, const std::list<std::string> &symbols,
    CandlestickInterval interval, long startTime, long endTime,
    const std::function<void(const std::vector<CandlestickEvent> &)> &callback,
    const std::function<void(Error &)> &errorHandler) {

  InputChecker::checker()
      ->shouldNotNull(+interval._to_string()/*interval.getValue()*/, "interval")
      ->checkCallback(callback);
  auto req = new WebSocketRequestImpl<std::vector<CandlestickEvent>>();
  req->autoClose = autoClose;
  req->time = symbols.size();
  req->connectionHandler = [symbols, interval, startTime,
                            endTime](WebSocketConnection *connection) {
    for (std::string symbol : symbols) {
      connection->send(Channels::klineChannel(Channels::OP_REQ, symbol,
                                              interval, startTime, endTime));
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
  };
  req->JsonParser = [interval](const JsonWrapper &json) {
    std::vector<CandlestickEvent> cans;
    JsonWrapper data = json.getJsonObjectOrArray("data");
    for (int i = 0; i < data.size(); i++) {
      JsonWrapper item = data.getJsonObjectAt(i);
      CandlestickEvent candlestickEvent;
      ChannelParser parser = ChannelParser(json.getString("rep"));
      candlestickEvent.symbol = parser.getSymbol();
      candlestickEvent.interval = interval;
      Candlestick can;
      can.timestamp = item.getLong("id");
      can.id = item.getLong("id");
      can.amount = item.getDecimal("amount");
      can.close = item.getDecimal("close");
      can.high = item.getDecimal("high");
      can.low = item.getDecimal("low");
      can.open = item.getDecimal("open");
      can.volume = item.getDecimal("vol");
      can.count = item.getLong("count");
      candlestickEvent.data = can;
      candlestickEvent.timestamp = can.timestamp;
      cans.push_back(candlestickEvent);
    }
    return cans;
  };
  req->isNeedSignature = false;
  req->Callback = callback;
  req->errorHandler = errorHandler;
  return req;
}

} // namespace HuobiSwap
