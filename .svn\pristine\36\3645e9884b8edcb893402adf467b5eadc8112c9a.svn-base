﻿#pragma once
#include <DataDef.h>
#include <order.hpp>

class TradingBroker
{
public:
	virtual bool init(std::vector<BrokerAccountData>* bads) = 0;
	virtual void release() = 0;

	virtual bool logon() = 0;
	virtual void logout() = 0;
	virtual bool is_logon() = 0;

	virtual BrokerType GetBrokerType() = 0;

	//
	// 主要交易接口
	//
	virtual bool query_broker_data(const std::string& id, const std::string& label, int datatype) = 0;

	virtual bool submit_order(const Order& order) = 0;
	virtual bool cancel_order(const Order& order) = 0;

	virtual int get_filling_order_size(const std::string& account_id = "") = 0;
	virtual bool is_active(const std::string& label) = 0;
	virtual bool is_filling_order(const std::string& label) = 0;
	virtual time_t get_last_trade_time(const std::string& account_id, const std::string& label, POSITION_EFFECT effect) = 0;
	virtual int load_last_trade(const std::string& account_id, BlockDataPtr blk_ptr) = 0;

	virtual bool create_order(const Order& order) = 0;
	virtual bool update_order(Order* order) = 0;
	virtual bool delete_open_order(const std::string& order_id, bool force = false) = 0;
	virtual void apply_remote_order(const std::string& portfolio_id, Order& order) = 0;
	virtual double get_commission(const Trade& trade) const = 0;
	virtual double get_tax(const Trade& trade) const = 0;
};

typedef std::shared_ptr<TradingBroker> TradingBrokerPtr;