#include "WebSocketWatchDog.h"

#include <list>
#include <chrono>
#include <thread>
#include "WebSocketConnection.h"

namespace HuobiSwap {

//class AutoLock {
//public:
//  AutoLock(std::mutex &mutex) : mutex_(mutex) { mutex_.lock(); }
//
//  ~AutoLock() { mutex_.unlock(); }
//
//private:
//  std::mutex &mutex_;
//};

void WebSocketWatchDog::WatchDogThread() {
  while (runningFlag) {
    //AutoLock lock(mutex);
    {
      std::lock_guard<std::mutex> lock(mutex);
      for (auto connection : connectionList) {
        if (connection == nullptr)
          continue;
        LineStatus lineStatus = connection->getLineStatus();
        if (lineStatus == LineStatus::LINE_ACTIVE) {
          // Check response
          if (op.isAutoReconnect) {
            if (connection->getConnectState() == ConnectionStatus::CONNECTED) {
              lwsl_user("time....\n");
              time_t ts = TimeService::getCurrentTimeStamp() - connection->getLastReceivedTime();
              if (ts > op.receiveLimitMs) {
                Log::WriteLog(" No response from server");
                std::cout << connection << ": receive timeout " << ts << "ms, try reconnect..." << std::endl;
                lwsl_user("auto recon\n");
                connection->disconnect();
                connection->reConnect(/*op.connectionDelayOnFailure*/);
              }
            } else if (connection->getConnectState() == ConnectionStatus::CLOSED) {
              std::cout << "check close, try reconnect..." << std::endl;
              lwsl_user("check close, try reconnect...\n");
              connection->reConnect(op.connectionDelayOnFailure);
            } else {
              std::cout << "unknown connect state..." << std::endl;
              lwsl_user("unknown...\n");
            }
          }
        } else if (lineStatus == LineStatus::LINE_DELAY) {
          //std::cout << "connection delay..." << std::endl;
          lwsl_user("delay....\n");
          connection->reConnect();
        } else {
          std::cout << "connection unknown..." << std::endl;
          lwsl_user("else...\n");
        }
      }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  }
}

void WebSocketWatchDog::onConnectionCreated(WebSocketConnection* connection) {
  for (auto conit : connectionList) {
    if (conit == connection)
      return;
  }
  {
    std::lock_guard<std::mutex> lock(mutex);
    connectionList.push_back(connection);
  }
}

void WebSocketWatchDog::onClosedNormally(WebSocketConnection* connection) {
  std::lock_guard<std::mutex> lock(mutex);
  connectionList.remove(connection);
}

WebSocketWatchDog::WebSocketWatchDog(SubscriptionOptions &op)
    : runningFlag(true) {
  this->op = op;
  dogthread = std::thread(&WebSocketWatchDog::WatchDogThread, this);
}

}


