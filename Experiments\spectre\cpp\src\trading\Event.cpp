/**
 * @file Event.cpp
 * @brief Implementation of Event system classes
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Event.h"
#include "trading/Calendar.h"
#include <algorithm>
#include <thread>
#include <chrono>

namespace Spectre {
namespace Trading {

// CalendarEvent implementation
CalendarEvent::CalendarEvent(const std::string& calendar_event_name, Callback callback, 
                            int64_t offset_ns)
    : Event(std::move(callback)), event_name_(calendar_event_name), offset_(offset_ns) {
}

void CalendarEvent::on_schedule(EventManager* evt_mgr) {
    // Try to get calendar from MarketEventManager
    auto* market_mgr = dynamic_cast<MarketEventManager*>(evt_mgr);
    if (market_mgr) {
        calendar_ = market_mgr->calendar();
        calculate_range();
    }
}

bool CalendarEvent::should_trigger() {
    if (!calendar_) {
        return false;
    }
    
    auto now = std::chrono::system_clock::now();
    if (now >= trigger_time_) {
        // Pop passed events and recalculate
        // This is a simplified implementation
        // In practice, you'd need to implement calendar event popping
        calculate_range();
        return true;
    }
    return false;
}

void CalendarEvent::calculate_range() {
    if (calendar_) {
        // This is a simplified implementation
        // In practice, you'd get the next event time from calendar
        // and add the offset
        trigger_time_ = std::chrono::system_clock::now() + std::chrono::hours(24);
    }
}

// EventReceiver implementation
void EventReceiver::unsubscribe() {
    if (event_manager_) {
        event_manager_->unsubscribe(this);
    }
}

void EventReceiver::schedule(std::shared_ptr<Event> evt) {
    if (event_manager_) {
        event_manager_->schedule(this, std::move(evt));
    }
}

void EventReceiver::stop_event_manager() {
    if (event_manager_) {
        event_manager_->stop();
    }
}

template<typename EventType>
void EventReceiver::fire_event() {
    if (event_manager_) {
        event_manager_->fire_event<EventType>(this);
    }
}

// Explicit template instantiations for common event types
template void EventReceiver::fire_event<EveryBarData>();
template void EventReceiver::fire_event<Always>();
template void EventReceiver::fire_event<MarketOpen>();
template void EventReceiver::fire_event<MarketClose>();

// EventManager implementation
void EventManager::subscribe(std::shared_ptr<EventReceiver> receiver) {
    if (subscribers_.find(receiver.get()) != subscribers_.end()) {
        throw std::runtime_error("Duplicate subscribe");
    }
    
    subscribers_[receiver.get()] = receiver;
    events_[receiver.get()] = EventList{};
    receiver->event_manager_ = this;
}

void EventManager::unsubscribe(EventReceiver* receiver) {
    auto it = subscribers_.find(receiver);
    if (it == subscribers_.end()) {
        throw std::runtime_error("Subscriber not exists");
    }
    
    subscribers_.erase(it);
    events_.erase(receiver);
    receiver->event_manager_ = nullptr;
}

void EventManager::schedule(EventReceiver* receiver, std::shared_ptr<Event> event) {
    auto it = events_.find(receiver);
    if (it != events_.end()) {
        it->second.push_back(event);
        event->on_schedule(this);
    }
}

template<typename EventType>
void EventManager::fire_event(EventReceiver* source) {
    for (auto& [receiver, events] : events_) {
        for (auto& evt : events) {
            // Check if event is of the requested type
            if (dynamic_cast<EventType*>(evt.get())) {
                evt->execute(source);
            }
        }
    }
}

// Explicit template instantiations
template void EventManager::fire_event<EveryBarData>(EventReceiver*);
template void EventManager::fire_event<Always>(EventReceiver*);
template void EventManager::fire_event<MarketOpen>(EventReceiver*);
template void EventManager::fire_event<MarketClose>(EventReceiver*);

void EventManager::process_events() {
    // Call on_run for all subscribers
    for (auto& [receiver_ptr, receiver_shared] : subscribers_) {
        receiver_shared->on_run();
    }
    
    // Main event loop
    while (!stop_) {
        check_triggers();
        
        // Small delay to prevent busy waiting
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    // Call on_end_of_run for all subscribers
    for (auto& [receiver_ptr, receiver_shared] : subscribers_) {
        receiver_shared->on_end_of_run();
    }
}

void EventManager::check_triggers() {
    for (auto& [receiver, events] : events_) {
        for (auto& evt : events) {
            if (evt->should_trigger()) {
                evt->execute(receiver);
            }
        }
    }
}

// MarketEventManager implementation
MarketEventManager::MarketEventManager(std::shared_ptr<Calendar> calendar)
    : calendar_(std::move(calendar)) {
}

void MarketEventManager::run() {
    if (!calendar_) {
        throw std::runtime_error("Calendar is required for MarketEventManager");
    }
    
    process_events();
}

} // namespace Trading
} // namespace Spectre
