#include "WsRequestClientImpl.h"
namespace HuobiSwap {
void WsRequestClientImpl::setProxy(const std::string &host) {
  if (impl && !host.empty()) {
    impl->setProxy(host);
  }
}

void WsRequestClientImpl::requestCandlestickEvent(
    const char *symbols, CandlestickInterval interval,
    const std::function<void(const std::vector<CandlestickEvent> &)> &callback,
    const std::function<void(Error &)> &errorHandler) {
  return requestCandlestickEvent(true, symbols, interval, 0, 0, callback,
                          errorHandler);
}

void WsRequestClientImpl::requestCandlestickEvent(
    bool autoClose, const char *symbols, CandlestickInterval interval,
    const std::function<void(const std::vector<CandlestickEvent> &)> &callback,
    const std::function<void(Error &)> &errorHandler) {
  return requestCandlestickEvent(autoClose, symbols, interval, 0, 0, callback,
                          errorHandler);
}

void WsRequestClientImpl::requestCandlestickEvent(
    const char *symbols, CandlestickInterval interval, long startTime,
    long endTime,
    const std::function<void(const std::vector<CandlestickEvent> &)> &callback,
    const std::function<void(Error &)> &errorHandler) {
  return requestCandlestickEvent(true, symbols, interval, startTime, endTime, callback,
                          errorHandler);
}

void WsRequestClientImpl::requestCandlestickEvent(
    bool autoClose, const char *symbols, CandlestickInterval interval,
    long startTime, long endTime,
    const std::function<void(const std::vector<CandlestickEvent> &)> &callback,
    const std::function<void(Error &)> &errorHandler) {
  req = impl->requestCandlestickEvent(
      autoClose, symbols, interval, startTime, endTime, callback, errorHandler);
}

void WsRequestClientImpl::startReq() {
  if (req) {
    impl->startReq(req);
  }
};

} // namespace HuobiSwap