﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.26403.7
MinimumVisualStudioVersion = 10.0.40219.1
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DataHub", "DataHub\DataHub.vcxproj", "{380CB2F2-8A5D-4652-877B-3D4DCA97717E}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Utils", "Utils\Utils.vcxproj", "{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "include", "include", "{D09F7212-C747-459F-95F2-BFBB286FA592}"
	ProjectSection(SolutionItems) = preProject
		include\BarSeries.h = include\BarSeries.h
		include\BusinessDataDef.h = include\BusinessDataDef.h
		include\ConstDef.h = include\ConstDef.h
		include\CtpApi.h = include\CtpApi.h
		include\DataDef.h = include\DataDef.h
		include\DataHub.h = include\DataHub.h
		include\DateTime.hpp = include\DateTime.hpp
		include\Indicator.h = include\Indicator.h
		include\Instrument.h = include\Instrument.h
		include\json.hpp = include\json.hpp
		include\Performance.h = include\Performance.h
		include\slog.hpp = include\slog.hpp
		include\TradeSystem.h = include\TradeSystem.h
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "TradingSvr", "TradingSvr\TradingSvr.vcxproj", "{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}"
	ProjectSection(ProjectDependencies) = postProject
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E} = {380CB2F2-8A5D-4652-877B-3D4DCA97717E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test", "test\test.vcxproj", "{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}"
	ProjectSection(ProjectDependencies) = postProject
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970} = {A23A9337-4BCD-4E83-8B6A-5B17BB09E970}
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E} = {380CB2F2-8A5D-4652-877B-3D4DCA97717E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboQuant", "RoboQuant\RoboQuant.vcxproj", "{09306A13-5EA6-4584-8A37-59906F93C12E}"
	ProjectSection(ProjectDependencies) = postProject
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970} = {A23A9337-4BCD-4E83-8B6A-5B17BB09E970}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "IBTrader", "IBTrader\IBTrader.vcxproj", "{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Broker", "Broker\Broker.vcxproj", "{62DC50CE-E620-485E-8856-09E8C73D0A0C}"
	ProjectSection(ProjectDependencies) = postProject
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A} = {6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}
		{A65018D6-331D-48EE-BC29-4E43E1AF0043} = {A65018D6-331D-48EE-BC29-4E43E1AF0043}
		{1416E3E3-4325-4FBE-9D38-3D07E0066715} = {1416E3E3-4325-4FBE-9D38-3D07E0066715}
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E} = {BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CTPTrader", "CTPTrader\CTPTrader.vcxproj", "{A65018D6-331D-48EE-BC29-4E43E1AF0043}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "FIXTrader", "FIXTrader\FIXTrader.vcxproj", "{1416E3E3-4325-4FBE-9D38-3D07E0066715}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Win32 = Debug|Win32
		Debug|x64 = Debug|x64
		Release|Win32 = Release|Win32
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Debug|Win32.ActiveCfg = Debug|Win32
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Debug|Win32.Build.0 = Debug|Win32
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Debug|x64.ActiveCfg = Debug|x64
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Debug|x64.Build.0 = Debug|x64
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Release|Win32.ActiveCfg = Release|Win32
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Release|Win32.Build.0 = Release|Win32
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Release|x64.ActiveCfg = Release|x64
		{380CB2F2-8A5D-4652-877B-3D4DCA97717E}.Release|x64.Build.0 = Release|x64
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Debug|Win32.ActiveCfg = Debug|Win32
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Debug|Win32.Build.0 = Debug|Win32
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Debug|x64.ActiveCfg = Debug|x64
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Debug|x64.Build.0 = Debug|x64
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Release|Win32.ActiveCfg = Release|Win32
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Release|Win32.Build.0 = Release|Win32
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Release|x64.ActiveCfg = Release|x64
		{BE23C0FB-F3C2-4F74-8AF5-6C3B3DDA3F1E}.Release|x64.Build.0 = Release|x64
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Debug|Win32.ActiveCfg = Debug|Win32
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Debug|Win32.Build.0 = Debug|Win32
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Debug|x64.ActiveCfg = Debug|x64
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Debug|x64.Build.0 = Debug|x64
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Release|Win32.ActiveCfg = Release|Win32
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Release|Win32.Build.0 = Release|Win32
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Release|x64.ActiveCfg = Release|x64
		{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}.Release|x64.Build.0 = Release|x64
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Debug|Win32.ActiveCfg = Debug|Win32
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Debug|Win32.Build.0 = Debug|Win32
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Debug|x64.ActiveCfg = Debug|x64
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Debug|x64.Build.0 = Debug|x64
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Release|Win32.ActiveCfg = Release|Win32
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Release|Win32.Build.0 = Release|Win32
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Release|x64.ActiveCfg = Release|x64
		{64B8EF5F-F3D4-43A4-A7A2-025D88C77CA9}.Release|x64.Build.0 = Release|x64
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Debug|Win32.ActiveCfg = Debug|Win32
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Debug|Win32.Build.0 = Debug|Win32
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Debug|x64.ActiveCfg = Debug|x64
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Debug|x64.Build.0 = Debug|x64
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Release|Win32.ActiveCfg = Release|Win32
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Release|Win32.Build.0 = Release|Win32
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Release|x64.ActiveCfg = Release|x64
		{09306A13-5EA6-4584-8A37-59906F93C12E}.Release|x64.Build.0 = Release|x64
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Debug|Win32.ActiveCfg = Debug|Win32
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Debug|Win32.Build.0 = Debug|Win32
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Debug|x64.ActiveCfg = Debug|x64
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Debug|x64.Build.0 = Debug|x64
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Release|Win32.ActiveCfg = Release|Win32
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Release|Win32.Build.0 = Release|Win32
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Release|x64.ActiveCfg = Release|x64
		{6F4CFD6F-F7D6-48F7-B8D2-F427E50AF28A}.Release|x64.Build.0 = Release|x64
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Debug|Win32.ActiveCfg = Debug|Win32
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Debug|Win32.Build.0 = Debug|Win32
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Debug|x64.ActiveCfg = Debug|x64
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Debug|x64.Build.0 = Debug|x64
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Release|Win32.ActiveCfg = Release|Win32
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Release|Win32.Build.0 = Release|Win32
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Release|x64.ActiveCfg = Release|x64
		{62DC50CE-E620-485E-8856-09E8C73D0A0C}.Release|x64.Build.0 = Release|x64
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Debug|Win32.ActiveCfg = Debug|Win32
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Debug|Win32.Build.0 = Debug|Win32
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Debug|x64.ActiveCfg = Debug|x64
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Debug|x64.Build.0 = Debug|x64
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Release|Win32.ActiveCfg = Release|Win32
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Release|Win32.Build.0 = Release|Win32
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Release|x64.ActiveCfg = Release|x64
		{A65018D6-331D-48EE-BC29-4E43E1AF0043}.Release|x64.Build.0 = Release|x64
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Debug|Win32.ActiveCfg = Debug|Win32
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Debug|Win32.Build.0 = Debug|Win32
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Debug|x64.ActiveCfg = Debug|x64
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Debug|x64.Build.0 = Debug|x64
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Release|Win32.ActiveCfg = Release|Win32
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Release|Win32.Build.0 = Release|Win32
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Release|x64.ActiveCfg = Release|x64
		{1416E3E3-4325-4FBE-9D38-3D07E0066715}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		VisualSVNWorkingCopyRoot = .
	EndGlobalSection
EndGlobal
