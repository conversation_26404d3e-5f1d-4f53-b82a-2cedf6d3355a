#include <iostream>
#include <torch/torch.h>
#include "Factor.h"
#include "BasicFactors.h"
#include "StatisticalFactors.h"
#include "TechnicalFactors.h"
#include "FilterFactors.h"
#include "FeatureFactors.h"
#include "LabelFactors.h"
#include "DataFactors.h"
#include "Performance.h"
#include "Utils.h"
#include "Rolling.h"
#include "Engine.h"
#include "Parallel.h"
#include "RollingParallel.h"

int main() {
    std::cout << "Hello from spectre_cpp!" << std::endl;

    // Simple LibTorch test
    torch::Tensor tensor = torch::rand({2, 3});
    std::cout << "Random tensor:\n" << tensor << std::endl;

    // Test basic utility functions
    std::cout << "\n--- Testing Utility Functions ---\n";
    torch::Tensor test_data = torch::tensor({{1.0, 2.0, std::numeric_limits<float>::quiet_NaN(), 4.0},
                                            {5.0, std::numeric_limits<float>::quiet_NaN(), 7.0, 8.0}});
    std::cout << "Test data with NaN:\n" << test_data << std::endl;
    std::cout << "nanmean (dim=1):\n" << Spectre::nanmean(test_data, 1, false) << std::endl;
    std::cout << "nansum (dim=1):\n" << Spectre::nansum(test_data, 1, false) << std::endl;
    std::cout << "nanstd (dim=1):\n" << Spectre::nanstd(test_data, 1, 0) << std::endl;

    // Test Rolling class
    std::cout << "\n--- Testing Rolling Class ---\n";
    torch::Tensor rolling_data = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0},
                                               {10.0, 20.0, 30.0, 40.0, 50.0}});
    std::cout << "Rolling data:\n" << rolling_data << std::endl;

    Spectre::Rolling rolling(rolling_data, 3);
    std::cout << "Rolling nanmean (window=3):\n" << rolling.nanmean() << std::endl;
    std::cout << "Rolling nansum (window=3):\n" << rolling.nansum() << std::endl;
    std::cout << "Rolling first:\n" << rolling.first() << std::endl;
    std::cout << "Rolling last:\n" << rolling.last() << std::endl;

    // Test new Parallel module
    std::cout << "\n--- Testing New Parallel Module ---\n";

    // Test DeviceConstant
    std::cout << "Testing DeviceConstant...\n";
    auto device_const = Spectre::DeviceConstant::get(torch::kCPU);
    auto linspace_vals = device_const->linspace(5, torch::kFloat);
    std::cout << "DeviceConstant linspace(5): " << linspace_vals << std::endl;

    // Test new parallel statistical functions
    torch::Tensor parallel_test_data = torch::tensor({{1.0, 2.0, std::numeric_limits<float>::quiet_NaN(), 4.0},
                                                      {5.0, std::numeric_limits<float>::quiet_NaN(), 7.0, 8.0}});
    std::cout << "Parallel test data:\n" << parallel_test_data << std::endl;

    auto parallel_nanmean = Spectre::nanmean(parallel_test_data, 1);
    auto parallel_nanstd = Spectre::nanstd(parallel_test_data, 1, 0);
    std::cout << "Parallel nanmean: " << parallel_nanmean << std::endl;
    std::cout << "Parallel nanstd: " << parallel_nanstd << std::endl;

    // Test ParallelGroupBy
    std::cout << "Testing ParallelGroupBy...\n";
    torch::Tensor group_keys = torch::tensor({0, 1, 0, 1, 0});
    torch::Tensor group_data = torch::randn({5, 3});
    std::cout << "Group keys: " << group_keys << std::endl;
    std::cout << "Group data:\n" << group_data << std::endl;

    Spectre::ParallelGroupBy groupby(group_keys);
    auto split_data = groupby.split(group_data);
    auto reverted_data = groupby.revert(split_data);
    std::cout << "Split data:\n" << split_data << std::endl;
    std::cout << "Reverted data:\n" << reverted_data << std::endl;

    // Test new RollingParallel
    std::cout << "Testing RollingParallel...\n";
    torch::Tensor rolling_parallel_data = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0, 6.0},
                                                         {10.0, 20.0, 30.0, 40.0, 50.0, 60.0}});
    Spectre::RollingParallel rolling_parallel(rolling_parallel_data, 3);
    auto rolling_parallel_mean = rolling_parallel.nanmean();
    auto rolling_parallel_std = rolling_parallel.nanstd();
    std::cout << "RollingParallel nanmean:\n" << rolling_parallel_mean << std::endl;
    std::cout << "RollingParallel nanstd:\n" << rolling_parallel_std << std::endl;

    // Test AbsFactor
    std::cout << "\n--- Testing AbsFactor ---\n";
    torch::Tensor test_data_abs = torch::tensor({{-1.0, 2.0, -3.0}, {4.0, -5.0, 6.0}});
    Spectre::AbsFactor abs_factor(nullptr);
    torch::Tensor result_abs = abs_factor.compute({test_data_abs});
    std::cout << "Original: \n" << test_data_abs << std::endl;
    std::cout << "Abs: \n" << result_abs << std::endl;

    // Test LogFactor
    std::cout << "\n--- Testing LogFactor ---\n";
    torch::Tensor test_data_log = torch::tensor({{1.0, 2.0, 3.0}, {4.0, 5.0, 6.0}});
    Spectre::LogFactor log_factor(nullptr);
    torch::Tensor result_log = log_factor.compute({test_data_log});
    std::cout << "Original: \n" << test_data_log << std::endl;
    std::cout << "Log: \n" << result_log << std::endl;

    // Test ShiftFactor
    std::cout << "\n--- Testing ShiftFactor ---\n";
    torch::Tensor test_data_shift = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0}, {6.0, 7.0, 8.0, 9.0, 10.0}});
    Spectre::ShiftFactor shift_factor_pos(nullptr, 2);
    torch::Tensor result_shift_pos = shift_factor_pos.compute({test_data_shift});
    std::cout << "Original: \n" << test_data_shift << std::endl;
    std::cout << "Shift (periods=2): \n" << result_shift_pos << std::endl;

    Spectre::ShiftFactor shift_factor_neg(nullptr, -1);
    torch::Tensor result_shift_neg = shift_factor_neg.compute({test_data_shift});
    std::cout << "Shift (periods=-1): \n" << result_shift_neg << std::endl;

    // Test SimpleMovingAverage
    std::cout << "\n--- Testing SimpleMovingAverage ---\n";
    torch::Tensor test_data_sma = torch::tensor({{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0},
                                                {10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0}});
    // Add NaN values for testing
    test_data_sma[0][2] = std::numeric_limits<float>::quiet_NaN();
    test_data_sma[1][4] = std::numeric_limits<float>::quiet_NaN();

    Spectre::SimpleMovingAverage sma_factor(nullptr, 3);
    torch::Tensor result_sma = sma_factor.compute({test_data_sma});
    std::cout << "Original: \n" << test_data_sma << std::endl;
    std::cout << "SMA (win=3): \n" << result_sma << std::endl;

    // Test Returns factor
    std::cout << "\n--- Testing Returns Factor ---\n";
    torch::Tensor price_data = torch::tensor({{100.0, 102.0, 101.0, 103.0, 105.0},
                                             {50.0, 51.0, 49.0, 52.0, 53.0}});
    Spectre::Returns returns_factor(nullptr);
    torch::Tensor result_returns = returns_factor.compute({price_data});
    std::cout << "Price data: \n" << price_data << std::endl;
    std::cout << "Returns: \n" << result_returns << std::endl;

    // Test new statistical factors
    std::cout << "\n--- Testing Statistical Factors ---\n";

    // Test StandardDeviation
    torch::Tensor test_data_3d = torch::randn({2, 5, 10});  // 2 assets, 5 time periods, 10 window
    Spectre::StandardDeviation std_factor(10);
    torch::Tensor result_std = std_factor.compute({test_data_3d});
    std::cout << "StandardDeviation result shape: " << result_std.sizes() << std::endl;

    // Test RollingHigh
    Spectre::RollingHigh high_factor(5);
    torch::Tensor test_data_high = torch::randn({2, 5, 5});
    torch::Tensor result_high = high_factor.compute({test_data_high});
    std::cout << "RollingHigh result shape: " << result_high.sizes() << std::endl;

    // Test new basic factors
    std::cout << "\n--- Testing Extended Basic Factors ---\n";

    // Test ElementWiseMax
    torch::Tensor a = torch::tensor({{1.0, 3.0, 2.0}, {4.0, 1.0, 6.0}});
    torch::Tensor b = torch::tensor({{2.0, 1.0, 4.0}, {3.0, 5.0, 2.0}});
    Spectre::ElementWiseMax max_factor({});
    torch::Tensor result_max = max_factor.compute({a, b});
    std::cout << "ElementWiseMax result: \n" << result_max << std::endl;

    // Test ConstantsFactor
    Spectre::ConstantsFactor const_factor(42.0f, nullptr);
    torch::Tensor result_const = const_factor.compute({a});
    std::cout << "ConstantsFactor result: \n" << result_const << std::endl;

    // Test feature engineering factors
    std::cout << "\n--- Testing Feature Engineering Factors ---\n";

    // Test RankFactor
    torch::Tensor rank_data = torch::tensor({{1.0, 3.0, 2.0}, {4.0, 1.0, 6.0}, {2.0, 5.0, 3.0}});
    Spectre::RankFactor rank_factor({});
    torch::Tensor result_rank = rank_factor.compute({rank_data});
    std::cout << "RankFactor result: \n" << result_rank << std::endl;

    // Test ZScoreFactor
    Spectre::ZScoreFactor zscore_factor({});
    torch::Tensor result_zscore = zscore_factor.compute({rank_data});
    std::cout << "ZScoreFactor result: \n" << result_zscore << std::endl;

    // Test label factors
    std::cout << "\n--- Testing Label Factors ---\n";

    // Test BinaryClassificationLabel
    torch::Tensor label_data = torch::tensor({{-0.5, 0.2, 1.5}, {0.8, -0.3, 0.1}});
    Spectre::BinaryClassificationLabel binary_label(nullptr, 0.0f);
    torch::Tensor result_binary = binary_label.compute({label_data});
    std::cout << "BinaryClassificationLabel result: \n" << result_binary << std::endl;

    // Test data quality factors
    std::cout << "\n--- Testing Data Quality Factors ---\n";

    // Test InterpolationFactor
    torch::Tensor data_with_nan = torch::tensor({{1.0, std::numeric_limits<float>::quiet_NaN(), 3.0},
                                                 {4.0, 5.0, std::numeric_limits<float>::quiet_NaN()}});
    Spectre::InterpolationFactor interp_factor(nullptr, Spectre::InterpolationFactor::Method::FORWARD_FILL);
    torch::Tensor result_interp = interp_factor.compute({data_with_nan});
    std::cout << "InterpolationFactor result: \n" << result_interp << std::endl;

    // Test performance monitoring
    std::cout << "\n--- Testing Performance Monitoring ---\n";

    {
        Spectre::ProfilerScope prof_scope("test_computation");

        // Simulate some computation
        torch::Tensor large_tensor = torch::randn({1000, 100});
        torch::Tensor result = torch::matmul(large_tensor, large_tensor.transpose(0, 1));

        std::cout << "Computed matrix multiplication of size: " << large_tensor.sizes() << std::endl;
    }

    // Print profiling results
    Spectre::ProfilerScope::print_timings();

    // Test GPU availability
    auto& gpu_manager = Spectre::GPUManager::instance();
    std::cout << "GPU available: " << (gpu_manager.is_available() ? "Yes" : "No") << std::endl;
    if (gpu_manager.is_available()) {
        std::cout << "GPU device count: " << gpu_manager.get_device_count() << std::endl;
    }

    std::cout << "\nAll comprehensive tests completed successfully!" << std::endl;
    std::cout << "? Spectre C++ Factor Library is fully functional!" << std::endl;
    return 0;
}
