#pragma once

#include <torch/torch.h>
#include <functional>
#include <vector>

namespace Spectre {

// ============================================================================
// Rolling class - Corresponds to Python's Rolling
// ============================================================================

class Rolling {
public:
    // Constructor
    Rolling(const torch::Tensor& data, int window, const torch::Tensor& adjustments = torch::Tensor());
    
    // Basic aggregation functions
    torch::Tensor nanmean(int64_t dim = 2) const;
    torch::Tensor nansum(int64_t dim = 2) const;
    torch::Tensor nanstd(int64_t dim = 2, int64_t ddof = 0) const;
    torch::Tensor nanmax(int64_t dim = 2) const;
    torch::Tensor nanmin(int64_t dim = 2) const;
    torch::Tensor nanprod(int64_t dim = 2) const;
    torch::Tensor nanvar(int64_t dim = 2, int64_t ddof = 0) const;
    
    // Position-based access
    torch::Tensor first() const;
    torch::Tensor last() const;
    torch::Tensor last_nonnan(int offset = 0) const;
    torch::Tensor loc(int64_t i) const;
    
    // Generic aggregation function
    template<typename Func>
    torch::Tensor agg(Func func) const {
        std::vector<torch::Tensor> seq;
        for (const auto& split_range : m_splits) {
            torch::Tensor adjusted_data = adjust(split_range.first, split_range.second);
            torch::Tensor result = func(adjusted_data);
            seq.push_back(result.contiguous());
        }
        return torch::cat(seq, 1);
    }
    
    // Generic aggregation function with additional arguments
    template<typename Func, typename... Args>
    torch::Tensor agg(Func func, Args... args) const {
        std::vector<torch::Tensor> seq;
        for (const auto& split_range : m_splits) {
            torch::Tensor adjusted_data = adjust(split_range.first, split_range.second);
            torch::Tensor result = func(adjusted_data, args...);
            seq.push_back(result.contiguous());
        }
        return torch::cat(seq, 1);
    }
    
    // Static utility function
    static torch::Tensor unfold(const torch::Tensor& x, int window, float fill_value = std::numeric_limits<float>::quiet_NaN());

private:
    torch::Tensor m_values;
    int m_window;
    torch::Tensor m_adjustments;
    torch::Tensor m_adjustment_last;
    std::vector<std::pair<int64_t, int64_t>> m_splits;
    
    static int s_split_multiplier; // For memory management
    
    // Apply adjustments to data slice
    torch::Tensor adjust(int64_t start, int64_t end) const;
    
    // Initialize splits for memory management
    void initialize_splits();
};

} // namespace Spectre
