#include "stdafx.h"
#include "ModelHelper.h"
#include <DataHub.h>
#pragma warning(disable: 4251)
#pragma warning(disable: 4624)

namespace bll {
#pragma comment(lib, "torch.lib")
#pragma comment(lib, "torch_cpu.lib")
#pragma comment(lib, "torch_cuda.lib")
#pragma comment(lib, "pytorch_jni.lib")
#pragma comment(lib, "clog.lib")
#pragma comment(lib, "c10.lib")

	std::mutex booster_mutex;

ModelHelper::ModelHelper()
{
	_mt = ModuleType::torch;
}


ModelHelper::~ModelHelper()
{
	Release();
}

int ModelHelper::Init()
{
	return true;
}

TorchModule ModelHelper::CreateTorchModel(const std::string& model_file)
{
	torch::DeviceType device_type;
	if (torch::cuda::is_available()) {
		std::cout << "CUDA available! Predicting on GPU." << std::endl;
		device_type = torch::kCUDA;
	}
	else {
		std::cout << "Predicting on CPU." << std::endl;
		device_type = torch::kCPU;
	}
	torch::Device device(device_type);
	try	{
		//Init model
		auto module = torch::jit::load(model_file, device);
		//module.to(device);
		module.eval();
		return module;
	}	catch (const c10::Error& e)	{
		std::cout << e.what() << std::endl;
	}
	return TorchModule();
}

BoosterHandle ModelHelper::CreateLgbmModel(const std::string& model_file)
{
	BoosterHandle booster_handle = nullptr;
	int num_total_model = 0;
	LGBM_BoosterCreateFromModelfile(
		model_file.c_str(),
		&num_total_model,
		&booster_handle);
	return booster_handle;
}

double ModelHelper::Predict(const std::string& model_id, std::vector<float>& data, int embedding)
{
	std::unique_lock<std::mutex> mutex_lock(booster_mutex);
	
	double ret = 0.0;

	if (_mt == ModuleType::torch) {
		auto iter = _torchmds.find(model_id);
		if (iter == _torchmds.end()) {
			TorchModule handle = CreateTorchModel(fmt::format("{}/model/{}.model", dal::dh().GetRunDir(), model_id));
			//if (handle == nullptr) {
			//	return ret;
			//}
			_torchmds[model_id] = handle;
		}

		if (dal::dh().GetModelParams(model_id, "input_dim") == 2) {
			std::vector<torch::jit::IValue> inputs;
			if (embedding >= 0) {
				inputs.push_back(torch::tensor({ embedding }, torch::kInt));
			}
			int lf_len = dal::dh().GetModelParams(model_id, "lf_len");
			int sf_len = dal::dh().GetModelParams(model_id, "sf_len");
			int ct_len = dal::dh().GetModelParams(model_id, "ct_len");
			if (lf_len != sf_len) {
				std::cout << "failed: lf_len != sf_len" << std::endl;
				return ret;
			}
			for (int i = 0; i < lf_len - ct_len; i++) {
				data.push_back(0.0);
			}
			at::Tensor d = torch::from_blob(data.data(), {1, 3, (long long)lf_len });
			inputs.emplace_back(d);
			//std::cout << inputs << std::endl;
			auto output = _torchmds[model_id].forward(inputs).toTensor();
			//output.slice(/*dim=*/1, /*start=*/0, /*end=*/1)
			//std::cout << output << std::endl;
			//std::cout << output[0][0].item().toFloat() << std::endl;
			if (embedding >= 0) {
				return output[0].item().toFloat();
			}
			else {
				return output[0][0].item().toFloat();
			}
		}
		else {
			std::vector<torch::jit::IValue> inputs;
			if (embedding >= 0) {
				inputs.push_back(torch::tensor({ embedding }, torch::kInt));
			}
			at::Tensor d = torch::from_blob(data.data(), { 1, (long long)data.size() });
			inputs.push_back(d);
			//std::cout << inputs << std::endl;
			auto output = _torchmds[model_id].forward(inputs).toTensor();
			//output.slice(/*dim=*/1, /*start=*/0, /*end=*/1)
			//std::cout << output << std::endl;
			//std::cout << output[0][0].item().toFloat() << std::endl;
			if (embedding >= 0) {
				return output[0].item().toFloat();
			}
			else {
				return output[0][0].item().toFloat();
			}

		}
	}
	else {
		//if (_boosters.find(model_id) == _boosters.end()) {
		//	BoosterHandle handle = CreateLgbmModel(fmt::format("{}/model/{}.model", dal::dh().GetRunDir(), model_id));
		//	if (handle == nullptr) {
		//		return ret;
		//	}
		//	_boosters[model_id] = handle;
		//}

		//int64_t out_len = 0;
		//double out_result = 0.0;

		//if (PredictForMatSingleRow(_boosters[model_id], data, out_len, out_result) != 0) {
		//	return ret;
		//}
		//return out_result;
	}

	return ret;
}

/*
predict_type
* C_API_PREDICT_NORMAL: normal prediction, with transform(if needed)
* C_API_PREDICT_RAW_SCORE : raw score
* C_API_PREDICT_LEAF_INDEX : leaf index
*/
int ModelHelper::PredictForMatSingleRow(BoosterHandle handle, const std::vector<double>& data, int64_t& out_len, double& out_result)
{
	return LGBM_BoosterPredictForMatSingleRow(
		handle,
		&data[0],
		C_API_DTYPE_FLOAT64, // C_API_DTYPE_FLOAT32 or C_API_DTYPE_FLOAT64
		data.size(),	//number columns
		1, // 1 for row major, 0 for column major
		C_API_PREDICT_NORMAL, // C_API_PREDICT_RAW_SCORE, C_API_PREDICT_LEAF_INDEX
		50,
		"", //parameter,
		&out_len,
		&out_result
	);
}

/*
int ModelHelper::PredictForCSRSingleRow()
{
	return LGBM_BoosterPredictForCSRSingleRow(
			_booster_handle,
			);
	return 0;
}


int ModelHelper::PredictForCSR()
{
	return LGBM_BoosterPredictForCSR
	(
		_booster_handle,
		);
	return 0;
}

int ModelHelper::PredictForMat()
{
	return LGBM_BoosterPredictForMat
	(
		_booster_handle,
		);
	return 0;
}

int ModelHelper::PredictForFile()
{
	return LGBM_BoosterPredictForFile
	(
		_booster_handle,
		);
	return 0;
}
*/

void ModelHelper::Release()
{
	//std::unique_lock<std::mutex> mutex_lock(booster_mutex);
	//for (auto handle : _boosters) {
	//	LGBM_BoosterFree(handle.second);
	//}
}
}
