#ifdef _GTEST
#include "stdafx.h"
#endif
#include "data_api.h"
#include <DataHub.h>
#include <common.hpp>
#include "../core/logging.h"
#include "../DataHub/Settings.h"

namespace dal {

typedef long long ssize_t;

DataSource::DataSource(DataRunMode mode)
{
	if (!dal::dh().Init(mode)) {
		VLOG(1) << "data source initialize failed!";
		return;
	}
}

std::string DataSource::GetRunDir()
{
	return dal::dh().GetRunDir();
}

size_t DataSource::GetSecNum()
{
	return dal::dh().GetSecCount();
}

std::string DataSource::GetSecName(const std::string& label)
{
	return kits::GbkToUtf8(dal::dh().GetSecName(label));
}

std::vector<std::string> DataSource::GetTradingDates(int begindate, int enddate)
{
	std::vector<std::string> ret;
	Calendars calendars = dal::dh().GetCalendars();
	ExtDate dt0(begindate);
	ExtDate dt1(enddate);

	while (dt0.yyyymmdd() <= dt1.yyyymmdd()) {
		if (calendars.is_business_day(dt0.to_time_t())) {
			ret.push_back(dt0.to_short_string());
		}
		++dt0;
	}
	return ret;
}

std::string DataSource::GetFutLxLabel(const std::string& label)
{
	return dal::dh().GetFuturesLxLabel(label);
}

std::vector<std::string> DataSource::GetAllFutNames(const std::string& market) {
	std::vector<std::string> ds;
	for (auto& mk : dal::settings()._mk_futs) {
		if (!market.empty() && market.compare(mk.first) != 0)
			continue;
		for (auto& nc : mk.second) {
			ds.push_back(kits::GbkToUtf8(nc.first));
		}
	}
	return ds;
}

std::vector<std::string> DataSource::GetAllFutCodes(const std::string& market) {
	std::vector<std::string> ds;
	for (auto& mk : dal::settings()._mk_futs) {
		if (!market.empty() && market.compare(mk.first) != 0)
			continue;
		for (auto& nc : mk.second) {
			ds.push_back(nc.second);
		}
	}
	return ds;
}

std::string DataSource::GetFutCodeByName(const std::string& name) {
	std::string nm = kits::Utf8ToGbk(name);
	for (auto& mk : dal::settings()._mk_futs) {
		for (auto& nc : mk.second) {
			if (nm.compare(nc.first) == 0) {
				return nc.second;
			}
		}
	}
	return "";
}

std::string DataSource::GetFutNameByCode(const std::string& code) {
	for (auto& mk : dal::settings()._mk_futs) {
		for (auto& nc : mk.second) {
			if (code.compare(nc.second) == 0) {
				return kits::GbkToUtf8(nc.first);
			}
		}
	}
	return "";
}

std::vector<std::string> DataSource::GetBlockData(const std::string& name)
{
	bool flag = false;
	std::string blkname = kits::Utf8ToGbk(name);
	if (name.compare("ZLLX") == 0) {
		blkname.assign("ZLQH");
		flag = true;
	}
	std::vector<std::string> labels;
	BlockDataPtr blk_ptr = dal::dh().GetBlockData(blkname);
	if (blk_ptr.get()) {
		for (auto idx: *blk_ptr) {
			std::string lb = dal::dh().GetLabel(idx);
			if (flag) {
				labels.push_back(dal::dh().GetFuturesLxLabel(lb));
			}
			else {
				labels.push_back(lb);
			}
		}
	}
	return labels;
}

bool DataSource::StartProviders()
{
	return dal::dh().StartProviders();
}

void DataSource::StopProviders()
{
	dal::dh().StopProviders();
}

double DataSource::LastPrice(const std::string& label)
{
	return dal::dh().GetLastPrice(label);
}

py::array DataSource::GetHistoryData(const std::string& label, int length, py::list bardata, BarSize barsize, DoRight doright)
{
	HistoryDataSeqPtr hds_ptr = dh().GetHistoryData(label, length, barsize, doright);
	if (hds_ptr.get() == nullptr || hds_ptr->empty()) {
		return py::array();
	}

	int count = 0;
	if (hds_ptr->size() > length && length != 0) {
		count = hds_ptr->size() - length;
	}
	else {
		length = hds_ptr->size();
	}
	std::shared_ptr<std::vector<double>> series = std::shared_ptr<std::vector<double>>(new std::vector<double>);
	for (auto& hd : *hds_ptr) {
		if (count > 0) {
			count--;
			continue;
		}
		for (auto item : bardata) {
			switch ((BarData)item.cast<int>()) {
			case BarData::DateTime:
				series->push_back(hd._date.to_time_t());
				break;
			case BarData::Open:
				series->push_back(hd._open);
				break;
			case BarData::High:
				series->push_back(hd._high);
				break;
			case BarData::Low:
				series->push_back(hd._low);
				break;
			case BarData::Close:
				series->push_back(hd._close);
				break;
			case BarData::Volume:
				series->push_back(hd._volume);
				break;
			case BarData::Amount:
				series->push_back(hd._amount);
				break;
			case BarData::Median:
				break;
			case BarData::Typical:
				series->push_back((hd._high + hd._low + hd._close) / 3);
				break;
			case BarData::Weighted:
				break;
			case BarData::OpenInt:
				break;
			case BarData::PreClose:
				series->push_back(hd._preclose);
				break;
			default:
				break;
			}
		}
	}


	ssize_t              ndim = 2;
	std::vector<ssize_t> shape = { (ssize_t)length , (ssize_t)bardata.size() };
	std::vector<ssize_t> strides = { (ssize_t)(sizeof(double) * bardata.size()) , sizeof(double) };
	// return 2-D NumPy array
	return py::array(py::buffer_info(
		series->data(),                           /* data as contiguous array  */
		sizeof(double),                          /* size of one scalar        */
		py::format_descriptor<double>::format(), /* data type                 */
		ndim,                                    /* number of dimensions      */
		shape,                                   /* shape of the matrix       */
		strides                                  /* strides for each axis     */
	));
}

py::array DataSource::GetHistoryData2(const std::string& label, int startdate, int enddate, py::list bardata, BarSize barsize, DoRight doright)
{
	ExtDate begin = ExtDate(startdate);
	ExtDate end = ExtDate(enddate);
	HistoryDataSeqPtr hds_ptr = dh().GetHistoryData2(label, begin, end, barsize, doright);
	if (hds_ptr.get() == nullptr || hds_ptr->empty()) {
		return py::array();
	}

	std::shared_ptr<std::vector<double>> series = std::shared_ptr<std::vector<double>>(new std::vector<double>);
	for (auto& hd : *hds_ptr) {
		for (auto item : bardata) {
			switch ((BarData)item.cast<int>()) {
			case BarData::DateTime:
				series->push_back(hd._date.to_time_t());
				break;
			case BarData::Open:
				series->push_back(hd._open);
				break;
			case BarData::High:
				series->push_back(hd._high);
				break;
			case BarData::Low:
				series->push_back(hd._low);
				break;
			case BarData::Close:
				series->push_back(hd._close);
				break;
			case BarData::Volume:
				series->push_back(hd._volume);
				break;
			case BarData::Amount:
				series->push_back(hd._amount);
				break;
			case BarData::Median:
				break;
			case BarData::Typical:
				series->push_back((hd._high + hd._low + hd._close) / 3);
				break;
			case BarData::Weighted:
				break;
			case BarData::OpenInt:
				break;
			case BarData::PreClose:
				series->push_back(hd._preclose);
				break;
			default:
				break;
			}
		}
	}

	ssize_t              ndim = 2;
	std::vector<ssize_t> shape = { (ssize_t)hds_ptr->size() , (ssize_t)bardata.size() };
	std::vector<ssize_t> strides = { (ssize_t)(sizeof(double) * bardata.size()) , sizeof(double) };
	// return 2-D NumPy array
	return py::array(py::buffer_info(
		series->data(),                           /* data as contiguous array  */
		sizeof(double),                          /* size of one scalar        */
		py::format_descriptor<double>::format(), /* data type                 */
		ndim,                                    /* number of dimensions      */
		shape,                                   /* shape of the matrix       */
		strides                                  /* strides for each axis     */
	));
}


py::array DataSource::GetBarSeries(const std::string& label, py::list bardata, BarSize barsize)
{
	BarSeries* bs_ptr = dh().GetBarSeries(label, barsize);
	if (bs_ptr == nullptr || bs_ptr->Size() == 0) {
		return py::array();
	}

	std::vector<double> series;
	for (auto& hd : *bs_ptr->RefHistoryDataSeqPtr()) {
		for (auto item : bardata) {
			switch ((BarData)item.cast<int>()) {
			case BarData::DateTime:
				series.push_back(hd._date.to_time_t());
				break;
			case BarData::Open:
				series.push_back(hd._open);
				break;
			case BarData::High:
				series.push_back(hd._high);
				break;
			case BarData::Low:
				series.push_back(hd._low);
				break;
			case BarData::Close:
				series.push_back(hd._close);
				break;
			case BarData::Volume:
				series.push_back(hd._volume);
				break;
			case BarData::Amount:
				series.push_back(hd._amount);
				break;
			case BarData::Median:
				break;
			case BarData::Typical:
				series.push_back((hd._high + hd._low + hd._close) / 3);
				break;
			case BarData::Weighted:
				break;
			case BarData::OpenInt:
				break;
			case BarData::PreClose:
				series.push_back(hd._preclose);
				break;
			default:
				break;
			}
		}
	}

	ssize_t              ndim = 2;
	std::vector<ssize_t> shape = { (ssize_t)bs_ptr->Size(), (ssize_t)bardata.size() };
	std::vector<ssize_t> strides = { (ssize_t)(sizeof(double) * bardata.size()) , sizeof(double) };

	// return 2-D NumPy array
	return py::array(py::buffer_info(
		series.data(),                           /* data as contiguous array  */
		sizeof(double),                          /* size of one scalar        */
		py::format_descriptor<double>::format(), /* data type                 */
		ndim,                                    /* number of dimensions      */
		shape,                                   /* shape of the matrix       */
		strides                                  /* strides for each axis     */
	));
}

double DataSource::GetRangeBarAtr(const std::string& label, BarSize barsize)
{
	return dal::dh().GetRangeBarAtr(label, barsize);
}

double DataSource::GetDefaultRangeBarAtr(const std::string& label, BarSize barsize, bool islast)
{
	return dal::dh().GetDefaultRangeBarAtr(label, barsize, islast);
}

// todo: GetTickData modify
py::array DataSource::GetTickData(const std::string &label, int daynum) {
	TickDataSeqPtr tds_ptr = dh().GetTickData(label, daynum);
	if (tds_ptr.get() == nullptr || tds_ptr->empty()) {
		return py::array();
	}

	std::shared_ptr<std::vector<double>> series = std::shared_ptr<std::vector<double>>(new std::vector<double>);
  for (auto &td : *tds_ptr) {
    series->push_back(td._timestamp);
    series->push_back(td._price);
    series->push_back(td._volume);
    //series->push_back(td._amount);
  }

	ssize_t              ndim = 2;
	std::vector<ssize_t> shape = { (ssize_t)tds_ptr->size(), 4 };
	std::vector<ssize_t> strides = { (ssize_t)(sizeof(double) * 4) , sizeof(double) };
	// return 2-D NumPy array
	return py::array(py::buffer_info(
		series->data(),                           /* data as contiguous array  */
		sizeof(double),                          /* size of one scalar        */
		py::format_descriptor<double>::format(), /* data type                 */
		ndim,                                    /* number of dimensions      */
		shape,                                   /* shape of the matrix       */
		strides                                  /* strides for each axis     */
	));
}

int DataSource::GetStkCodeIndex(const std::string& label) {
	return dal::dh().GetStkCodeIndex(label);
}

int DataSource::GetFutCodeIndex(const std::string& label) {
	return dal::dh().GetFutCodeIndex(label);
}


} //dal
