#pragma once

#include "Factor.h"
#include "BasicFactors.h"
#include "Utils.h"
#include <torch/torch.h>
#include <memory>

namespace Spectre {

// Forward declarations
class FactorEngine;

// ============================================================================
// Label Generation Factors - Corresponding to Python's label.py
// ============================================================================

// ForwardReturnsFactor: Forward-looking returns for prediction targets
class ForwardReturnsFactor : public CustomFactor {
public:
    ForwardReturnsFactor(const std::shared_ptr<BaseFactor>& price_factor, int periods = 1)
        : CustomFactor(1, {price_factor}), m_periods(periods) {
        if (periods <= 0) {
            throw std::runtime_error("periods must be positive");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("ForwardReturnsFactor expects exactly one input.");
        }
        
        torch::Tensor prices = inputs[0];
        
        // Calculate forward returns: (price[t+periods] - price[t]) / price[t]
        torch::Tensor shifted_prices = torch::roll(prices, -m_periods, 1);
        torch::Tensor forward_returns = (shifted_prices - prices) / prices;
        
        // Set last m_periods values to NaN (no future data available)
        if (m_periods > 0) {
            forward_returns.slice(1, -m_periods, forward_returns.size(1)).fill_(std::numeric_limits<float>::quiet_NaN());
        }
        
        return forward_returns;
    }

private:
    int m_periods;
};

// BinaryClassificationLabel: Convert continuous values to binary labels
class BinaryClassificationLabel : public CrossSectionFactor {
public:
    BinaryClassificationLabel(const std::shared_ptr<BaseFactor>& factor, float threshold = 0.0f)
        : CrossSectionFactor(1, {factor}), m_threshold(threshold) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("BinaryClassificationLabel expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Convert to binary labels: 1 if > threshold, 0 otherwise
        torch::Tensor labels = (data > m_threshold).to(torch::kFloat32);
        
        // Preserve NaN values
        labels.masked_fill_(data.isnan(), std::numeric_limits<float>::quiet_NaN());
        
        return labels;
    }

private:
    float m_threshold;
};

// QuantileClassificationLabel: Convert to quantile-based classification labels
class QuantileClassificationLabel : public CrossSectionFactor {
public:
    QuantileClassificationLabel(const std::shared_ptr<BaseFactor>& factor, int n_classes = 3)
        : CrossSectionFactor(1, {factor}), m_n_classes(n_classes) {
        if (n_classes <= 1) {
            throw std::runtime_error("n_classes must be greater than 1");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("QuantileClassificationLabel expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate quantile boundaries for each time step
        std::vector<float> quantile_points;
        for (int i = 1; i < m_n_classes; ++i) {
            quantile_points.push_back(static_cast<float>(i) / m_n_classes);
        }
        
        torch::Tensor result = torch::zeros_like(data);
        
        for (int64_t t = 0; t < data.size(1); ++t) {
            torch::Tensor time_slice = data.select(1, t);
            torch::Tensor valid_mask = ~time_slice.isnan();
            
            if (valid_mask.sum().item<int64_t>() > 0) {
                torch::Tensor valid_data = time_slice.masked_select(valid_mask);
                
                // Calculate quantile thresholds
                std::vector<float> thresholds;
                for (float q : quantile_points) {
                    torch::Tensor quantile_val = torch::quantile(valid_data, q);
                    thresholds.push_back(quantile_val.item<float>());
                }
                
                // Assign class labels (0-based)
                torch::Tensor time_result = torch::zeros_like(time_slice);
                for (int64_t i = 0; i < time_slice.size(0); ++i) {
                    if (valid_mask[i].item<bool>()) {
                        float val = time_slice[i].item<float>();
                        int class_label = 0;
                        for (size_t j = 0; j < thresholds.size(); ++j) {
                            if (val <= thresholds[j]) {
                                class_label = static_cast<int>(j);
                                break;
                            }
                        }
                        if (class_label == 0 && val > thresholds.back()) {
                            class_label = m_n_classes - 1;
                        }
                        time_result[i] = class_label;
                    } else {
                        time_result[i] = std::numeric_limits<float>::quiet_NaN();
                    }
                }
                result.select(1, t).copy_(time_result);
            } else {
                result.select(1, t).fill_(std::numeric_limits<float>::quiet_NaN());
            }
        }
        
        return result;
    }

private:
    int m_n_classes;
};

// TopBottomLabel: Label top/bottom performers
class TopBottomLabel : public CrossSectionFactor {
public:
    TopBottomLabel(const std::shared_ptr<BaseFactor>& factor, float top_pct = 0.2f, float bottom_pct = 0.2f)
        : CrossSectionFactor(1, {factor}), m_top_pct(top_pct), m_bottom_pct(bottom_pct) {
        if (top_pct < 0.0f || top_pct > 1.0f || bottom_pct < 0.0f || bottom_pct > 1.0f) {
            throw std::runtime_error("Percentages must be between 0 and 1");
        }
        if (top_pct + bottom_pct > 1.0f) {
            throw std::runtime_error("Sum of top and bottom percentages cannot exceed 1");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("TopBottomLabel expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        
        // Calculate percentile thresholds for each time step
        torch::Tensor top_threshold = torch::quantile(data, 1.0f - m_top_pct, 0, true);
        torch::Tensor bottom_threshold = torch::quantile(data, m_bottom_pct, 0, true);
        
        // Create labels: 1 for top, -1 for bottom, 0 for middle
        torch::Tensor labels = torch::zeros_like(data);
        labels.masked_fill_(data >= top_threshold, 1.0f);
        labels.masked_fill_(data <= bottom_threshold, -1.0f);
        
        // Preserve NaN values
        labels.masked_fill_(data.isnan(), std::numeric_limits<float>::quiet_NaN());
        
        return labels;
    }

private:
    float m_top_pct;
    float m_bottom_pct;
};

// RegressionLabel: Continuous regression targets with optional transformations
class RegressionLabel : public CustomFactor {
public:
    enum class Transform {
        NONE,
        LOG,
        SQRT,
        STANDARDIZE
    };

    RegressionLabel(const std::shared_ptr<BaseFactor>& factor, Transform transform = Transform::NONE)
        : CustomFactor(1, {factor}), m_transform(transform) {
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RegressionLabel expects exactly one input.");
        }
        
        torch::Tensor data = inputs[0];
        torch::Tensor result = data.clone();
        
        switch (m_transform) {
            case Transform::NONE:
                // No transformation
                break;
                
            case Transform::LOG:
                // Log transformation (add small constant to avoid log(0))
                result = torch::log(torch::clamp(result, 1e-8f));
                break;
                
            case Transform::SQRT:
                // Square root transformation (handle negative values)
                result = torch::sign(result) * torch::sqrt(torch::abs(result));
                break;
                
            case Transform::STANDARDIZE:
                // Z-score standardization
                {
                    torch::Tensor mean_val = Spectre::nanmean(result, c10::nullopt, false);
                    torch::Tensor std_val = Spectre::nanstd(result, c10::nullopt, false);
                    result = (result - mean_val) / (std_val + 1e-8f);
                }
                break;
        }
        
        return result;
    }

private:
    Transform m_transform;
};

// TrendLabel: Label based on trend direction
class TrendLabel : public CustomFactor {
public:
    TrendLabel(const std::shared_ptr<BaseFactor>& price_factor, int lookback = 5, int lookahead = 5)
        : CustomFactor(1, {price_factor}), m_lookback(lookback), m_lookahead(lookahead) {
        if (lookback <= 0 || lookahead <= 0) {
            throw std::runtime_error("lookback and lookahead must be positive");
        }
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("TrendLabel expects exactly one input.");
        }
        
        torch::Tensor prices = inputs[0];
        
        // Calculate past trend (slope over lookback period)
        torch::Tensor past_prices = torch::roll(prices, m_lookback, 1);
        torch::Tensor past_trend = (prices - past_prices) / past_prices;
        
        // Calculate future trend (slope over lookahead period)
        torch::Tensor future_prices = torch::roll(prices, -m_lookahead, 1);
        torch::Tensor future_trend = (future_prices - prices) / prices;
        
        // Label based on trend consistency: 1 if both positive, -1 if both negative, 0 otherwise
        torch::Tensor labels = torch::zeros_like(prices);
        torch::Tensor both_positive = (past_trend > 0) & (future_trend > 0);
        torch::Tensor both_negative = (past_trend < 0) & (future_trend < 0);
        
        labels.masked_fill_(both_positive, 1.0f);
        labels.masked_fill_(both_negative, -1.0f);
        
        // Set boundary values to NaN
        labels.slice(1, 0, m_lookback).fill_(std::numeric_limits<float>::quiet_NaN());
        labels.slice(1, -m_lookahead, labels.size(1)).fill_(std::numeric_limits<float>::quiet_NaN());
        
        return labels;
    }

private:
    int m_lookback;
    int m_lookahead;
};

} // namespace Spectre
