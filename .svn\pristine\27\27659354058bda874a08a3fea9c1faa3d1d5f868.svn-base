#pragma once
#include <string>
#include <vector>
#include <map>
#include <LightGBM/c_api.h>
#include <torch/torch.h>
#include <torch/script.h>
#include <TradeSystem.h>
#include <onnxruntime/onnxruntime_cxx_api.h>

namespace bll {

using TorchModule = torch::jit::script::Module;
class ModelHelper
{
public:
	ModelHelper ();
	~ModelHelper();

	double Predict(const std::string& model_id, std::vector<float>& data, std::vector<int32_t>& embedding, ModelType mtype=ModelType::unkown);
	double Predict_torch(const std::string& model_id, std::vector<float>& data, std::vector<int32_t>& embedding);
	double Predict_onnx(const std::string& model_id, std::vector<float>& data, std::vector<int32_t>& embedding);
	double Gbdt(const std::string& model_id, std::vector<float>& data, int32_t embedding = -1);
	double Predict_tst(const std::string& model_id, std::vector<int32_t>& embedding, std::vector<float>& data, TemporalData& temporal, int timeenc);
	std::vector<std::array<int, 4>> BarGpt(
		const std::string& model_id, int timeenc, size_t block_size,
		std::vector<int32_t>& data, TemporalData& timefs, int max_new_bars,
		double temperature = 0.1, int top_k = 10);
	std::pair<int, int> GetBarGptBlockSize(const std::string& model_id);
	ModelType GetModelType(const std::string& model_id);
	std::array<double, 4> GetModelPredStat(const std::string& model_id);
	void Release();
	void Reset();

private:
	
	int Init();
	OrtSession* CreateOnnxModel(const std::string& model_file);
	TorchModule CreateTorchModel(const std::string& model_file);
	BoosterHandle CreateLgbmModel(const std::string& model_file);
	int PredictForMatSingleRow(BoosterHandle handle, const std::vector<float>& data, int64_t& out_len, double& out_result);
	int PredictForMatSingleRowFastInit(BoosterHandle handle, int ncol, FastConfigHandle& out_fastConfig);
	int PredictForMatSingleRowFast(FastConfigHandle handle, const std::vector<float>& data, int64_t& out_len, double& out_result);

	bool GetModelInOutInfo(const std::string& model_id);

	std::array<int, 4> IndexToBar(int64_t idx);
	//int PredictForCSRSingleRow();
	//int PredictForCSR();
	//int PredictForMat();
	//int PredictForFile();
	//ModuleType _mt;
	std::map<std::string, ModelType> _mts;
	std::map<std::string, BoosterHandle> _boosters;
	std::map<std::string, FastConfigHandle> _fast_configs;
	std::map<std::string, torch::jit::script::Module> _torchmds;
	std::map<std::string, std::vector<double>> _model_preds;

	//Ort::Env _ort_env;
	std::map<std::string, OrtSession*> _ort_sessions;

	std::map<std::string, std::vector<const char*>> _input_node_names;
	std::map<std::string, std::vector<std::vector<int64_t>>> _input_node_dims;
	std::map<std::string, std::vector<ONNXTensorElementDataType>> _input_types;
	std::map<std::string, std::vector<OrtValue*>> _input_tensors;

	std::map<std::string, std::vector<const char*>> _output_node_names;
	std::map<std::string, std::vector<std::vector<int64_t>>> _output_node_dims;
	std::map<std::string, std::vector<OrtValue*>> _output_tensors;

	std::vector<std::array<int, 4>> _bar_index;
	std::map<std::string, std::pair<int, int>> _gpt_block_sizes;
};

}

