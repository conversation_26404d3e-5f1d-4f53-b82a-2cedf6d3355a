/**
 * @file Portfolio.cpp
 * @brief Implementation of Portfolio class
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Portfolio.h"
#include <torch/torch.h>
#include <numeric>
#include <algorithm>
#include <cmath>

namespace Spectre {
namespace Trading {

double Portfolio::HistoryRecord::total_value() const {
    double total = cash;
    for (const auto& [asset, value] : asset_values) {
        total += value;
    }
    return total;
}

Portfolio::Portfolio(std::shared_ptr<StopModel> stop_model)
    : cash_(0.0), stop_model_(stop_model) {
    // Initialize with invalid time point
    current_dt_ = TimePoint{};
}

Portfolio::Portfolio(const Portfolio& other)
    : positions_(other.positions_), cash_(other.cash_), current_dt_(other.current_dt_),
      stop_model_(other.stop_model_), history_(other.history_), 
      funds_change_(other.funds_change_) {
}

Portfolio& Portfolio::operator=(const Portfolio& other) {
    if (this != &other) {
        positions_ = other.positions_;
        cash_ = other.cash_;
        current_dt_ = other.current_dt_;
        stop_model_ = other.stop_model_;
        history_ = other.history_;
        funds_change_ = other.funds_change_;
    }
    return *this;
}

Portfolio::Portfolio(Portfolio&& other) noexcept
    : positions_(std::move(other.positions_)), cash_(other.cash_),
      current_dt_(other.current_dt_), stop_model_(std::move(other.stop_model_)),
      history_(std::move(other.history_)), funds_change_(std::move(other.funds_change_)) {
}

Portfolio& Portfolio::operator=(Portfolio&& other) noexcept {
    if (this != &other) {
        positions_ = std::move(other.positions_);
        cash_ = other.cash_;
        current_dt_ = other.current_dt_;
        stop_model_ = std::move(other.stop_model_);
        history_ = std::move(other.history_);
        funds_change_ = std::move(other.funds_change_);
    }
    return *this;
}

double Portfolio::value() const {
    double total = cash_;
    for (const auto& [asset, position] : positions_) {
        if (position.shares() != 0) {
            total += position.value();
        }
    }
    return total;
}

double Portfolio::leverage() const {
    double total_abs_value = 0.0;
    double total_value = cash_;
    
    for (const auto& [asset, position] : positions_) {
        if (position.shares() != 0) {
            double pos_value = position.value();
            total_abs_value += std::abs(pos_value);
            total_value += pos_value;
        }
    }
    
    if (total_value == 0.0) {
        return 0.0;
    }
    
    return total_abs_value / total_value;
}

torch::Tensor Portfolio::returns() const {
    if (history_.empty()) {
        return torch::empty({0});
    }
    
    std::vector<double> values;
    values.reserve(history_.size());
    
    for (const auto& record : history_) {
        values.push_back(record.total_value());
    }
    
    // Add current value if not already in history
    if (current_dt_ != TimePoint{}) {
        values.push_back(value());
    }
    
    if (values.size() < 2) {
        return torch::empty({0});
    }
    
    // Calculate returns
    std::vector<double> returns_vec;
    returns_vec.reserve(values.size() - 1);
    
    for (size_t i = 1; i < values.size(); ++i) {
        if (values[i-1] != 0.0) {
            returns_vec.push_back(values[i] / values[i-1] - 1.0);
        } else {
            returns_vec.push_back(0.0);
        }
    }
    
    // Handle fund changes if any
    if (!funds_change_.empty()) {
        // This is a simplified implementation
        // In practice, you'd need to adjust returns for fund flows
        // TODO: Implement proper fund-adjusted returns calculation
    }
    
    return torch::from_blob(returns_vec.data(), {static_cast<int64_t>(returns_vec.size())}, 
                           torch::kDouble).clone();
}

int64_t Portfolio::shares(const AssetId& asset) const {
    auto it = positions_.find(asset);
    return (it != positions_.end()) ? it->second.shares() : 0;
}

bool Portfolio::has_position(const AssetId& asset) const {
    auto it = positions_.find(asset);
    return (it != positions_.end()) && (it->second.shares() != 0);
}

void Portfolio::set_datetime(const TimePoint& dt) {
    if (current_dt_ != TimePoint{}) {
        // Check that new time is not before current time
        if (dt < current_dt_) {
            throw std::invalid_argument("Cannot set a date less than the current date");
        }
        
        // If date has changed, add current state to history
        // Convert to days since epoch for comparison
        auto current_days = std::chrono::duration_cast<std::chrono::hours>(current_dt_.time_since_epoch()).count() / 24;
        auto new_days = std::chrono::duration_cast<std::chrono::hours>(dt.time_since_epoch()).count() / 24;

        if (new_days > current_days) {
            history_.push_back(get_today_record());
        }
    }
    
    current_dt_ = dt;
    
    // Update all positions' current datetime
    for (auto& [asset, position] : positions_) {
        position.set_current_dt(dt);
    }
}

double Portfolio::update(const AssetId& asset, int64_t amount, double fill_price, double commission) {
    if (current_dt_ == TimePoint{}) {
        throw std::runtime_error("Portfolio datetime must be set before updating positions");
    }
    
    if (amount == 0) {
        return 0.0;
    }
    
    auto it = positions_.find(asset);
    if (it != positions_.end()) {
        auto [is_empty, realized] = it->second.update(amount, fill_price, commission, current_dt_);
        if (is_empty) {
            positions_.erase(it);
        }
        return realized;
    } else {
        // Create new position
        positions_.emplace(asset, Position(amount, fill_price, commission, current_dt_, stop_model_));
        return 0.0;
    }
}

void Portfolio::update_cash(double amount, bool is_funds) {
    if (std::isnan(amount)) {
        throw std::invalid_argument("Cash amount cannot be NaN");
    }
    
    cash_ += amount;
    
    if (is_funds) {
        TimePoint fund_time = (current_dt_ != TimePoint{}) ? current_dt_ : TimePoint{};
        funds_change_.push_back({fund_time, amount});
    }
}

void Portfolio::update_value(const PriceFunction& prices) {
    update_value_function(prices);
}

void Portfolio::update_value(const PriceMap& prices) {
    update_value_map(prices);
}

void Portfolio::process_split(const AssetId& asset, double inverse_ratio, double last_price) {
    auto it = positions_.find(asset);
    if (it != positions_.end()) {
        double cash_from_split = it->second.process_split(inverse_ratio, last_price);
        update_cash(cash_from_split);
    }
}

void Portfolio::process_dividend(const AssetId& asset, double amount, double tax) {
    auto it = positions_.find(asset);
    if (it != positions_.end()) {
        double cash_from_dividend = it->second.process_dividend(amount, tax);
        update_cash(cash_from_dividend);
    }
}

void Portfolio::process_borrow_interest(double day_passed, double money_interest_rate, 
                                       double stock_interest_rate) {
    double interest = 0.0;
    
    // Calculate interest on short positions
    for (const auto& [asset, position] : positions_) {
        if (position.shares() < 0 && !std::isnan(position.value())) {
            interest += position.value() * (stock_interest_rate / 365.0) * day_passed;
        }
    }
    
    // Calculate interest on negative cash (margin)
    if (cash_ < 0) {
        interest += cash_ * (money_interest_rate / 365.0) * day_passed;
    }
    
    update_cash(interest);
}

void Portfolio::set_stop_model(std::shared_ptr<StopModel> stop_model) {
    stop_model_ = stop_model;
}

void Portfolio::clear() {
    positions_.clear();
    cash_ = 0.0;
    current_dt_ = TimePoint{};
    history_.clear();
    funds_change_.clear();
}

Portfolio::HistoryRecord Portfolio::get_today_record() const {
    HistoryRecord record;
    record.timestamp = current_dt_;
    record.cash = cash_;
    
    for (const auto& [asset, position] : positions_) {
        record.asset_values[asset] = position.value();
        record.asset_shares[asset] = static_cast<double>(position.shares());
        record.average_prices[asset] = position.average_price();
    }
    
    return record;
}

void Portfolio::update_value_function(const PriceFunction& prices) {
    for (auto& [asset, position] : positions_) {
        double price = prices(asset);
        if (!std::isnan(price)) {
            position.set_last_price(price);
        }
    }
}

void Portfolio::update_value_map(const PriceMap& prices) {
    for (auto& [asset, position] : positions_) {
        auto it = prices.find(asset);
        if (it != prices.end() && !std::isnan(it->second)) {
            position.set_last_price(it->second);
        }
    }
}

} // namespace Trading
} // namespace Spectre
