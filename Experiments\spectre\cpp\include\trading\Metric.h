#pragma once

/**
 * @file Metric.h
 * @brief Performance metrics calculation functions
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, He<PERSON>zh. All rights reserved.
 * @license Apache 2.0
 */

#include <torch/torch.h>
#include <tuple>
#include <vector>

namespace Spectre {
namespace Trading {
namespace Metric {

/**
 * @brief Calculate drawdown and drawdown duration
 * @param cumulative_returns Tensor of cumulative returns
 * @return Tuple of (drawdown, drawdown_duration) tensors
 */
std::tuple<torch::Tensor, torch::Tensor> drawdown(const torch::Tensor& cumulative_returns);

/**
 * @brief Calculate Sharpe ratio
 * @param daily_returns Tensor of daily returns
 * @param annual_risk_free_rate Annual risk-free rate (default: 0.0)
 * @return Sharpe ratio
 */
double sharpe_ratio(const torch::Tensor& daily_returns, double annual_risk_free_rate = 0.0);

/**
 * @brief Calculate turnover ratio
 * @param positions Portfolio positions tensor
 * @param transactions Transaction records tensor
 * @return Turnover ratio tensor
 */
torch::Tensor turnover(const torch::Tensor& positions, const torch::Tensor& transactions);

/**
 * @brief Calculate annual volatility
 * @param daily_returns Tensor of daily returns
 * @return Annual volatility
 */
double annual_volatility(const torch::Tensor& daily_returns);

/**
 * @brief Calculate maximum drawdown
 * @param cumulative_returns Tensor of cumulative returns
 * @return Maximum drawdown value
 */
double max_drawdown(const torch::Tensor& cumulative_returns);

/**
 * @brief Calculate Calmar ratio (annual return / max drawdown)
 * @param daily_returns Tensor of daily returns
 * @return Calmar ratio
 */
double calmar_ratio(const torch::Tensor& daily_returns);

/**
 * @brief Calculate Sortino ratio
 * @param daily_returns Tensor of daily returns
 * @param annual_risk_free_rate Annual risk-free rate (default: 0.0)
 * @return Sortino ratio
 */
double sortino_ratio(const torch::Tensor& daily_returns, double annual_risk_free_rate = 0.0);

/**
 * @brief Calculate information ratio
 * @param returns Portfolio returns
 * @param benchmark_returns Benchmark returns
 * @return Information ratio
 */
double information_ratio(const torch::Tensor& returns, const torch::Tensor& benchmark_returns);

/**
 * @brief Calculate beta relative to benchmark
 * @param returns Portfolio returns
 * @param benchmark_returns Benchmark returns
 * @return Beta coefficient
 */
double beta(const torch::Tensor& returns, const torch::Tensor& benchmark_returns);

/**
 * @brief Calculate alpha relative to benchmark
 * @param returns Portfolio returns
 * @param benchmark_returns Benchmark returns
 * @param risk_free_rate Risk-free rate
 * @return Alpha coefficient
 */
double alpha(const torch::Tensor& returns, const torch::Tensor& benchmark_returns, 
             double risk_free_rate = 0.0);

/**
 * @brief Calculate Value at Risk (VaR)
 * @param returns Return series
 * @param confidence_level Confidence level (default: 0.05 for 95% VaR)
 * @return VaR value
 */
double value_at_risk(const torch::Tensor& returns, double confidence_level = 0.05);

/**
 * @brief Calculate Conditional Value at Risk (CVaR)
 * @param returns Return series
 * @param confidence_level Confidence level (default: 0.05 for 95% CVaR)
 * @return CVaR value
 */
double conditional_value_at_risk(const torch::Tensor& returns, double confidence_level = 0.05);

/**
 * @brief Calculate win rate (percentage of positive returns)
 * @param returns Return series
 * @return Win rate as percentage
 */
double win_rate(const torch::Tensor& returns);

/**
 * @brief Calculate profit factor (gross profit / gross loss)
 * @param returns Return series
 * @return Profit factor
 */
double profit_factor(const torch::Tensor& returns);

/**
 * @brief Calculate average win and average loss
 * @param returns Return series
 * @return Tuple of (average_win, average_loss)
 */
std::tuple<double, double> win_loss_ratio(const torch::Tensor& returns);

/**
 * @brief Calculate comprehensive performance metrics
 * @param returns Daily returns tensor
 * @param benchmark_returns Optional benchmark returns
 * @param risk_free_rate Risk-free rate (default: 0.0)
 * @return Map of metric names to values
 */
std::unordered_map<std::string, double> calculate_performance_metrics(
    const torch::Tensor& returns,
    const torch::Tensor& benchmark_returns = torch::Tensor{},
    double risk_free_rate = 0.0
);

} // namespace Metric
} // namespace Trading
} // namespace Spectre
