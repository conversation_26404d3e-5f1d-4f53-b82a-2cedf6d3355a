/// @file
/// <AUTHOR> <<EMAIL>>
/// [GitHub Repository](https://github.com/dpilger26/NumCpp)
/// @version 2.0.0
///
/// @section License
/// Copyright 2020 David <PERSON>
///
/// Permission is hereby granted, free of charge, to any person obtaining a copy of this
/// software and associated documentation files(the "Software"), to deal in the Software
/// without restriction, including without limitation the rights to use, copy, modify,
/// merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
/// permit persons to whom the Software is furnished to do so, subject to the following
/// conditions :
///
/// The above copyright notice and this permission notice shall be included in all copies
/// or substantial portions of the Software.
///
/// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
/// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
/// PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE
/// FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
/// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
/// DEALINGS IN THE SOFTWARE.
///
/// @section Description
/// Functions for working with NdArrays
///
#pragma once

#include "NumCpp/Core/Shape.hpp"
#include "NumCpp/Core/Types.hpp"
#include "NumCpp/Core/Internal/StaticAsserts.hpp"
#include "NumCpp/Functions/full.hpp"
#include "NumCpp/NdArray.hpp"

namespace nc
{
    //============================================================================
    // Method Description:
    ///						Return a new array of given shape and type, filled with zeros.
    ///
    ///                     NumPy Reference: https://www.numpy.org/devdocs/reference/generated/numpy.zeros.html
    ///
    /// @param
    ///				inSquareSize
    /// @return
    ///				NdArray
    ///
    template<typename dtype>
    NdArray<dtype> zeros(uint32 inSquareSize) 
    {
        STATIC_ASSERT_ARITHMETIC_OR_COMPLEX(dtype);

        return full(inSquareSize, dtype{ 0 });
    }

    //============================================================================
    // Method Description:
    ///						Return a new array of given shape and type, filled with zeros.
    ///
    ///                     NumPy Reference: https://www.numpy.org/devdocs/reference/generated/numpy.zeros.html
    ///
    /// @param				inNumRows
    /// @param				inNumCols
    /// @return
    ///				NdArray
    ///
    template<typename dtype>
    NdArray<dtype> zeros(uint32 inNumRows, uint32 inNumCols) 
    {
        STATIC_ASSERT_ARITHMETIC_OR_COMPLEX(dtype);

        return full(inNumRows, inNumCols, dtype{ 0 });
    }

    //============================================================================
    // Method Description:
    ///						Return a new array of given shape and type, filled with zeros.
    ///
    ///                     NumPy Reference: https://www.numpy.org/devdocs/reference/generated/numpy.zeros.html
    ///
    /// @param
    ///				inShape
    /// @return
    ///				NdArray
    ///
    template<typename dtype>
    NdArray<dtype> zeros(const Shape& inShape) 
    {
        STATIC_ASSERT_ARITHMETIC_OR_COMPLEX(dtype);

        return full(inShape, dtype{ 0 });
    }
}
