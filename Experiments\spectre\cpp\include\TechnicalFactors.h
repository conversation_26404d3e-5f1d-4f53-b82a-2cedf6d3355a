#pragma once

#include "Factor.h"
#include "BasicFactors.h"
#include "StatisticalFactors.h"
#include "Utils.h"

#include <torch/torch.h>
#include <memory>

namespace Spectre {

// Forward declarations
class FactorEngine;
class NormalizedBollingerBands;

// ============================================================================
// Technical Analysis Factors - Corresponding to Python's technical.py
// ============================================================================

// BollingerBands: Bollinger Bands indicator
class BollingerBands : public CustomFactor {
public:
    BollingerBands(int win = 20, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, float k = 2.0f)
        : CustomFactor(1, {}), m_k(k), m_original_win(win) {
        m_min_win = 2;
        
        if (inputs.empty()) {
            throw std::runtime_error("BollingerBands requires at least one input (close price)");
        }
        
        // Create sub-factors for MA and STDDEV
        std::vector<std::shared_ptr<BaseFactor>> close_input = {inputs[0]};
        m_ma_factor = std::make_shared<SimpleMovingAverage>(close_input[0], win);
        m_std_factor = std::make_shared<StandardDeviation>(win, close_input);
        
        // Set inputs: close, ma, std, k
        m_inputs = {inputs[0], m_ma_factor, m_std_factor};
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 3) {
            throw std::runtime_error("BollingerBands expects exactly three inputs (close, ma, std).");
        }
        
        torch::Tensor closes = inputs[0];
        torch::Tensor ma = inputs[1];
        torch::Tensor std_val = inputs[2];
        
        torch::Tensor d = m_k * std_val;
        torch::Tensor upper = ma + d;
        torch::Tensor lower = ma - d;
        
        // Return as [upper, middle, lower] in the last dimension
        return torch::stack({upper, ma, lower}, -1);
    }
    
    // Create normalized version
    std::shared_ptr<NormalizedBollingerBands> normalized() {
        return std::make_shared<NormalizedBollingerBands>(m_original_win, m_inputs, m_k);
    }

private:
    float m_k;
    int m_original_win;
    std::shared_ptr<SimpleMovingAverage> m_ma_factor;
    std::shared_ptr<StandardDeviation> m_std_factor;
};

// NormalizedBollingerBands: Normalized Bollinger Bands
class NormalizedBollingerBands : public CustomFactor {
public:
    NormalizedBollingerBands(int win = 20, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {}, float k = 2.0f)
        : CustomFactor(1, {}), m_k(k) {
        m_min_win = 2;
        
        if (inputs.empty()) {
            throw std::runtime_error("NormalizedBollingerBands requires at least one input (close price)");
        }
        
        // Create sub-factors for MA and STDDEV
        std::vector<std::shared_ptr<BaseFactor>> close_input = {inputs[0]};
        m_ma_factor = std::make_shared<SimpleMovingAverage>(close_input[0], win);
        m_std_factor = std::make_shared<StandardDeviation>(win, close_input);
        
        // Set inputs: close, ma, std
        m_inputs = {inputs[0], m_ma_factor, m_std_factor};
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 3) {
            throw std::runtime_error("NormalizedBollingerBands expects exactly three inputs (close, ma, std).");
        }
        
        torch::Tensor closes = inputs[0];
        torch::Tensor ma = inputs[1];
        torch::Tensor std_val = inputs[2];
        
        return (closes - ma) / (m_k * std_val);
    }

private:
    float m_k;
    std::shared_ptr<SimpleMovingAverage> m_ma_factor;
    std::shared_ptr<StandardDeviation> m_std_factor;
};

// RSI: Relative Strength Index
class RSI : public CustomFactor {
public:
    RSI(int win = 14, const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(win + 1, inputs) {
        m_min_win = 2;
        m_rsi_win = win;
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 1) {
            throw std::runtime_error("RSI expects exactly one input.");
        }
        
        if (inputs[0].dim() == 3) {
            torch::Tensor data = inputs[0];
            
            // Calculate price changes
            torch::Tensor diff = data.diff(1, 2);  // diff along window dimension
            
            // Separate gains and losses
            torch::Tensor gains = torch::clamp(diff, 0.0f);
            torch::Tensor losses = torch::clamp(-diff, 0.0f);
            
            // Calculate average gains and losses
            torch::Tensor avg_gains = Spectre::nanmean(gains, 2, false);
            torch::Tensor avg_losses = Spectre::nanmean(losses, 2, false);
            
            // Calculate RS and RSI
            torch::Tensor rs = avg_gains / (avg_losses + 1e-8f);  // Add small epsilon to avoid division by zero
            torch::Tensor rsi = 100.0f - (100.0f / (1.0f + rs));
            
            return rsi;
        } else {
            throw std::runtime_error("RSI requires 3D input tensor");
        }
    }

private:
    int m_rsi_win;
};

// MACD: Moving Average Convergence Divergence
class MACD : public CustomFactor {
public:
    MACD(int fast_period = 12, int slow_period = 26, int signal_period = 9,
         const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(1, {}), m_signal_period(signal_period) {
        m_min_win = 2;
        
        if (inputs.empty()) {
            throw std::runtime_error("MACD requires at least one input (close price)");
        }
        
        // Create EMA factors
        std::vector<std::shared_ptr<BaseFactor>> close_input = {inputs[0]};
        m_fast_ema = std::make_shared<ExponentialWeightedMovingAverage>(fast_period, close_input);
        m_slow_ema = std::make_shared<ExponentialWeightedMovingAverage>(slow_period, close_input);
        
        m_inputs = {inputs[0], m_fast_ema, m_slow_ema};
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 3) {
            throw std::runtime_error("MACD expects exactly three inputs (close, fast_ema, slow_ema).");
        }
        
        torch::Tensor fast_ema = inputs[1];
        torch::Tensor slow_ema = inputs[2];
        
        // MACD line = fast EMA - slow EMA
        torch::Tensor macd_line = fast_ema - slow_ema;
        
        // For simplicity, return just the MACD line
        // In a full implementation, you'd also calculate the signal line and histogram
        return macd_line;
    }

private:
    int m_signal_period;
    std::shared_ptr<ExponentialWeightedMovingAverage> m_fast_ema;
    std::shared_ptr<ExponentialWeightedMovingAverage> m_slow_ema;
};

// StochasticOscillator: Stochastic %K and %D
class StochasticOscillator : public CustomFactor {
public:
    StochasticOscillator(int k_period = 14, int d_period = 3,
                        const std::vector<std::shared_ptr<BaseFactor>>& inputs = {})
        : CustomFactor(k_period, {}), m_d_period(d_period) {
        m_min_win = 2;
        
        if (inputs.size() < 3) {
            throw std::runtime_error("StochasticOscillator requires three inputs (high, low, close)");
        }
        
        m_inputs = inputs;  // high, low, close
    }

    torch::Tensor compute(const std::vector<torch::Tensor>& inputs) override {
        if (inputs.size() != 3) {
            throw std::runtime_error("StochasticOscillator expects exactly three inputs (high, low, close).");
        }
        
        if (inputs[0].dim() == 3) {
            torch::Tensor highs = inputs[0];
            torch::Tensor lows = inputs[1];
            torch::Tensor closes = inputs[2];
            
            // Get current close (last value in window)
            torch::Tensor current_close = closes.select(2, -1);
            
            // Calculate highest high and lowest low in the period
            torch::Tensor highest_high = Spectre::nanmax(highs, 2, false);
            torch::Tensor lowest_low = Spectre::nanmin(lows, 2, false);
            
            // Calculate %K
            torch::Tensor k_percent = 100.0f * (current_close - lowest_low) / (highest_high - lowest_low + 1e-8f);
            
            return k_percent;
        } else {
            throw std::runtime_error("StochasticOscillator requires 3D input tensors");
        }
    }

private:
    int m_d_period;
};

} // namespace Spectre
