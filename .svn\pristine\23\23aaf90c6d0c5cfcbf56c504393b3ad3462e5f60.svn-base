
#include "stdafx.h"
#include "ProductKeyManagerApp.h"
#include "ProductKeyManagerDlg.h"
#include "DlgLogon.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/*** Definition of the class "CProductKeyManagerApp" **************************/

/*** The one and only CProductKeyManagerApp object ****************************/
CProductKeyManagerApp theApp;

/*** Constructor *************************************************************/
CProductKeyManagerApp::CProductKeyManagerApp()
{
	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
}

/*** Public member functions *************************************************/
BOOL CProductKeyManagerApp::InitInstance()
{
	// Standard initialization
	// If you are not using these features and wish to reduce the size
	//  of your final executable, you should remove from the following
	//  the specific initialization routines you do not need.
	DlgLogon ddlg;
	if (ddlg.DoModal() != IDOK) {
		return FALSE;
	}

	CProductKeyManagerDlg dlg;
	m_pMainWnd = &dlg;
	INT_PTR nResponse = dlg.DoModal();
	if (nResponse == IDOK)
	{
		// TODO: Place code here to handle when the dialog is
		//  dismissed with OK
	}
	else if (nResponse == IDCANCEL)
	{
		// TODO: Place code here to handle when the dialog is
		//  dismissed with Cancel
	}

	// Since the dialog has been closed, return FALSE so that we exit the
	//  application, rather than start the application's message pump.
	return FALSE;
}

/*** Message handler table ***************************************************/
BEGIN_MESSAGE_MAP(CProductKeyManagerApp, CWinApp)
	//{{AFX_MSG_MAP(CProductKeyManagerApp)
		// NOTE - the ClassWizard will add and remove mapping macros here.
		//    DO NOT EDIT what you see in these blocks of generated code!
	//}}AFX_MSG
	ON_COMMAND(ID_HELP, CWinApp::OnHelp)
END_MESSAGE_MAP()
