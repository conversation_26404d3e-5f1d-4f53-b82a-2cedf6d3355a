/**
 * @file StopModel.cpp
 * @brief Implementation of StopModel classes
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/StopModel.h"
#include "trading/Position.h"
#include <cmath>
#include <algorithm>

namespace Spectre {
namespace Trading {

// PnLDecayTrailingStopTracker implementation
double PnLDecayTrailingStopTracker::current() const {
    if (!tracking_position_) {
        return 0.0;
    }
    
    Position* pos = tracking_position_;
    double shares_sign = (pos->shares() > 0) ? 1.0 : ((pos->shares() < 0) ? -1.0 : 0.0);
    double pnl = (recorded_price_ / pos->average_price() - 1.0) * shares_sign;
    
    // Clamp PnL based on target sign
    if (target_ > 0) {
        pnl = std::max(pnl, 0.0);
    } else {
        pnl = std::min(pnl, 0.0);
    }
    
    return pnl;
}

// TimeDecayTrailingStopTracker implementation
double TimeDecayTrailingStopTracker::current() const {
    if (!tracking_position_) {
        return 0.0;
    }
    
    Position* pos = tracking_position_;
    auto period = pos->period();
    return period.count(); // Return period in seconds
}

} // namespace Trading
} // namespace Spectre
