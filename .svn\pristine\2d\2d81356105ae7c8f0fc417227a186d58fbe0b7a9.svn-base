#pragma once
#include <BusinessDataDef.h>

namespace dal {
	class CrossSection
	{
	public:
		enum class Period{fast, slow};
		struct CSData {
			unsigned long key;
			float value;
		};

		CrossSection() {};
		~CrossSection() {};

		bool Init(const std::string& blk_name, const std::string& name);
		void Update();
		MarketTaData* GetMarketTaData() { return &_mtd; }

	private:
		void Save();
		bool Load();
		inline unsigned long GetSlowIndex(const time_t& tt) const {
			return (tt / 86400);
		}
		inline unsigned long GetFastIndex(const time_t& tt) const {
			return (tt / 300);
		}
		void CalcIndex(Period period);

		std::string _name;
		std::vector<CSData> _fast_ds;
		std::vector<CSData> _slow_ds;

		std::string _blk_name;
		BlockDataPtr _blk_ptr;
		MarketTaData _mtd;
	};
}

