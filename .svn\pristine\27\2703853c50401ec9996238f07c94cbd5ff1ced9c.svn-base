/*******************************************************************************
* File name： Order.h
* Description: HuobiSwap api Order header files.
* Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
* Version: 0.0.1
* Date： 2020-05-26
* History: 
*
*******************************************************************************/

#ifndef ORDER_H
#define ORDER_H

#include <string>
#include <vector>
#include "HuobiSwap/Enums.h"
#include "HuobiSwap/Decimal.h"
#include "HuobiSwap/Trade.h"

namespace Hu<PERSON>iSwap {

    /**
     * The detail order information.
     */
    struct Order {
        /**
         * symbol here is the contract_code.
         */
        std::string symbol;

        /**
         * The quantity in this order.
         */
        Decimal volume;

        /**
         * The limit price of order.
         */
        Decimal price;


        /**
         * The Order price type.
         */
        OrderPriceType order_price_type = OrderPriceType::SDK_NOTSUPPORT;

        /**
         * The direction 
         */
        OrderSide direction = OrderSide::SDK_NOTSUPPORT;

        /**
         * The offset 
         */
        TradeOffset offset = TradeOffset::SDK_NOTSUPPORT;

        /**
         * order status
         */
        OrderStatus status = OrderStatus::SDK_NOTSUPPORT;

        /**
         * order id
         */
        long long order_id;

        /**
         * client_order_id
         */
        long client_order_id;

        /**
         * order source.
         */
        OrderSource source = OrderSource::SDK_NOTSUPPORT;   

        /**
         * order type.
         */
        OrderType order_type = OrderType::SDK_NOTSUPPORT; 

        /**
         * The UNIX formatted timestamp in UTC when the order was created.
         */
        long long created_at = 0;

        /**
         * accumulated trade volume
         */
        Decimal trade_volume = 0;

        /**
         *  accumulated turnover 
         */
        Decimal trade_turnover = 0;

        /**
         * accumulated fee
         */
        Decimal fee = 0;

        /**
         * trade average order price
         */
        Decimal trade_avg_price = 0;

        /**
         * margin frozen
         */
        Decimal margin_frozen = 0;

        /**
         * profit
         */
        Decimal profit = 0;

        /**
         * liquidation type
         */
        LiquidationType liquidation_type = LiquidationType::SDK_NOTSUPPORT;

        /**
         * match results
         */
        std::vector<Trade> trades;

    };

}


#endif /* ORDER_H */
