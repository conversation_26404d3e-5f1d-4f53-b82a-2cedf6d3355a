/*******************************************************************************
* File name： CandlestickEvent.h
* Description: HuobiSwap api CandlestickEvent header files.
* Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(and<PERSON><PERSON><PERSON><EMAIL>)
* Version: 0.0.1
* Date： 2020-05-26
* History: 
*
*******************************************************************************/

#ifndef CANDELSICKEVENT_H
#define CANDELSICKEVENT_H

#include <string>
#include "HuobiSwap/Enums.h"
#include "HuobiSwap/Candlestick.h"

namespace Hu<PERSON>iSwap {

    /**
     * The candlestick/kline data received by subscription of candlestick/kline.
     */
    struct CandlestickEvent {
        /**
         * The symbol you subscribed.
         */
        std::string symbol;
        
        /**
         * The UNIX formatted timestamp generated by server in UTC.
         */
        long long timestamp = 0;
        
        /**
         * The candlestick/kline interval you subscribed.
         */
        CandlestickInterval interval = CandlestickInterval::SDK_NOTSUPPORT;
        
        /**
         * The data of candlestick/kline.
         */
        Candlestick data;
    };
}
#endif /* CANDELSICKEVENT_H */

