#include "stdafx.h"
#include "BrokerProxyI.h"
#include <FixApi.h>
#include "DlgBroker.h"
#include "../Utils/common.h"
#include "../Broker/TradingBroker.h"
#include "../DataHub/Settings.h"

extern TradingBroker g_tb;


BrokerProxyI::BrokerProxyI()
{
}

BrokerProxyI::~BrokerProxyI()
{
}


void BrokerProxyI::AddCallbackReceiver(const ::std::string& clientid, const ::broker::CallbackReceiverPrx& receiver, const ::Ice::Current&)
{
	((DlgBroker*)AfxGetApp()->m_pMainWnd)->AddClient(clientid, receiver);
}

void BrokerProxyI::AddClient(const ::Ice::Identity& ident, const ::Ice::Current& current)
{
	broker::CallbackReceiverPrx client = broker::CallbackReceiverPrx::uncheckedCast(current.con->createProxy(ident));
	((DlgBroker*)AfxGetApp()->m_pMainWnd)->AddClient(ident.name, client);
}

bool BrokerProxyI::IsLogon(const ::std::string& accountid, const ::Ice::Current&)
{
	return g_tb.IsLogon(accountid);
}

bool BrokerProxyI::SendOrder(const ::std::string& accountid, const ::std::string& id,
	const ::std::string& label, ::Ice::Int ordtype, ::Ice::Double price, ::Ice::Long qty, ::Ice::Int openclose, const ::Ice::Current&)
{
	return g_tb.SendOrder(accountid, id, label, ordtype, price, qty, (OpenClose)openclose);
}


bool BrokerProxyI::CancelOrder(const ::std::string& accountid, const ::std::string& ordid, const ::std::string& id, const ::std::string& label, ::Ice::Int ordtype, ::Ice::Long qty, const ::Ice::Current&)
{
	return g_tb.CancelOrder(accountid, ordid, id, label, ordtype, qty);
}


bool BrokerProxyI::QueryBrokerData(const ::std::string& accountid, const ::std::string& id, const ::std::string& label, ::Ice::Int type, const ::Ice::Current&)
{
	return g_tb.QueryBrokerData(accountid, id, label, type);
}

void BrokerProxyI::Shutdown(const ::Ice::Current& current)
{
	current.adapter->getCommunicator()->shutdown();
}
