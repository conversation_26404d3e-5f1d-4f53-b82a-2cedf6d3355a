#ifndef BOOK_CHAPTER6_TASK_PROCESSOR_MULTITHREAD_HPP
#define BOOK_CHAPTER6_TASK_PROCESSOR_MULTITHREAD_HPP

#if defined(_MSC_VER) && (_MSC_VER >= 1200)
# pragma once
#endif // defined(_MSC_VER) && (_MSC_VER >= 1200)

#include <jobs/tasks_processor_network.hpp>

namespace tp_multithread {

class tasks_processor: public tp_network::tasks_processor {
public:
  static tasks_processor& get();

	boost::thread_group tg;
  // Default value will attempt to guess optimal count of threads
  void start_multiple(std::size_t threads_count = 0) {
    if (!threads_count) {
        threads_count = (std::min)(
          static_cast<int>(boost::thread::hardware_concurrency() > 4 ? boost::thread::hardware_concurrency() - 2 : boost::thread::hardware_concurrency()), 6);
    }

    // one thread is the current thread
    if (threads_count > 1) {
      --threads_count;
    }

		std::cout << ">>> Starting work thread group: " << threads_count << " threads" << std::endl;
		if (tg.size() > 0) {
			tg.join_all();
		}

    for (std::size_t i = 0; i < threads_count; ++i) {
        tg.create_thread(boost::bind(&boost::asio::io_service::run, boost::ref(ios_)));
    }

    ios_.run();
    //tg.join_all();
  }

	void stop_multiple() {
		tg.join_all();
	}
};

} // namespace tp_multithread

#endif // BOOK_CHAPTER6_TASK_PROCESSOR_MULTITHREAD_HPP
