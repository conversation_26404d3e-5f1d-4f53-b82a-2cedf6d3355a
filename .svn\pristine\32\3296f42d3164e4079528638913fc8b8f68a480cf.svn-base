#pragma once

#include <patterns/singleton.hpp>
#include <boost/function.hpp>
#include <FixApi.h>

class FixApiI : public tb::FixApi, public utils::Singleton<FixApiI>
{
	friend class utils::Singleton<FixApiI>;
private:
	TbMsgCallback _msgCallback;

	FixApiI(void){};
public:
	~FixApiI(void);
	virtual bool RegisterTbMsgCallback(const std::string& accountid, const TbMsgCallback& msgCallback );
	virtual bool RegisterTbQueryBrokerDataCallback(const std::string& accountid, const TbQueryBrokerDataCallback& qbdCallback);

	virtual bool Logon(const std::string& accountid, const std::string& userid, const std::string& pwd, int logintype, const std::string& settingfile);
	virtual void Logout(const std::string& accountid);
	virtual bool IsLogon(const std::string& accountid);

	virtual bool SendOrder(const std::string& accountid, const std::string& id, const std::string& label, int ordtype, double price, long qty);
	virtual bool CancelOrder(const std::string& accountid, const std::string& ordid, const std::string& id, const std::string& label, int ordtype, long qty);
	virtual bool QueryBrokerData(const std::string& accountid, const std::string& id, const std::string& label, int querytype);

	virtual void Release();
};
