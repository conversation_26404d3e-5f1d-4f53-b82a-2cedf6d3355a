E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Trading.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_trading.dir\Release\Trading.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Position.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_trading.dir\Release\Position.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Portfolio.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_trading.dir\Release\Portfolio.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Event.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_trading.dir\Release\Event.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\StopModel.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_trading.dir\Release\StopModel.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Metric.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_trading.dir\Release\Metric.obj
