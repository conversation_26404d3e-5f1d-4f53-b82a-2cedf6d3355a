﻿
// ReplayToolsDlg.h: 头文件
//

#pragma once
#include <Portfolio.h>
//#include "../3rdParty/ChartCtrl/ChartCtrl.h"
#include <afxcmn.h>
#include <quicklist/QuickList.h>
#include <ChartCtrl\ChartCtrl.h>
#include <order.hpp>


// CReplayToolsDlg 对话框
class CReplayToolsDlg : public CDialogEx
{
// 构造
public:
	CReplayToolsDlg(CWnd* pParent = nullptr);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_REPLAYTOOLS_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持


// 实现
protected:
	HICON m_hIcon;
	std::vector<Order> _orders;
	static const int Cols = 18;
	CChartMouseListener* _pMouserListener;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	afx_msg LRESULT OnGetListItem(WPARAM wParam, LPARAM lParam);
	DECLARE_MESSAGE_MAP()

	Portfolio* _pf_ptr;
	dal::InstrumentPtr _itm_ptr;
	std::string _label;
	std::vector<std::array<std::string, Cols>> _listText;
	std::vector<std::string> _exprNames;

	int m_nCurSel;

public:
	CString m_sPortfolio;
	void UpdateCombox();
	void ProcessNotifyInfo(const NotifyInfo& ni);

	CQuickList m_listOrders;
	void InitOrderList();
	void FillOrderList();		//当OrderData记录个数发生改变时使用


	CChartCtrl m_chartSlow;
	CChartCtrl m_chartFast;
	void InitChart();
	void DrawSlowChart();
	void DrawFastChart();
	void DrawMainBand(CChartCtrl& chart, RealSeriesPtr* bs, BarSize barsize);
	void DrawTrendHL(CChartCtrl& chart, RealSeriesPtr* bs, BarSize barsize);
	void DrawOrder(CChartCtrl& chart, RealSeriesPtr* bs, BarSize barsize);
	static void OnLButtonDoubleClick(CPoint& point);

	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnDestroy();
	afx_msg void OnCbnSelendokComboPortfolio();
	afx_msg void OnNMClickListOrders(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNMDblclkListOrders(NMHDR* pNMHDR, LRESULT* pResult);
};
