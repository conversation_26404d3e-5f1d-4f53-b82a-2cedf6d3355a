#pragma once
#include <exprtk_base.hpp>

class ExpressionProcessor : public expression_processor<double>
{
public:
	ExpressionProcessor();
	~ExpressionProcessor();

private:
	void AddVariables();
	void UpdateVarValue();

	//contex
	std::vector<double> var_open;
	std::vector<double> var_high;
	std::vector<double> var_low;
	std::vector<double> var_close;
	std::vector<double> var_vwap;
	std::vector<double> var_volume;
	std::vector<double> var_amount;
	std::vector<double> var_banchmark_index_close;
	std::vector<double> var_banchmark_index_open;
	std::vector<double> var_return;
};

