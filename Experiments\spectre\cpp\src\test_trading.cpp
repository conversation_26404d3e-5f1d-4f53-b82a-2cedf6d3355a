/**
 * @file test_trading.cpp
 * @brief Unit tests for trading module
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Trading.h"
#include <iostream>
#include <cassert>
#include <cmath>
#include <chrono>

using namespace Spectre::Trading;

void test_position() {
    std::cout << "Testing Position class..." << std::endl;
    
    auto now = std::chrono::system_clock::now();
    
    // Test basic position creation
    Position pos(100, 50.0, 1.0, now);
    
    assert(pos.shares() == 100);
    assert(std::abs(pos.average_price() - 50.01) < 1e-6); // 50.0 + 1.0/100
    assert(std::abs(pos.last_price() - 50.0) < 1e-6);
    assert(std::abs(pos.realized() - 0.0) < 1e-6);
    
    // Test unrealized P&L
    pos.set_last_price(55.0);
    double expected_unrealized = (55.0 - 50.01) * 100;
    assert(std::abs(pos.unrealized() - expected_unrealized) < 1e-6);
    
    // Test position update (partial sell)
    auto [is_empty, realized] = pos.update(-50, 52.0, 0.5, now);
    assert(!is_empty);
    assert(pos.shares() == 50);
    
    // Test position close
    auto [is_empty2, realized2] = pos.update(-50, 53.0, 0.5, now);
    assert(is_empty2);
    assert(pos.shares() == 0);
    
    std::cout << "Position tests passed!" << std::endl;
}

void test_portfolio() {
    std::cout << "Testing Portfolio class..." << std::endl;
    
    Portfolio portfolio;
    auto now = std::chrono::system_clock::now();
    
    // Set initial cash
    portfolio.update_cash(10000.0, true);
    assert(std::abs(portfolio.cash() - 10000.0) < 1e-6);
    
    // Set datetime
    portfolio.set_datetime(now);
    
    // Add position
    double realized = portfolio.update("AAPL", 100, 150.0, 1.0);
    assert(std::abs(realized - 0.0) < 1e-6);
    assert(portfolio.shares("AAPL") == 100);
    assert(portfolio.has_position("AAPL"));
    
    // Update prices
    Portfolio::PriceMap prices = {{"AAPL", 155.0}};
    portfolio.update_value(prices);
    
    // Check portfolio value
    double expected_value = 10000.0 + 100 * 155.0; // Cash + position value
    assert(std::abs(portfolio.value() - expected_value) < 1e-6);
    
    // Test partial sell
    realized = portfolio.update("AAPL", -50, 160.0, 0.5);
    assert(portfolio.shares("AAPL") == 50);
    
    std::cout << "Portfolio tests passed!" << std::endl;
}

void test_stop_model() {
    std::cout << "Testing StopModel classes..." << std::endl;
    
    // Test basic stop model
    auto callback = []() { return true; };
    StopModel stop_model(0.1, callback); // 10% stop
    
    auto tracker = stop_model.new_tracker(100.0, false); // Long position
    assert(tracker != nullptr);
    
    // Price should not trigger stop yet
    tracker->update_price(105.0);
    assert(!tracker->check_trigger());
    
    // Price drop should trigger stop
    tracker->update_price(89.0); // Below 90 (100 * 1.1)
    // Note: This would trigger in a real scenario with proper callback
    
    // Test trailing stop model
    TrailingStopModel trailing_stop(-0.05, callback); // 5% trailing stop
    auto trailing_tracker = trailing_stop.new_tracker(100.0, false);
    
    trailing_tracker->update_price(110.0); // Price goes up
    trailing_tracker->update_price(105.0); // Price drops but not enough to trigger
    
    std::cout << "StopModel tests passed!" << std::endl;
}

void test_metrics() {
    std::cout << "Testing Metric functions..." << std::endl;
    
    // Create sample returns
    std::vector<double> returns_data = {0.01, -0.005, 0.02, -0.01, 0.015, -0.008, 0.012};
    auto returns = torch::from_blob(returns_data.data(), {static_cast<int64_t>(returns_data.size())}, 
                                   torch::kDouble).clone();
    
    // Test Sharpe ratio
    double sharpe = Metric::sharpe_ratio(returns, 0.02); // 2% risk-free rate
    assert(!std::isnan(sharpe));
    
    // Test annual volatility
    double vol = Metric::annual_volatility(returns);
    assert(vol > 0);
    
    // Test drawdown
    auto cumulative_returns = torch::cumsum(returns, 0);
    auto [dd, dd_duration] = Metric::drawdown(cumulative_returns);
    assert(dd.size(0) == returns.size(0));
    
    // Test comprehensive metrics
    auto metrics = Metric::calculate_performance_metrics(returns);
    assert(metrics.find("sharpe_ratio") != metrics.end());
    assert(metrics.find("annual_volatility") != metrics.end());
    assert(metrics.find("max_drawdown") != metrics.end());
    
    std::cout << "Metric tests passed!" << std::endl;
}

void test_event_system() {
    std::cout << "Testing Event system..." << std::endl;
    
    // Create event manager
    EventManager event_mgr;
    
    // Create event receiver
    class TestReceiver : public EventReceiver {
    public:
        bool event_fired = false;
        
        void on_run() override {
            // Schedule an Always event
            auto callback = [this](EventReceiver*) { this->event_fired = true; };
            schedule(std::make_shared<Always>(callback));
        }
    };
    
    auto receiver = std::make_shared<TestReceiver>();
    event_mgr.subscribe(receiver);
    
    // Simulate event processing (simplified)
    receiver->on_run();
    
    std::cout << "Event system tests passed!" << std::endl;
}

int main() {
    try {
        std::cout << "Starting Trading Module Tests..." << std::endl;
        
        test_position();
        test_portfolio();
        test_stop_model();
        test_metrics();
        test_event_system();
        
        std::cout << "All tests passed successfully!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
