#pragma once

#include <torch/torch.h>
#include <memory>
#include <vector>
#include <chrono>
#include <string>
#include <unordered_map>
#include <thread>
#include <future>

namespace Spectre {

// ============================================================================
// Performance Monitoring and Optimization
// ============================================================================

// Timer: Simple performance timing utility
class Timer {
public:
    Timer() : m_start(std::chrono::high_resolution_clock::now()) {}
    
    void reset() {
        m_start = std::chrono::high_resolution_clock::now();
    }
    
    double elapsed_ms() const {
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - m_start);
        return duration.count() / 1000.0;
    }
    
    double elapsed_seconds() const {
        return elapsed_ms() / 1000.0;
    }

private:
    std::chrono::high_resolution_clock::time_point m_start;
};

// ProfilerScope: RAII-style profiler for automatic timing
class ProfilerScope {
public:
    ProfilerScope(const std::string& name) : m_name(name), m_timer() {
        // Could integrate with external profiling tools here
    }
    
    ~ProfilerScope() {
        double elapsed = m_timer.elapsed_ms();
        // Log or store timing information
        // For now, just store in static map for later retrieval
        get_timings()[m_name] += elapsed;
    }
    
    static std::unordered_map<std::string, double>& get_timings() {
        static std::unordered_map<std::string, double> timings;
        return timings;
    }
    
    static void print_timings() {
        auto& timings = get_timings();
        for (const auto& pair : timings) {
            std::cout << pair.first << ": " << pair.second << " ms" << std::endl;
        }
    }
    
    static void clear_timings() {
        get_timings().clear();
    }

private:
    std::string m_name;
    Timer m_timer;
};

// Macro for easy profiling
#define PROFILE_SCOPE(name) ProfilerScope _prof_scope(name)

// ============================================================================
// Memory Management Utilities
// ============================================================================

// MemoryPool: Simple memory pool for tensor allocation
class MemoryPool {
public:
    static MemoryPool& instance() {
        static MemoryPool pool;
        return pool;
    }
    
    torch::Tensor get_tensor(const std::vector<int64_t>& sizes, torch::ScalarType dtype = torch::kFloat32) {
        // For now, just create new tensors
        // In a full implementation, this would reuse pre-allocated tensors
        return torch::empty(sizes, torch::TensorOptions().dtype(dtype));
    }
    
    void return_tensor(torch::Tensor& tensor) {
        // In a full implementation, this would return the tensor to the pool
        // For now, just clear the reference
        tensor = torch::Tensor();
    }
    
    size_t get_allocated_memory() const {
        // Return current memory usage
        // Note: LibTorch C++ API may not expose all CUDA memory functions
        return 0;  // Simplified implementation
    }

private:
    MemoryPool() = default;
};

// ============================================================================
// Parallel Processing Utilities
// ============================================================================

// ThreadPool: Simple thread pool for parallel factor computation
class ThreadPool {
public:
    ThreadPool(size_t num_threads = std::thread::hardware_concurrency()) 
        : m_num_threads(num_threads) {
        if (m_num_threads == 0) {
            m_num_threads = 1;
        }
    }
    
    template<typename Func, typename... Args>
    auto submit(Func&& func, Args&&... args) -> std::future<decltype(func(args...))> {
        auto task = std::make_shared<std::packaged_task<decltype(func(args...))()>>(
            std::bind(std::forward<Func>(func), std::forward<Args>(args)...)
        );
        
        auto future = task->get_future();
        
        // For simplicity, just execute immediately in current thread
        // In a full implementation, this would queue the task for worker threads
        (*task)();
        
        return future;
    }
    
    size_t get_thread_count() const {
        return m_num_threads;
    }

private:
    size_t m_num_threads;
};

// ParallelExecutor: Utility for parallel factor computation
class ParallelExecutor {
public:
    static ParallelExecutor& instance() {
        static ParallelExecutor executor;
        return executor;
    }
    
    // Execute function in parallel across tensor slices
    template<typename Func>
    torch::Tensor parallel_apply(const torch::Tensor& input, Func func, int64_t split_dim = 0) {
        PROFILE_SCOPE("parallel_apply");
        
        if (input.size(split_dim) <= 1 || m_thread_pool.get_thread_count() == 1) {
            // No benefit from parallelization
            return func(input);
        }
        
        // Split tensor into chunks
        int64_t chunk_size = std::max(1L, input.size(split_dim) / static_cast<int64_t>(m_thread_pool.get_thread_count()));
        std::vector<torch::Tensor> chunks;
        std::vector<std::future<torch::Tensor>> futures;
        
        for (int64_t start = 0; start < input.size(split_dim); start += chunk_size) {
            int64_t end = std::min(start + chunk_size, input.size(split_dim));
            torch::Tensor chunk = input.slice(split_dim, start, end);
            
            auto future = m_thread_pool.submit([func, chunk]() {
                return func(chunk);
            });
            
            futures.push_back(std::move(future));
        }
        
        // Collect results
        std::vector<torch::Tensor> results;
        for (auto& future : futures) {
            results.push_back(future.get());
        }
        
        // Concatenate results
        return torch::cat(results, split_dim);
    }
    
    // Parallel reduction operation
    template<typename Func, typename ReduceFunc>
    torch::Tensor parallel_reduce(const torch::Tensor& input, Func map_func, ReduceFunc reduce_func, int64_t split_dim = 0) {
        PROFILE_SCOPE("parallel_reduce");
        
        if (input.size(split_dim) <= 1 || m_thread_pool.get_thread_count() == 1) {
            return map_func(input);
        }
        
        // Apply map function in parallel
        torch::Tensor mapped = parallel_apply(input, map_func, split_dim);
        
        // Reduce results
        return reduce_func(mapped);
    }

private:
    ThreadPool m_thread_pool;
    
    ParallelExecutor() = default;
};

// ============================================================================
// GPU Acceleration Utilities
// ============================================================================

// GPUManager: Manage GPU resources and operations
class GPUManager {
public:
    static GPUManager& instance() {
        static GPUManager manager;
        return manager;
    }
    
    bool is_available() const {
        return torch::cuda::is_available();
    }
    
    int get_device_count() const {
        return torch::cuda::is_available() ? torch::cuda::device_count() : 0;
    }
    
    torch::Device get_default_device() const {
        return is_available() ? torch::Device(torch::kCUDA, 0) : torch::Device(torch::kCPU);
    }
    
    torch::Tensor to_gpu(const torch::Tensor& tensor, int device_id = 0) const {
        if (is_available() && tensor.device().is_cpu()) {
            return tensor.to(torch::Device(torch::kCUDA, device_id));
        }
        return tensor;
    }
    
    torch::Tensor to_cpu(const torch::Tensor& tensor) const {
        if (tensor.device().is_cuda()) {
            return tensor.to(torch::kCPU);
        }
        return tensor;
    }
    
    void synchronize() const {
        if (is_available()) {
            torch::cuda::synchronize();
        }
    }
    
    size_t get_memory_usage(int device_id = 0) const {
        // Simplified implementation
        return 0;
    }

    void clear_cache() const {
        // Simplified implementation - LibTorch C++ API may not expose this
        if (is_available()) {
            // torch::cuda::empty_cache(); // Not available in all LibTorch versions
        }
    }

private:
    GPUManager() = default;
};

// ============================================================================
// Batch Processing Utilities
// ============================================================================

// BatchProcessor: Process large datasets in batches
class BatchProcessor {
public:
    BatchProcessor(size_t batch_size = 1000) : m_batch_size(batch_size) {}
    
    template<typename Func>
    torch::Tensor process_batches(const torch::Tensor& input, Func func, int64_t batch_dim = 1) {
        PROFILE_SCOPE("batch_processing");
        
        if (input.size(batch_dim) <= static_cast<int64_t>(m_batch_size)) {
            return func(input);
        }
        
        std::vector<torch::Tensor> results;
        
        for (int64_t start = 0; start < input.size(batch_dim); start += m_batch_size) {
            int64_t end = std::min(start + static_cast<int64_t>(m_batch_size), input.size(batch_dim));
            torch::Tensor batch = input.slice(batch_dim, start, end);
            
            torch::Tensor result = func(batch);
            results.push_back(result);
        }
        
        return torch::cat(results, batch_dim);
    }
    
    void set_batch_size(size_t batch_size) {
        m_batch_size = batch_size;
    }
    
    size_t get_batch_size() const {
        return m_batch_size;
    }

private:
    size_t m_batch_size;
};

// ============================================================================
// Optimization Hints and Settings
// ============================================================================

// OptimizationSettings: Global optimization settings
class OptimizationSettings {
public:
    static OptimizationSettings& instance() {
        static OptimizationSettings settings;
        return settings;
    }
    
    void enable_gpu_acceleration(bool enable = true) {
        m_gpu_acceleration = enable && GPUManager::instance().is_available();
    }
    
    void set_parallel_threshold(size_t threshold) {
        m_parallel_threshold = threshold;
    }
    
    void set_batch_size(size_t batch_size) {
        m_batch_size = batch_size;
    }
    
    bool should_use_gpu() const {
        return m_gpu_acceleration;
    }
    
    bool should_use_parallel(size_t data_size) const {
        return data_size >= m_parallel_threshold;
    }
    
    size_t get_batch_size() const {
        return m_batch_size;
    }

private:
    bool m_gpu_acceleration = false;
    size_t m_parallel_threshold = 10000;
    size_t m_batch_size = 1000;
    
    OptimizationSettings() = default;
};

} // namespace Spectre
