#ifndef SYNCCLIENTIMPL_H
#define SYNCCLIENTIMPL_H

#include "HuobiSwap/RequestClient.h"
#include "HuobiSwap/RequestOptions.h"
#include "RestApiImpl.h"
#include "Utils/ApiSignature.h"

namespace HuobiSwap {

class SyncClientImpl : public RequestClient {
private:
  const char *accesssKey;
  const char *secretKey;
  RestApiImpl *impl;

public:
  void setProxy(const std::string &host) override;
  SyncClientImpl(const char *accesssKey, const char *secretKey);
  SyncClientImpl(const char *accesssKey, const char *secretKey,
                 RequestOptions &op);
  SyncClientImpl();
  SyncClientImpl(RequestOptions &op);
  std::vector<Account> getAccountInfo();
  std::vector<Account> getAccountInfo(std::string contractCode);
  std::vector<Position> getPositionInfo(std::string contractCode);
  std::vector<SymbolInfo> getSymbolInfo();
  std::vector<SymbolInfo> getSymbolInfo(std::string contractCode);
  std::vector<ContractInfo> getContractInfo();

  bool placeContractOrder(PlaceContractOrderRequest &request, bool cross=false);
  bool submitContractCancelOrder(const std::string& orderId, const std::string& symbol, bool cross=false);
  bool submitContractClientCancelOrder(const std::string& clientOrderId, const std::string& symbol, bool cross=false);
};

} // namespace HuobiSwap

#endif /* SYNCCLIENTIMPL_H */