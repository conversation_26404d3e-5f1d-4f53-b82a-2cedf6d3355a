﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9F59193F-CA70-31EC-95D0-C8717B9854AC}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>spectre_trading</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">spectre_trading.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">spectre_trading</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">spectre_trading.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">spectre_trading</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">spectre_trading.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">spectre_trading</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">spectre_trading.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">spectre_trading</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/BaseLibrary/libtorch/include" /external:I "E:/BaseLibrary/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/BaseLibrary/libtorch/include" /external:I "E:/BaseLibrary/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/BaseLibrary/libtorch/include" /external:I "E:/BaseLibrary/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/BaseLibrary/libtorch/include" /external:I "E:/BaseLibrary/libtorch/include/torch/csrc/api/include" /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;USE_DISTRIBUTED;USE_C10D_GLOO;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\spectre\cpp\include;E:\BaseLibrary\libtorch\include;E:\BaseLibrary\libtorch\include\torch\csrc\api\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/spectre/cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/spectre/cpp -BE:/lab/RoboQuant/src/Experiments/spectre/cpp/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/spectre/cpp/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkl.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\utils.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfig.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/spectre/cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/spectre/cpp -BE:/lab/RoboQuant/src/Experiments/spectre/cpp/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/spectre/cpp/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkl.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\utils.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfig.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/spectre/cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/spectre/cpp -BE:/lab/RoboQuant/src/Experiments/spectre/cpp/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/spectre/cpp/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkl.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\utils.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfig.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/spectre/cpp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/spectre/cpp -BE:/lab/RoboQuant/src/Experiments/spectre/cpp/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/spectre/cpp/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckCompilerFlag.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckFlagCommonConfig.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Config.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets-release.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\Caffe2Targets.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkl.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\mkldnn.cmake;E:\BaseLibrary\libtorch\share\cmake\Caffe2\public\utils.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfig.cmake;E:\BaseLibrary\libtorch\share\cmake\Torch\TorchConfigVersion.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Trading.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Position.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Portfolio.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Event.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\StopModel.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Metric.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\ZERO_CHECK.vcxproj">
      <Project>{FF0C6A13-1074-3F80-B450-531EC93F6D6D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>