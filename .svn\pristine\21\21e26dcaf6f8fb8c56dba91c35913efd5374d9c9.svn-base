#include <common.hpp>
#include <cstdio>
#include "pybind_api.h"
#include "core/db.h"
#include "core/logging.h"
//#include <DataHub.h>
#include <Instrument.h>
//#include <TradeSystem.h>
//#include <Strategy.h>
#include "data/data_api.h"
//#include "quant/quant_api.h"

namespace python {
	constexpr bool kPyBindFalse = false;

	namespace py = pybind11;

	//static dal::DataHub& gDh

	void addGlobalMethods(py::module& m) {
		m.attr("data_mode") = py::int_(0);
		m.def("get_data_mode", []() {return dal::dh().GetMode(); });

		auto initialize = [&]() {
			// Initialization of the module
#ifdef USE_NUMPY
			([]() -> void {
				// import_array1() forces a void return value.
				import_array1();
			})();
#endif // USE_NUMPY
			// Single threaded, so safe
			static bool initialized = false;
			if (initialized) {
				return;
			}
			// We will create a default workspace for us to run stuff.
			//switchWorkspaceInternal("default", true);
			//gCurrentWorkspaceName = "default";

			initialized = true;
		};

		initialize();
	}

	void addObjectMethods(py::module& m) {
		
		// DB
		py::class_<db::Transaction>(m, "Transaction")
			.def("put", &db::Transaction::Put)
			.def("delete", &db::Transaction::Delete)
			.def("commit", &db::Transaction::Commit);
		py::class_<db::Cursor>(m, "Cursor")
			.def("supports_seek", &db::Cursor::SupportsSeek)
			.def("seek_to_first", &db::Cursor::SeekToFirst)
			.def("seek", &db::Cursor::Seek)
			.def("next", &db::Cursor::Next)
			.def("key", [](db::Cursor* self) -> py::bytes { return self->key(); })
			.def("value", [](db::Cursor* self) -> py::bytes { return self->value(); })
			.def("valid", &db::Cursor::Valid);
		py::enum_<db::Mode>(m, "Mode")
			.value("read", db::Mode::READ)
			.value("write", db::Mode::WRITE)
			.value("new", db::Mode::NEW)
			.export_values();
		py::class_<db::DB /*, std::unique_ptr<DB>*/>(m, "DB")
			.def("new_transaction", &db::DB::NewTransaction)
			.def("new_cursor", &db::DB::NewCursor)
			.def("close", &db::DB::Close);
		m.def("create_db", &db::CreateDB);
		
		m.def("registered_dbs", []() {
			return db::Caffe2DBRegistry()->Keys();
		});

		//Data API
		py::enum_<BarData>(m, "BarData")
			.value("datetime", BarData::DateTime)
			.value("open", BarData::Open)
			.value("high", BarData::High)
			.value("low", BarData::Low)
			.value("close", BarData::Close)
			.value("volume", BarData::Volume)
			.value("amount", BarData::Amount)
			.value("typical", BarData::Typical)
			.value("preclose", BarData::PreClose);

		py::enum_<BarSize>(m, "BarSize")
			.value("tick", BarSize::tick)
			.value("min1", BarSize::min1)
			.value("min5", BarSize::min5)
			.value("min15", BarSize::min15)
			.value("min30", BarSize::min30)
			.value("min60", BarSize::min60)
			.value("mrange", BarSize::mrange)
			.value("day", BarSize::day)
			.value("drange", BarSize::drange)
			.value("week", BarSize::week)
			.value("month", BarSize::month)
			.value("quarter", BarSize::quarter)
			.value("year", BarSize::year);

		py::enum_<DoRight>(m, "DoRight")
			.value("none", DoRight::none)
			.value("forward", DoRight::forward)
			.value("backward", DoRight::backward);

		py::enum_<DataRunMode>(m, "RunMode")
			.value("active", DataRunMode::ACTIVE)
			.value("passive", DataRunMode::PASSIVE)
			.value("simulation", DataRunMode::SIMULATION);

		//DataSource API
		py::class_<dal::DataSource>(m, "DataSource")
			.def(py::init<DataRunMode>())
			.def("get_run_dir", &dal::DataSource::GetRunDir)
			.def("get_sec_num", &dal::DataSource::GetSecNum)
			.def("get_trading_dates", &dal::DataSource::GetTradingDates)
			.def("get_sec_name", &dal::DataSource::GetSecName)
			.def("get_all_fut_names", &dal::DataSource::GetAllFutNames)
			.def("get_all_fut_codes", &dal::DataSource::GetAllFutCodes)
			.def("get_fut_name_by_code", &dal::DataSource::GetFutNameByCode)
			.def("get_fut_code_by_name", &dal::DataSource::GetFutCodeByName)
			.def("get_fut_lx_label", &dal::DataSource::GetFutLxLabel)
			.def("get_block_data", &dal::DataSource::GetBlockData)
			.def("start_providers", &dal::DataSource::StartProviders)
			.def("stop_providers", &dal::DataSource::StopProviders)
			.def("price", &dal::DataSource::LastPrice)
			.def("get_history_data", &dal::DataSource::GetHistoryData,
				py::arg("label"),
				py::arg("length"),
				py::arg("bardata"),
				py::arg("barsize") = BarSize::day,
				py::arg("doright") = DoRight::forward)
			.def("get_history_data2", &dal::DataSource::GetHistoryData2,
				py::arg("label"),
				py::arg("begindate"),
				py::arg("enddate"),
				py::arg("bardata"),
				py::arg("barsize") = BarSize::day,
				py::arg("doright") = DoRight::forward)
			.def("get_bar_series", &dal::DataSource::GetBarSeries,
				py::arg("label"),
				py::arg("bardata"),
				py::arg("barsize") = BarSize::day)
			.def("get_rangebar_atr", &dal::DataSource::GetRangeBarAtr,
				py::arg("label"),
				py::arg("barsize"))
			.def("get_default_rangebar_atr", &dal::DataSource::GetDefaultRangeBarAtr,
				py::arg("label"),
				py::arg("barsize"),
				py::arg("islast") = true)
      .def("get_tick_data", &dal::DataSource::GetTickData,
        py::arg("label"),
				py::arg("daynum"))
			.def("get_stk_code_index", &dal::DataSource::GetStkCodeIndex,
				py::arg("label"))
			.def("get_fut_code_index", &dal::DataSource::GetFutCodeIndex,
				py::arg("label"));

		//Quant API
		//py::class_<bll::BackTest>(m, "Backtest")
		//	.def(py::init<>())
		//	.def("set_parameter", &bll::BackTest::SetParameter)
		//	.def("start", &bll::BackTest::Start)
		//	.def("stop", &bll::BackTest::Stop)
		//	.def("report", &bll::BackTest::GetReport);
	}

	PYBIND11_MODULE(qtunnel, m) {
		m.doc() = "pybind11 quant trading system interface to qtunnel";

		addGlobalMethods(m);
		addObjectMethods(m);
		//for (const auto& addition : PybindAdditionRegistry()->Keys()) {
		//	PybindAdditionRegistry()->Create(addition, m);
		//}
	}

} // python

