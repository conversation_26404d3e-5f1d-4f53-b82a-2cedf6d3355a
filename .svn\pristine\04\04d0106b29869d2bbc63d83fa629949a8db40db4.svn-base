﻿#pragma once
#include <vector>
#include <quicklist/QuickList.h>
#include <DataDef.h>
#include <Strategy.h>
// DlgInstrumentView 对话框
const int ivlistCols = 22;

class DlgInstrumentView : public CDialogEx
{
	DECLARE_DYNAMIC(DlgInstrumentView)

	struct ListText
	{
		char  txt[ivlistCols + 1][64];
		ListText()
		{
			memset(this, 0, sizeof(ListText));
		}
	};


public:
	DlgInstrumentView(CWnd* pParent = nullptr);   // 标准构造函数
	virtual ~DlgInstrumentView();
	void InitFilterResultList();
	void FillFilterResultList(BlockDataPtr blk_ptr = BlockDataPtr());
	void SortColumn();
	std::string GetPrevLabel();
	std::string GetNextLabel();

	std::vector<ListText> _listText;
	static char* _szHeaders[ivlistCols];
	static COLORREF _szMarkColor[6];
	CQuickList m_listFilterResult;
	static int nCurCol;
	static bool bFlag;
	std::vector<std::string> _whis;
  std::map<std::string, bll::BtRpt> _optiman_atrs;

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_DIALOG_INSTRUMENT };
#endif

protected:
	BlockDataPtr _blk_ptr;
	int m_nCurSel;
	int _labels_sel;
	CString m_sBindBlock;
	int m_nHistoryNum;
	int m_nApplyObject;
	int m_nPeriodType;

	double m_dThreshold1;
	double m_dThreshold2;
	double m_dL1;
	double m_dL2;
	double m_dL3;
	double m_dSpin;

	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
  void LoadSecRangeBarValue();
  void UpdateParameter();
  double GetSpinValue();
  void UpdateCombox();
  double Predict(dal::InstrumentPtr itm_ptr, const std::string& model_id);

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg LRESULT OnGetListItem(WPARAM wParam, LPARAM lParam);
	afx_msg void OnLvnColumnclickListSec(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNMClickListFilterResult(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNMDblclkListFilterResult(NMHDR* pNMHDR, LRESULT* pResult);
  afx_msg void OnCbnSelendokComboBindBlock();
  afx_msg void OnBnClickedRadioShort();
  afx_msg void OnBnClickedRadioLong();
  afx_msg void OnDeltaposSpinThreshold1(NMHDR* pNMHDR, LRESULT* pResult);
  afx_msg void OnDeltaposSpinThreshold2(NMHDR* pNMHDR, LRESULT* pResult);
  afx_msg void OnDeltaposSpinL1(NMHDR* pNMHDR, LRESULT* pResult);
  afx_msg void OnDeltaposSpinL2(NMHDR* pNMHDR, LRESULT* pResult);
  afx_msg void OnDeltaposSpinL3(NMHDR* pNMHDR, LRESULT* pResult);
  afx_msg void OnBnClickedButtonSaveOption();
  afx_msg void OnBnClickedButtonTestRange();
  afx_msg void OnBnClickedRadioFuture();
  afx_msg void OnBnClickedRadioStock();
  afx_msg void OnBnClickedButtonImportRange();
private:
	CString m_sModelName;
	std::string _long_model;
	std::string _short_model;
public:
	afx_msg void OnCbnSelendokComboModel();
};
