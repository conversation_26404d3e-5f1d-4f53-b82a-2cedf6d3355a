/// @file
/// <AUTHOR> <<EMAIL>>
/// [GitHub Repository](https://github.com/dpilger26/NumCpp)
/// @version 2.0.0
///
/// @section License
/// Copyright 2020 David <PERSON>
///
/// Permission is hereby granted, free of charge, to any person obtaining a copy of this
/// software and associated documentation files(the "Software"), to deal in the Software
/// without restriction, including without limitation the rights to use, copy, modify,
/// merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
/// permit persons to whom the Software is furnished to do so, subject to the following
/// conditions :
///
/// The above copyright notice and this permission notice shall be included in all copies
/// or substantial portions of the Software.
///
/// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
/// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
/// PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE
/// FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
/// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
/// DEALINGS IN THE SOFTWARE.
///
/// @section Description
/// Draw samples from a uniform distribution.
///
#pragma once

#include "NumCpp/Core/DtypeInfo.hpp"
#include "NumCpp/Core/Internal/StaticAsserts.hpp"
#include "NumCpp/Random/randFloat.hpp"

namespace nc
{
    namespace random
    {
        //============================================================================
        // Method Description:
        ///						Draw sample from a uniform distribution.
        ///
        ///						Samples are uniformly distributed over the half -
        ///						open interval[low, high) (includes low, but excludes high)
        ///
        ///                     NumPy Reference: https://docs.scipy.org/doc/numpy/reference/generated/numpy.random.uniform.html#numpy.random.uniform
        ///
        /// @param				inLow
        /// @param				inHigh
        /// @return
        ///				NdArray
        ///
        template<typename dtype>
        dtype uniform(dtype inLow, dtype inHigh)
        {
            STATIC_ASSERT_FLOAT(dtype);

            return randFloat(inLow, inHigh);
        }

        //============================================================================
        // Method Description:
        ///						Draw samples from a uniform distribution.
        ///
        ///						Samples are uniformly distributed over the half -
        ///						open interval[low, high) (includes low, but excludes high)
        ///
        ///                     NumPy Reference: https://docs.scipy.org/doc/numpy/reference/generated/numpy.random.uniform.html#numpy.random.uniform
        ///
        /// @param				inShape
        /// @param				inLow
        /// @param				inHigh
        /// @return
        ///				NdArray
        ///
        template<typename dtype>
        NdArray<dtype> uniform(const Shape& inShape, dtype inLow, dtype inHigh)
        {
            STATIC_ASSERT_FLOAT(dtype);

            return randFloat(inShape, inLow, inHigh);
        }
    }
}
