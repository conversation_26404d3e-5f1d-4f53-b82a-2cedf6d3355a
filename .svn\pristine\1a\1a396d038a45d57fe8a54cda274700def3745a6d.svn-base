# This python snippet is appended to the python module generated by SWIG
# customizing and extending its functionality


import re

# For ta_common

# TA_RetCodeInfo constructor can accept optional parameter

TA_RetCodeInfo.swig_init = TA_RetCodeInfo.__init__

def _temp_(self, code = 0):
    TA_RetCodeInfo.swig_init(self)
    TA_SetRetCodeInfo(code, self)

TA_RetCodeInfo.__init__ = _temp_




# parameter TA_Initialize is optional
# TA_Initialize protects itself against multiple calls

_initialized = False

def TA_Initialize():
    global _initialized
    if ( _initialized ):
        retCode = TA_Shutdown()
        if ( retCode != TA_SUCCESS ):
            return retCode

    retCode = _TaLib.TA_Initialize()
    _initialized = (retCode == TA_SUCCESS)
    return retCode



def TA_Shutdown():
    global _initialized
    if ( _initialized ):
        _initialized = False
        return _TaLib.TA_Shutdown()
    else:
        # We are more forgiving on multiple calls to TA_Shutdown
        # than TA-LIB on TA_Shutdown()
        return TA_SUCCESS




# A way to automatically initialize and shutdown

class TA_Initialization:
    def __init__( self ):
        TA_Initialize()

    def __del__(self):
        TA_Shutdown()

# a singleton object
_initialization_object = TA_Initialization()
