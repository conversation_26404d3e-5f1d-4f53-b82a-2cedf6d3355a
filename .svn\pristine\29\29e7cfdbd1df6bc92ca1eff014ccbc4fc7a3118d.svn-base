#pragma once
#include <string>
#include <list>
#include <DataBase.h>
#include "../Utils/AdoDb.h"
#include "DataHubI.h"
#include <PortfolioManager.h>
#include <TradeSystem.h>
#include <patterns/singleton.hpp>

namespace dal
{
using namespace bll;
class DbAccess : public utils::AdoDb, public utils::Singleton<DbAccess>
{
	friend class utils::Singleton<DbAccess>;
private:
	DbAccess();

public:
	~DbAccess(void);

	bool Init(DataHubI* pDataHub, const std::string& sConnectString );

	bool ReadCashFlowData(std::list<CashFlowData>& tds, int order = 0);
	bool AddCashFlowData(CashFlowData& td);

	bool AddStrategyData(const StrategyData& strategy);
	bool UpdateStrategyData(const StrategyData& strategy);
	bool ReadStrategyData(const std::string& label, StrategyData* pStrategy);

	bool AddTradeProcedureData(const TradeProcedure& tp);
	bool UpdateTradeProcedureData(const TradeProcedure& tp);
	bool ReadTradeProcedureData(const std::string& label, TradeProcedure* pTp);

private:
	std::string _sConnectString;
	DataHubI* _pDataHub;
};
}