#include <iostream>
#include <torch/torch.h>
#include "Parallel.h"

int main() {
    std::cout << "??????? Parallel ???..." << std::endl;
    
    try {
        // ?????????????
        torch::Tensor data = torch::tensor({{1.0, 2.0, std::numeric_limits<double>::quiet_NaN(), 4.0},
                                           {5.0, std::numeric_limits<double>::quiet_NaN(), 7.0, 8.0}});
        
        std::cout << "????????:\n" << data << std::endl;
        
        // ???? nanmean
        auto mean_result = Spectre::nanmean(data, 1);
        std::cout << "nanmean ???:\n" << mean_result << std::endl;
        
        // ???? nansum
        auto sum_result = Spectre::nansum(data, 1);
        std::cout << "nansum ???:\n" << sum_result << std::endl;
        
        // ???? DeviceConstant
        auto device_const = Spectre::DeviceConstant::get(torch::kCPU);
        auto linspace_result = device_const->linspace(5, torch::kFloat);
        std::cout << "DeviceConstant linspace ???:\n" << linspace_result << std::endl;
        
        // Test ParallelGroupBy
        std::cout << "\nTesting ParallelGroupBy..." << std::endl;
        torch::Tensor keys = torch::tensor({0, 1, 0, 1, 0});
        torch::Tensor group_data = torch::randn({5, 3});
        std::cout << "Group keys: " << keys << std::endl;
        std::cout << "Group data:\n" << group_data << std::endl;

        Spectre::ParallelGroupBy groupby(keys);
        auto split_data = groupby.split(group_data);
        auto reverted_data = groupby.revert(split_data);
        std::cout << "Split data:\n" << split_data << std::endl;
        std::cout << "Reverted data:\n" << reverted_data << std::endl;

        // Verify restoration is correct
        torch::Tensor diff = torch::abs(group_data - reverted_data);
        std::cout << "Max difference: " << diff.max().item<double>() << std::endl;

        std::cout << "All tests completed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "????: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
