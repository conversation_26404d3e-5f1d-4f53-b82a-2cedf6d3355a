#pragma once
#include <ISeries.h>
#include <Indicator.h>
#include <BarSeries.h>

namespace dal {
	class BarSeriesI : public /*DoubleSeries, */BarSeries
	{
	public:
		BarSeriesI(void);
		BarSeriesI(HistoryDataSeqPtr hds_ptr, BarSize barsize);
		virtual ~BarSeriesI(void);

		HistoryData First;	//  Gets the first bar of this series 
		HistoryData Last;	//  Gets the last bar of this series 
		int FirstIndex;
		int LastIndex;
		BarSize barsize;

		double Ago(int n);			//  Returns n-item-ago value 
		double Ago(int n, BarData barData);			//  Returns n-item-ago value 
		bool Contains(ExtDate date);	//  Checks if this series contains a value with specified time stamp 
		ExtDate GetDateTime(int index);	//  Returns date time by specified index 
		int GetIndex(ExtDate date);	//  Returns index by specified date time 
		int FromTimeBars(ExtDate date);

		double Item(ExtDate date, BarData barData);
		double Item(int index, BarData barData);

		HistoryData& Item(int index) {
			boost::unique_lock<boost::shared_mutex> lock(*_bar_mutex);
			assert(index >= 0 && index < _bars.size());
			return _bars.at(index);
		}
		HistoryData& Item(ExtDate date) {
			int idx = GetIndex(date);
			return Item(idx);
		}
		/*
		BarSeries Compress(unsigned long newBarSize);
		void Add(HistBar bar);
		void Add(HistBar::BarType barType, unsigned long size, DATE beginTime, DATE endTime, double open, double high, double low, double close, unsigned long volume, unsigned long openInt);
		void Add(DATE datetime, double open, double high, double low, double close, unsigned long volume, unsigned long size);

		void Remove(DATE dateTime);
		void Remove(int index);
		*/
		Cross Crosses(double data);
		bool CrossesAbove(double data);
		bool CrossesBelow(double data);

		double HighestHigh();
		double HighestHigh(ExtDate dateTime1, ExtDate dateTime2);
		double HighestHigh(int nBars);
		double HighestHigh(int index1, int index2);

		double LowestLow();
		double LowestLow(ExtDate dateTime1, ExtDate dateTime2);
		double LowestLow(int nBars);
		double LowestLow(int index1, int index2);


		RealSeriesPtr GetBarDataSeries(BarData barData);
		int GetBarDataHLCVSeries(RealSeriesPtr highs, RealSeriesPtr lows, RealSeriesPtr closes, RealSeriesPtr volumes);

		HistoryDataSeq& GetBars() {
			return _bars;
		}

		size_t Size() {
			//boost::shared_lock<boost::shared_mutex> lock(*_bar_mutex);
			return _bars.size();
		}

		HistoryDataSeq* RefHistoryDataSeqPtr();

		void UpdateNewQuoteData(QuoteData* qd_ptr, BarSize barsize);
		void Resize(size_t len);
		void Reserve(size_t len);
		void Reset(HistoryDataSeqPtr hds_ptr, double range = 0.0);
		void Reset(HistoryDataSeq* hds_ptr, double range = 0.0);
		HistoryDataSeqPtr GetHistoryData();
		boost::shared_ptr<boost::shared_mutex> GetMutex() { return _bar_mutex; }
		void Clear();
		double GetRange() { return range; }
		void SetRange(double range) { this->range = range; }

		//BarSeries();
		// 	BarSeries(std::string name, std::string title);
		// 	BarSeries(std::string name);
		//HistBar Item(int i);
		//HistBar Item(DateTime DateTime);
		// 	HistBar Item(DateTime DateTime, EIndexOption Option);
		// 	double Item(int Index, int BarData);
		//double Item(int Index, BarData BarData);
		// 	virtual BarSeries Clone();
		// 	virtual BarSeries Clone(int Index1, int Index2);
		// 	virtual BarSeries Clone(DateTime DateTime1, DateTime DateTime2);
		// 	bool Contains(HistBar HistBar);
		// 	void Add(HistBar bar);
		// 	void Remove(HistBar HistBar);
		// 	BarSeries Shift(int offset);
		//HistBar Ago(int n);
		// 	DoubleSeries GetArray(BarData BarData);
		// 	DoubleSeries GetArray(int Index1, int Index2, BarData BarData);
		// 	DoubleSeries GetArray(DateTime DateTime1, DateTime DateTime2, BarData BarData);
		// 	DoubleSeries GetHighSeries();
		// 	DoubleSeries GetLowSeries();
		// 	DoubleSeries GetOpenSeries();
		// 	DoubleSeries GetCloseSeries();
		// 	DoubleSeries GetVolumeSeries();
		// 	DoubleSeries GetOpenIntSeries();
		// 	DoubleSeries GetHighSeries(DateTime date1, DateTime date2);
		// 	DoubleSeries GetLowSeries(DateTime date1, DateTime date2);
		// 	DoubleSeries GetOpenSeries(DateTime date1, DateTime date2);
		// 	DoubleSeries GetCloseSeries(DateTime date1, DateTime date2);
		// 	DoubleSeries GetVolumeSeries(DateTime date1, DateTime date2);
		// 	DoubleSeries GetOpenIntSeries(DateTime date1, DateTime date2);
		// 	HistBar HighestHighBar(int Index1, int Index2);
		// 	HistBar HighestHighBar(DateTime DateTime1, DateTime DateTime2);
		// 	HistBar HighestHighBar();
		// 	HistBar LowestLowBar(int Index1, int Index2);
		// 	HistBar LowestLowBar(DateTime DateTime1, DateTime DateTime2);
		// 	HistBar LowestLowBar(int nBars);
		// 	HistBar HighestHighBar(int nBars);
		// 	HistBar LowestLowBar();
		// 	double HighestHigh(int index1, int index2);
		// 	double LowestLow(int index1, int index2);
		// 	double HighestHigh(DateTime dateTime1, DateTime dateTime2);
		// 	double LowestLow(DateTime dateTime1, DateTime dateTime2);
		// 	double HighestHigh(int nBars);
		// 	double LowestLow(int nBars);
		// 	double HighestHigh();
		// 	double LowestLow();
		// 	virtual double GetMin(int index1, int index2);
		// 	virtual double GetMax(int index1, int index2);
		// 	virtual double GetMin();
		// 	virtual double GetMax();
		// 	double CloseD(DateTime dateTime);
		// 	double CloseW(DateTime dateTime);
		// 	double CloseM(DateTime dateTime);
		// 	double CloseY(DateTime dateTime);
		// 	double CloseD(int index, int daysAgo);
		// 	double CloseW(int index, int weeksAgo);
		// 	double CloseM(int index, int monthsAgo);
		// 	double CloseY(int index, int yearsAgo);
		// 	double CloseD(DateTime dateTime, int daysAgo);
		// 	double CloseW(DateTime dateTime, int weeksAgo);
		// 	double CloseM(DateTime dateTime, int monthsAgo);
		// 	double CloseY(DateTime dateTime, int yearsAgo);
		// 	double CloseD(HistBar bar, int daysAgo);
		// 	double CloseW(HistBar bar, int weeksAgo);
		// 	double CloseM(HistBar bar, int monthsAgo);
		// 	double CloseY(HistBar bar, int yearsAgo);
		// 	double CloseD(HistBar bar);
		// 	double CloseW(HistBar bar);
		// 	double CloseY(HistBar bar);
		// 	double OpenD(DateTime dateTime);
		// 	double OpenW(DateTime dateTime);
		// 	double OpenM(DateTime dateTime);
		// 	double OpenY(DateTime dateTime);
		// 	double OpenD(int index, int daysAgo);
		// 	double OpenW(int index, int weeksAgo);
		// 	double OpenM(int index, int monthsAgo);
		// 	double OpenY(int index, int yearsAgo);
		// 	double OpenD(DateTime dateTime, int daysAgo);
		// 	double OpenW(DateTime dateTime, int weeksAgo);
		// 	double OpenM(DateTime dateTime, int monthsAgo);
		// 	double OpenY(DateTime dateTime, int yearsAgo);
		// 	double OpenD(HistBar bar, int daysAgo);
		// 	double OpenW(HistBar bar, int weeksAgo);
		// 	double OpenM(HistBar bar, int monthsAgo);
		// 	double OpenY(HistBar bar, int yearsAgo);
		// 	double OpenD(HistBar bar);
		// 	double OpenW(HistBar bar);
		// 	double OpenM(HistBar bar);
		// 	double OpenY(HistBar bar);
		// 	double HighD(DateTime dateTime);
		// 	double HighW(DateTime dateTime);
		// 	double HighM(DateTime dateTime);
		// 	double HighY(DateTime dateTime);
		// 	double HighD(int index, int daysAgo);
		// 	double HighW(int index, int weeksAgo);
		// 	double HighM(int index, int monthsAgo);
		// 	double HighY(int index, int yearsAgo);
		// 	double HighD(DateTime dateTime, int daysAgo);
		// 	double HighW(DateTime dateTime, int weeksAgo);
		// 	double HighM(DateTime dateTime, int monthsAgo);
		// 	double HighY(DateTime dateTime, int yearsAgo);
		// 	double HighD(HistBar bar, int daysAgo);
		// 	double HighW(HistBar bar, int weeksAgo);
		// 	double HighM(HistBar bar, int monthsAgo);
		// 	double HighY(HistBar bar, int yearsAgo);
		// 	double HighD(HistBar bar);
		// 	double HighW(HistBar bar);
		// 	double HighM(HistBar bar);
		// 	double HighY(HistBar bar);
		// 	double LowD(DateTime dateTime);
		// 	double LowW(DateTime dateTime);
		// 	double LowM(DateTime dateTime);
		// 	double LowY(DateTime dateTime);
		// 	double LowD(int index, int daysAgo);
		// 	double LowW(int index, int weeksAgo);
		// 	double LowM(int index, int monthsAgo);
		// 	double LowY(int index, int yearsAgo);
		// 	double LowD(DateTime dateTime, int daysAgo);
		// 	double LowW(DateTime dateTime, int weeksAgo);
		// 	double LowM(DateTime dateTime, int monthsAgo);
		// 	double LowY(DateTime dateTime, int yearsAgo);
		// 	double LowD(HistBar bar, int daysAgo);
		// 	double LowW(HistBar bar, int weeksAgo);
		// 	double LowM(HistBar bar, int monthsAgo);
		// 	double LowY(HistBar bar, int yearsAgo);
		// 	double LowD(HistBar bar);
		// 	double LowW(HistBar bar);
		// 	double LowM(HistBar bar);
		// 	double LowY(HistBar bar);
		// 	virtual BarSeries Compress(long NewBarSize);
		// 	virtual BarSeries Compress(long newBarSize, TimeSpan sessionStart, TimeSpan sessionEnd);
		// 	virtual BarSeries Compress(long newBarSize, TimeSpan sessionStart, TimeSpan sessionEnd, BarSlycing mode);
		// 	virtual BarSeries Compress(long newBarSize, TimeSpan sessionStart, TimeSpan sessionEnd, BarSlycing mode, bool weeklySlicingEnabled, bool clubWithPrevBar);


	private:
		inline double get_item(HistoryData& bar, BarData barData);
		void UpdatePriceToRangeBar(HistoryDataSeq* hdes_ptr, ExtDate hdate, float price);
		HistoryDataSeqPtr ConverttoRangeBar(HistoryDataSeq* src_ptr);
		double range;

		boost::shared_ptr<boost::shared_mutex> _bar_mutex;
		HistoryDataSeq _bars;
	};
}
