#pragma once

/**
 * @file Portfolio.h
 * @brief Portfolio class for managing multiple asset positions
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "Position.h"
#include "StopModel.h"
#include <torch/torch.h>
#include <unordered_map>
#include <vector>
#include <functional>
#include <chrono>
#include <memory>

namespace Spectre {
namespace Trading {

/**
 * @brief Manages a portfolio of multiple asset positions
 * 
 * This class handles:
 * - Multiple asset positions
 * - Cash management
 * - Portfolio-level P&L tracking
 * - Historical performance records
 * - Stop loss management across positions
 * - Corporate actions processing
 */
class Portfolio {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using AssetId = std::string;
    using PriceFunction = std::function<double(const AssetId&)>;
    using PriceMap = std::unordered_map<AssetId, double>;

    /**
     * @brief Portfolio history record structure
     */
    struct HistoryRecord {
        TimePoint timestamp;
        double cash;
        std::unordered_map<AssetId, double> asset_values;
        std::unordered_map<AssetId, double> asset_shares;
        std::unordered_map<AssetId, double> average_prices;
        
        double total_value() const;
    };

    /**
     * @brief Fund change record for tracking deposits/withdrawals
     */
    struct FundChange {
        TimePoint timestamp;
        double amount;
    };

    /**
     * @brief Constructor
     * @param stop_model Default stop model for all positions
     */
    explicit Portfolio(std::shared_ptr<StopModel> stop_model = nullptr);

    // Destructor
    ~Portfolio() = default;

    // Copy and move operations
    Portfolio(const Portfolio& other);
    Portfolio& operator=(const Portfolio& other);
    Portfolio(Portfolio&& other) noexcept;
    Portfolio& operator=(Portfolio&& other) noexcept;

    // Getters
    const std::unordered_map<AssetId, Position>& positions() const { return positions_; }
    double cash() const { return cash_; }
    double value() const;
    double leverage() const;
    TimePoint current_dt() const { return current_dt_; }
    
    /**
     * @brief Get historical portfolio records
     * @return Vector of historical records
     */
    const std::vector<HistoryRecord>& history() const { return history_; }
    
    /**
     * @brief Get fund change history
     * @return Vector of fund changes
     */
    const std::vector<FundChange>& fund_history() const { return funds_change_; }
    
    /**
     * @brief Calculate portfolio returns
     * @return Tensor of daily returns
     */
    torch::Tensor returns() const;

    // Position management
    /**
     * @brief Get shares for a specific asset
     * @param asset Asset identifier
     * @return Number of shares (0 if no position)
     */
    int64_t shares(const AssetId& asset) const;

    /**
     * @brief Check if position exists for asset
     * @param asset Asset identifier
     * @return True if position exists
     */
    bool has_position(const AssetId& asset) const;

    // Portfolio operations
    /**
     * @brief Set current datetime and update history
     * @param dt New datetime
     */
    void set_datetime(const TimePoint& dt);

    /**
     * @brief Update position for an asset
     * @param asset Asset identifier
     * @param amount Share amount to add/remove
     * @param fill_price Fill price for the trade
     * @param commission Commission for the trade
     * @return Realized P&L from the trade
     */
    double update(const AssetId& asset, int64_t amount, double fill_price, double commission);

    /**
     * @brief Update cash balance
     * @param amount Amount to add/remove
     * @param is_funds Whether this is a fund transfer (deposit/withdrawal)
     */
    void update_cash(double amount, bool is_funds = false);

    /**
     * @brief Update all position values with current prices
     * @param prices Price function or price map
     */
    void update_value(const PriceFunction& prices);
    void update_value(const PriceMap& prices);

    // Corporate actions
    /**
     * @brief Process stock split for an asset
     * @param asset Asset identifier
     * @param inverse_ratio Split ratio
     * @param last_price Price after split
     */
    void process_split(const AssetId& asset, double inverse_ratio, double last_price);

    /**
     * @brief Process dividend payment for an asset
     * @param asset Asset identifier
     * @param amount Dividend per share
     * @param tax Tax rate
     */
    void process_dividend(const AssetId& asset, double amount, double tax);

    /**
     * @brief Process borrowing interest for short positions and negative cash
     * @param day_passed Number of days passed
     * @param money_interest_rate Interest rate for cash borrowing
     * @param stock_interest_rate Interest rate for stock borrowing
     */
    void process_borrow_interest(double day_passed, double money_interest_rate, 
                                double stock_interest_rate);

    // Stop loss management
    /**
     * @brief Set default stop model for new positions
     * @param stop_model Stop model to use
     */
    void set_stop_model(std::shared_ptr<StopModel> stop_model);

    /**
     * @brief Check stop triggers for all positions
     * @param args Arguments to pass to stop models
     * @return Vector of stop trigger results
     */
    template<typename... Args>
    std::vector<bool> check_stop_trigger(Args&&... args);

    /**
     * @brief Clear all positions and reset portfolio
     */
    void clear();

private:
    // Portfolio state
    std::unordered_map<AssetId, Position> positions_;
    double cash_;
    TimePoint current_dt_;
    std::shared_ptr<StopModel> stop_model_;

    // History tracking
    std::vector<HistoryRecord> history_;
    std::vector<FundChange> funds_change_;

    // Helper methods
    HistoryRecord get_today_record() const;
    void update_value_function(const PriceFunction& prices);
    void update_value_map(const PriceMap& prices);
};

} // namespace Trading
} // namespace Spectre
