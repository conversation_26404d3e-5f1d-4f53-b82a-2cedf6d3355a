#pragma once
#include <public.h>
#include <ExecutionReport.h>
using namespace Executions;

namespace Services
{
	enum ServiceErrorType
	{
		SET_Message,
		SET_Warning,
		SET_Error
	};

	enum ServiceStatus
	{
		Stopped = 0,
		Started = 1,
	};

	class ServiceId
	{
	public:
		const BYTE ExecutionSimulator = 1;
		const BYTE FIX = 10;
		ServiceId()
		{
		}
	};

	class IService
	{
	public:
		EventHandler StatusChangedEvent;
		//ServiceErrorEventHandler ErrorEvent;
		BYTE id;
		std::string name;
		std::string description;
		ServiceStatus status;
		virtual void Start() = 0;
		virtual void Stop() = 0;
	};

	typedef boost::shared_ptr<IService> IServicePtr;

	class IMarketService : IService
	{
		//FIXLogonEventHandler Logon;
		//FIXLogoutEventHandler Logout;
		//virtual void Send(Logon message) = 0;
		//virtual void Send(Logout message) = 0;
	};
	typedef boost::shared_ptr<IMarketService> IMarketServicePtr;

	class IExecutionService : IService, IMarketService
	{
		//FIXNewOrderSingleEventHandler NewOrderSingle;
		//FIXOrderCancelRequestEventHandler OrderCancelRequest;
		//FIXOrderCancelReplaceRequestEventHandler OrderCancelReplaceRequest;
		virtual void Send(ExecutionReport message) = 0;
		virtual void Send(OrderCancelReject message) = 0;
	};
	typedef boost::shared_ptr<IExecutionService> IExecutionServicePtr;
}