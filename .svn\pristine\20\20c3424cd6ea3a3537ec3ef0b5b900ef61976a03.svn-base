// Microsoft Visual C++ generated resource script.
//
#pragma code_page(65001)

#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#ifndef APSTUDIO_INVOKED
#include "targetver.h"
#endif
#include "afxres.h"
#include "verrsrc.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// 中文(简体，中国) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#ifndef APSTUDIO_INVOKED\r\n"
    "#include ""targetver.h""\r\n"
    "#endif\r\n"
    "#include ""afxres.h""\r\n"
    "#include ""verrsrc.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)\r\n"
    "LANGUAGE 4, 2\r\n"
    "#include ""res\\LabTools.rc2""  // 闂?Microsoft Visual C++ 缂傚倸鍊搁崐鎼佸磹瑜版帗鍋嬮柣鎰仛椤愯姤銇勯幇鍫曟闁稿骸绉归弻娑㈠即閵娿儲鐏撻梺闈涚墱閸嬪懐鎹?\n"
    "#include ""afxres.rc""      // 闂傚倷绀侀幖顐ょ矓閺夋嚚娲Χ婢跺﹪妫峰銈嗙墬缁海澹曟總鍛婄厱闁哄洢鍔嬬花鍏笺亜?\n"
    "#if !defined(_AFXDLL)\r\n"
    "#include ""afxribbon.rc""   // MFC 闂傚倷绀侀幉鈥愁潖婵犳艾绐楅柡鍥ュ灩缁€鍌涙叏濡炶浜鹃悗瑙勬礈婵炩偓鐎规洦鍋婂畷鐔碱敆閳ь剟顢旈鐐粹拺缁绢厼鎳愰悞浠嬫煕閹扳晛濡块柛姗嗗墴濮婃椽宕崟鍨紖闂侀潧鐗嗗ú銏ゅ磻閹烘垟鏀?\n"
    "#endif\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON                    "res\\LabTools.ico"


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOGEX 0, 0, 170, 62
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "关于 LabTools"
FONT 9, "MS Shell Dlg", 0, 0, 0x1
BEGIN
    ICON            IDR_MAINFRAME,IDC_STATIC,14,14,21,20
    LTEXT           "LabTools，版本 1.0",IDC_STATIC,42,14,114,8,SS_NOPREFIX
    LTEXT           "版权所有 (C) 2018",IDC_STATIC,42,26,114,8
    DEFPUSHBUTTON   "确定",IDOK,113,41,50,14,WS_GROUP
END

IDD_LABTOOLS_DIALOG DIALOGEX 0, 0, 924, 631
STYLE DS_SETFONT | DS_MODALFRAME | WS_MINIMIZEBOX | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "QuantLab V3.0"
FONT 9, "新宋体", 400, 0, 0x86
BEGIN
    CONTROL         "",IDC_TAB_PAGES,"SysTabControl32",0x0,153,380,763,243
    EDITTEXT        IDC_EDIT_SHORT_STEP_NUMBER,633,358,36,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_UNITS,726,358,40,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_LONG_STARTING,265,358,36,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_LONG_STEP_SIZE,331,358,36,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_LONG_STEP_NUMBER,399,358,36,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_SHORT_STARTING,498,358,36,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_SHORT_STEP_SIZE,565,358,36,14,ES_AUTOHSCROLL
    PUSHBUTTON      "生成特征值",IDC_BUTTON_GENERATE_FEATURES,732,295,57,14
    COMBOBOX        IDC_COMBO_LONG_STRATEGY,610,319,110,251,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDC_COMBO_SHORT_STRATEGY,610,336,110,251,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "导入参数..",IDC_BUTTON_IMPORT_RANGEBAR,801,295,50,14
    PUSHBUTTON      "保存参数",IDC_BUTTON_SAVE_PARAMETER,864,315,50,14
    PUSHBUTTON      "回测",IDC_BUTTON_BACKTEST,864,295,50,14
    CONTROL         "",IDC_TREE_FILTER,"SysTreeView32",WS_BORDER | WS_HSCROLL | WS_TABSTOP,7,7,139,616
    EDITTEXT        IDC_EDIT_NAME,183,18,98,12,ES_AUTOHSCROLL
    LISTBOX         IDC_LIST_CATEGORY,155,48,63,212,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    LISTBOX         IDC_LIST_PARAMETER,224,48,101,212,LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDC_COMBO_USES,319,20,31,62,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDC_COMBO_BAR_SIZE,466,20,51,198,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    CONTROL         "",IDC_TAB_EXPR,"SysTabControl32",0x0,331,34,585,228
    CONTROL         "上证A股",IDC_CHECK_SHA,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,203,268,40,10
    CONTROL         "深证A股",IDC_CHECK_SZA,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,249,268,40,10
    CONTROL         "中小板",IDC_CHECK_SZZXB,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,295,267,33,11
    CONTROL         "创业板",IDC_CHECK_SZCYB,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,334,267,33,11
    CONTROL         "基金",IDC_CHECK_FUND,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,412,268,25,10
    CONTROL         "期货",IDC_CHECK_ETF,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,443,268,25,10
    CONTROL         "EX",IDC_CHECK_EXT_MARKET,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,474,268,21,10
    EDITTEXT        IDC_EDIT_BIND_LABEL,207,313,78,14,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_BIND_BLOCK,207,332,79,262,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_EDIT_N_DAY,368,318,33,14,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_DATE_RANGE,407,318,92,262,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    CONTROL         "",IDC_DATETIME_START,"SysDateTimePick32",DTS_RIGHTALIGN | WS_TABSTOP,351,335,64,12
    CONTROL         "",IDC_DATETIME_END,"SysDateTimePick32",DTS_RIGHTALIGN | WS_TABSTOP,435,335,64,12
    PUSHBUTTON      "新增",IDC_BUTTON_NEW,744,18,33,14
    PUSHBUTTON      "删除",IDC_BUTTON_DEL,792,18,33,14
    PUSHBUTTON      "保存",IDOK,832,18,33,14
    LTEXT           "名称：",IDC_STATIC,155,22,26,8
    LTEXT           "标的选择：",IDC_STATIC,155,268,41,8
    LTEXT           "用途：",IDC_STATIC,290,22,24,8
    LTEXT           "分类：",IDC_STATIC,155,35,26,8
    LTEXT           "数据：",IDC_STATIC,224,35,26,8
    LTEXT           "适用周期：",IDC_STATIC,422,22,40,8
    PUSHBUTTON      "编译",IDC_BUTTON_EXPRESSION_CHECK,524,18,37,14
    CONTROL         "执行",IDC_CHECK_EXECUTE_EXPRESSION,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,566,20,31,10
    PUSHBUTTON      "更新计算",IDC_BUTTON_RECALC_FACTOR,595,18,40,14
    PUSHBUTTON      "复制",IDC_BUTTON_COPY,663,18,33,14
    PUSHBUTTON      "粘贴",IDC_BUTTON_PASTE,703,18,33,14
    GROUPBOX        "策略编辑",IDC_STATIC,151,7,764,275
    GROUPBOX        "策略回测",IDC_STATIC,151,287,765,89
    CONTROL         "所有",IDC_RADIO_OBJECT_RANGE,"Button",BS_AUTORADIOBUTTON | WS_GROUP,157,302,32,10
    CONTROL         "当前",IDC_RADIO_OBJECT_RANGE1,"Button",BS_AUTORADIOBUTTON,157,317,32,10
    CONTROL         "指定板块",IDC_RADIO_OBJECT_RANGE2,"Button",BS_AUTORADIOBUTTON,157,333,46,10
    CONTROL         "所有行情",IDC_RADIO_QUOTE_RANGE,"Button",BS_AUTORADIOBUTTON | WS_GROUP,298,304,48,10
    CONTROL         "最后n个Bar  n=",IDC_RADIO_QUOTE_RANGE1,"Button",BS_AUTORADIOBUTTON,298,320,67,10
    CONTROL         "指定时段",IDC_RADIO_QUOTE_RANGE2,"Button",BS_AUTORADIOBUTTON,298,334,48,10
    LTEXT           "~",IDC_STATIC,422,337,8,8
    CONTROL         "当前策略",IDC_RADIO_SEL_STRATEGY,"Button",BS_AUTORADIOBUTTON | WS_GROUP,516,304,48,10
    CONTROL         "选定策略",IDC_RADIO_SEL_STRATEGY1,"Button",BS_AUTORADIOBUTTON,516,320,48,10
    CONTROL         "空头策略",IDC_CHECK_IS_SHORT,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,572,304,48,10
    LTEXT           "多头策略",IDC_STATIC,570,321,33,8
    LTEXT           "空头策略",IDC_STATIC,571,337,33,8
    LTEXT           "",IDC_STATIC_STATUS,735,314,64,34
    LTEXT           "",IDC_STATIC_TREND,806,314,54,34,SS_NOTIFY
    LTEXT           "起始",IDC_STATIC,243,360,19,8
    LTEXT           "步长",IDC_STATIC,309,360,17,8
    LTEXT           "步数",IDC_STATIC,373,360,17,8
    LTEXT           "长周期",IDC_STATIC,213,360,25,8
    LTEXT           "起始",IDC_STATIC,477,360,19,8
    LTEXT           "步长",IDC_STATIC,543,360,17,8
    LTEXT           "步数",IDC_STATIC,607,360,17,8
    LTEXT           "短周期",IDC_STATIC,448,360,25,8
    CONTROL         "批量回测",IDC_CHECK_BATCH,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,159,359,48,10
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDHORZ,153,352,760,1,WS_EX_STATICEDGE
    EDITTEXT        IDC_EDIT_AMOUNT,819,358,40,14,ES_AUTOHSCROLL
    LTEXT           "初始本金",IDC_STATIC,685,360,33,8
    LTEXT           "交易金额",IDC_STATIC,779,360,33,8
    COMBOBOX        IDC_COMBO_CATEGORY,384,20,32,161,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "分类：",IDC_STATIC,357,22,24,8
    PUSHBUTTON      "输出报告",IDC_BUTTON_EXPORT_REPORT,864,335,50,14
    CONTROL         "科创板",IDC_CHECK_SHKCB,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,373,267,33,11
    PUSHBUTTON      "导出...",IDC_BUTTON_EXPORT,880,18,33,14
    CONTROL         "发布",IDC_CHECK_RELEASE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,887,269,27,10
END

IDD_DIALOG_SIMULATION DIALOGEX 0, 0, 309, 156
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "策略测试"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_DATETIME_START,"SysDateTimePick32",DTS_RIGHTALIGN | WS_TABSTOP,138,127,64,15
    CONTROL         "",IDC_DATETIME_END,"SysDateTimePick32",DTS_RIGHTALIGN | WS_TABSTOP,224,124,64,15
    PUSHBUTTON      "回测",IDC_BUTTON_BACKTEST,173,14,50,14
    EDITTEXT        IDC_EDIT1,71,31,73,14,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO1,71,14,73,51,CBS_DROPDOWN | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "初始资金",IDC_STATIC,21,33,33,8
    LTEXT           "选择策略",IDC_STATIC,21,17,33,8
    LTEXT           "开始时间",IDC_STATIC,136,113,33,8
    LTEXT           "结束时间",IDC_STATIC,223,113,33,8
    GROUPBOX        "范围",IDC_STATIC,130,52,172,95
    CONTROL         "所有行情",IDC_RADIO1,"Button",BS_AUTORADIOBUTTON,132,67,48,10
    CONTROL         "最后n天 n=",IDC_RADIO2,"Button",BS_AUTORADIOBUTTON,133,83,55,10
    EDITTEXT        IDC_EDIT2,191,81,40,14,ES_AUTOHSCROLL
    CONTROL         "指定时段",IDC_RADIO3,"Button",BS_AUTORADIOBUTTON,133,97,48,10
    GROUPBOX        "应用于",IDC_STATIC,19,55,103,91
    CONTROL         "所有",IDC_RADIO4,"Button",BS_AUTORADIOBUTTON,25,71,32,10
    CONTROL         "当前",IDC_RADIO5,"Button",BS_AUTORADIOBUTTON,25,90,32,10
    CONTROL         "指定板块",IDC_RADIO6,"Button",BS_AUTORADIOBUTTON,25,107,48,10
    COMBOBOX        IDC_COMBO2,25,124,78,30,CBS_DROPDOWN | CBS_SORT | WS_VSCROLL | WS_TABSTOP
END

IDD_DIALOG_ORDER_MANAGMENT DIALOGEX 0, 0, 780, 247
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_VISIBLE | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_LIST_ORDERS,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | LVS_OWNERDATA | WS_BORDER | WS_TABSTOP,1,6,778,240
END

IDD_DIALOG_PORTFOLIO_MANAGEMENT DIALOGEX 0, 0, 751, 329
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_LIST_PORTFOLIO,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | LVS_OWNERDATA | WS_BORDER | WS_TABSTOP,1,40,749,289
    LTEXT           "本    金",-1,14,12,25,8
    LTEXT           "可用现金",-1,14,26,33,8
    LTEXT           "当日盈亏",-1,119,13,33,8
    LTEXT           "仓位市值",-1,119,26,33,8
    LTEXT           "资金用比",-1,216,26,33,8
    LTEXT           "日收益率",-1,216,12,33,8
    LTEXT           "累计收益率",-1,298,12,41,8
    LTEXT           "总   权   益",-1,298,26,41,8
    CONTROL         "--.--",IDC_STATIC_LB00,"Static",SS_SIMPLE | WS_GROUP,50,13,59,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB01,"Static",SS_SIMPLE | WS_GROUP,50,26,59,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB02,"Static",SS_SIMPLE | WS_GROUP,159,13,45,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB03,"Static",SS_SIMPLE | WS_GROUP,159,26,45,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB05,"Static",SS_SIMPLE | WS_GROUP,256,26,36,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB04,"Static",SS_SIMPLE | WS_GROUP,256,12,36,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB07,"Static",SS_SIMPLE | WS_GROUP,341,26,47,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    LTEXT           "夏普比率",-1,405,13,41,8
    LTEXT           "年化收益率",IDC_STATIC_POSITION_SIZE,405,26,41,8,SS_NOTIFY
    CONTROL         "--.--",IDC_STATIC_LB08,"Static",SS_SIMPLE | SS_NOTIFY | WS_GROUP,451,12,43,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB09,"Static",SS_SIMPLE | SS_NOTIFY | WS_GROUP,451,26,43,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    CONTROL         "--.--",IDC_STATIC_LB06,"Static",SS_SIMPLE | WS_GROUP,341,12,47,10,WS_EX_TRANSPARENT | WS_EX_RIGHT
    PUSHBUTTON      "详细报告...",IDC_BUTTON_REPORT,556,8,50,14
    LTEXT           "",IDC_STATIC_INFO,557,27,108,9
END

IDD_DIALOG_SYSTEM_LOG DIALOGEX 0, 0, 622, 98
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LISTBOX         IDC_LIST_SYS_LOG,0,6,622,92,LBS_SORT | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
END

IDD_DIALOG_ACCOUNT_INFORMATION DIALOGEX 0, 0, 777, 228
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_LIST_ACCOUNT_INFO,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | LVS_OWNERDATA | WS_BORDER | WS_TABSTOP,0,7,777,221
END

IDD_DIALOG_TREND_CHART DIALOGEX 0, 0, 689, 374
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "趋势分析"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "CustomPrice",IDC_CUSTOM_PRICE_CHART,"ChartCtrl",WS_CLIPCHILDREN | WS_TABSTOP,0,0,689,181
    CONTROL         "CustomIndicator1",IDC_CUSTOM_INDICATOR_CHART1,"ChartCtrl",WS_CLIPCHILDREN | WS_TABSTOP,0,237,689,63
    CONTROL         "CustomIndicator2",IDC_CUSTOM_INDICATOR_CHART2,"ChartCtrl",WS_CLIPCHILDREN | WS_TABSTOP,0,299,689,62
    CONTROL         "CustomVolume",IDC_CUSTOM_VOLUME_CHART,"ChartCtrl",WS_CLIPCHILDREN | WS_TABSTOP,0,180,689,57
    CONTROL         "",IDC_TAB_INDICTOR_NAME,"SysTabControl32",TCS_FOCUSNEVER,0,361,689,13
END

IDD_DIALOG_OPTION DIALOGEX 0, 0, 765, 211
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    PUSHBUTTON      "保存参数",IDC_BUTTON_RELOAD_OPTION,109,134,50,14
    GROUPBOX        "Range Bar参数设置",IDC_STATIC,7,7,200,122
    LTEXT           "历史数据长度",IDC_STATIC,20,54,49,8
    EDITTEXT        IDC_EDIT_HISTORY_NUM,77,52,40,14,ES_RIGHT | ES_AUTOHSCROLL
    PUSHBUTTON      "测试参数",IDC_BUTTON_WRITE_RANGE,37,134,50,14
    CONTROL         "适用于期货",IDC_RADIO_FUTURE,"Button",BS_AUTORADIOBUTTON | WS_GROUP,19,22,56,10
    CONTROL         "适用于股票",IDC_RADIO_STOCK,"Button",BS_AUTORADIOBUTTON,79,22,56,10
    PUSHBUTTON      "主连归档",IDC_BUTTON_MAIN_FUT_STORE,238,51,50,14
    PUSHBUTTON      "归档整理",IDC_BUTTON2,294,51,50,14
    GROUPBOX        "期货历史行情归档",IDC_STATIC,224,7,133,69
    CONTROL         "通达信",IDC_RADIO_TDX,"Button",BS_AUTORADIOBUTTON | WS_GROUP,240,20,40,10
    CONTROL         "文华财经",IDC_RADIO_WH,"Button",BS_AUTORADIOBUTTON,295,21,48,10
    CONTROL         "归档行情库",IDC_RADIO_DB,"Button",BS_AUTORADIOBUTTON,240,35,56,10
    CONTROL         "使用期货主连行情作为回测数据",IDC_CHECK_ASIO,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,375,14,128,10
    CONTROL         "输出交易日志信息",IDC_CHECK_LOGGING,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,511,14,80,10
    EDITTEXT        IDC_EDIT_ATR_THRESHOLD1,60,85,31,14,ES_RIGHT | ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_ATR_THRESHOLD2,155,85,31,14,ES_RIGHT | ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_RANGE_L1,60,104,31,14,ES_RIGHT | ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_RANGE_L2,108,104,31,14,ES_RIGHT | ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_RANGE_L3,155,104,31,14,ES_RIGHT | ES_AUTOHSCROLL
    LTEXT           "Level1",IDC_STATIC,62,71,22,8
    LTEXT           "Level2",IDC_STATIC,111,71,22,8
    LTEXT           "Level3",IDC_STATIC,159,71,22,8
    CONTROL         "短周期",IDC_RADIO_SHORT,"Button",BS_AUTORADIOBUTTON | WS_GROUP,19,39,40,10
    CONTROL         "长周期",IDC_RADIO_LONG,"Button",BS_AUTORADIOBUTTON,79,38,40,10
    LTEXT           "ATR Ratio",IDC_STATIC,18,88,33,8
    LTEXT           "Range Ratio",IDC_STATIC,11,106,40,8
    LTEXT           "",IDC_STATIC_STATS,7,156,170,34
    CONTROL         "",IDC_SPIN_THRESHOLD1,"msctls_updown32",UDS_ALIGNRIGHT | UDS_ARROWKEYS,91,85,10,14
    CONTROL         "",IDC_SPIN_L1,"msctls_updown32",UDS_ALIGNRIGHT | UDS_ARROWKEYS,91,104,10,14
    CONTROL         "",IDC_SPIN_L2,"msctls_updown32",UDS_ALIGNRIGHT | UDS_ARROWKEYS,139,104,10,14
    CONTROL         "",IDC_SPIN_THRESHOLD2,"msctls_updown32",UDS_ARROWKEYS,187,85,11,14
    CONTROL         "",IDC_SPIN_L3,"msctls_updown32",UDS_ARROWKEYS,187,104,10,14
    PUSHBUTTON      "上传RangeBar参数",IDC_BUTTON_SYNC_RANGEBAR,237,90,79,14
    PUSHBUTTON      "上传主力期货代码",IDC_BUTTON_SYNC_MAIN_FUTURE,237,127,80,14
    PUSHBUTTON      "上传期货历史行情",IDC_BUTTON_SYNC_FUTURE_HISTORY_DATA,237,146,79,14
    PUSHBUTTON      "上传交易策略数据",IDC_BUTTON_SYNC_FACTOR_DB,237,108,79,14
    EDITTEXT        IDC_EDIT_INPUT,397,29,307,14,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "执行",IDC_RUN,708,28,50,14
    LTEXT           ">>>",IDC_STATIC,374,32,17,8
    CONTROL         "并行模拟行情",IDC_CHECK_QUOTE_MODE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,612,14,64,10
    PUSHBUTTON      "导入代码表",IDC_BUTTON_IMPORT_CODE_TABLE,374,53,50,14
    PUSHBUTTON      "上传证券代码表",IDC_BUTTON_SYNC_FUTURE_CODE_TABLE,237,165,79,14
    PUSHBUTTON      "Console View",IDC_BUTTON_CONSOLE,440,53,50,14
END

IDD_DIALOG_BACKTEST_REPORT DIALOGEX 0, 0, 787, 443
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "收益率曲线"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    COMBOBOX        IDC_COMBO_PORTFOLIO,61,8,76,92,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    CONTROL         "收益曲线",IDC_RADIO_TYPE,"Button",BS_AUTORADIOBUTTON | NOT WS_VISIBLE | WS_GROUP,0,26,48,10
    CONTROL         "指数比较",IDC_RADIO_TYPE2,"Button",BS_AUTORADIOBUTTON | NOT WS_VISIBLE,58,26,48,10
    CONTROL         "YieldCurveChart",IDC_YIELD_CURVE_CHART,"ChartCtrl",WS_CLIPCHILDREN | WS_TABSTOP,0,32,787,289
    CONTROL         "YieldCurveChart",IDC_PNL_CHART,"ChartCtrl",WS_CLIPCHILDREN | WS_TABSTOP,0,321,787,122
    LTEXT           "选择投资组合：",IDC_STATIC,0,10,57,8
    LTEXT           "RiskData",IDC_STATIC_RISK_INFO,147,9,640,20
END

IDD_DIALOG_BROWSER DIALOGEX 0, 0, 419, 275
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
END

IDD_DIALOG_SET_RANGE_VALUE DIALOGEX 0, 0, 229, 80
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Set RangeBar Value"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,113,59,50,14
    PUSHBUTTON      "取消",IDCANCEL,173,59,50,14
    EDITTEXT        IDC_EDIT_RANGE,90,28,52,14,ES_AUTOHSCROLL
END

IDD_DIALOG_CEF DIALOGEX 0, 0, 587, 391
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
END

IDD_DIALOG_EXPRESS_PAGE0 DIALOGEX 0, 0, 584, 213
STYLE DS_SETFONT | WS_CHILD | WS_SYSMENU
FONT 9, "新宋体", 400, 0, 0x86
BEGIN
    LTEXT           "入市策略：",IDC_STATIC_EXPRESS1,1,0,64,8
    EDITTEXT        IDC_EDIT_EXPRESSION,0,11,385,49,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_EXPRESSION2,0,73,385,82,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_EXPRESSION3,0,167,385,45,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_EXPRESSION4,390,11,193,50,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_EXPRESSION5,389,74,194,52,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    LTEXT           "增仓策略：",IDC_STATIC,392,1,44,8
    LTEXT           "止损/止盈策略：",IDC_STATIC_EXPRESS2,0,63,64,8
    LTEXT           "离市策略：",IDC_STATIC,0,157,43,8
    LTEXT           "减仓策略：",IDC_STATIC,392,63,43,8
    CTEXT           "",IDC_STATIC_NAME,147,1,123,8
    EDITTEXT        IDC_EDIT_REMARK,389,141,194,71,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    LTEXT           "策略说明：",IDC_STATIC,392,129,43,8
END

IDD_DIALOG_EXPRESS_PAGE1 DIALOGEX 0, 0, 585, 212
STYLE DS_SETFONT | WS_CHILD | WS_SYSMENU
FONT 9, "新宋体", 400, 0, 0x86
BEGIN
    LTEXT           "个股/合约筛选：",IDC_STATIC_EXPRESS1,1,0,64,8
    EDITTEXT        IDC_EDIT_EXPRESSION6,0,11,584,116,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_EXPRESSION7,0,142,292,70,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    LTEXT           "板块筛选：",IDC_STATIC_EXPRESS2,0,131,64,8
    CTEXT           "",IDC_STATIC_NAME,224,1,123,8
    EDITTEXT        IDC_EDIT_REMARK2,299,142,284,70,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    LTEXT           "模块说明：",IDC_STATIC,300,131,43,8
END

IDD_DIALOG_INSTRUMENT DIALOGEX 0, 0, 762, 195
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_LIST_FILTER_RESULT,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | LVS_OWNERDATA | WS_BORDER | WS_TABSTOP,0,0,762,195
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 3,0,0,1
 PRODUCTVERSION 3,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "080404b0"
        BEGIN
            VALUE "CompanyName", "新旭航科技有限公司"
            VALUE "FileDescription", "LabTools"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "LabTools.exe"
            VALUE "LegalCopyright", "(C) 新旭航科技有限公司。  保留所有权利。"
            VALUE "OriginalFilename", "LabTools.exe"
            VALUE "ProductName", "TODO: <产品名>"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x804, 1200
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDD_ABOUTBOX, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 163
        TOPMARGIN, 7
        BOTTOMMARGIN, 55
    END

    IDD_LABTOOLS_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 916
        TOPMARGIN, 7
        BOTTOMMARGIN, 623
    END

    IDD_DIALOG_SIMULATION, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
        BOTTOMMARGIN, 149
    END

    IDD_DIALOG_ORDER_MANAGMENT, DIALOG
    BEGIN
        LEFTMARGIN, 1
        RIGHTMARGIN, 779
        TOPMARGIN, 6
        BOTTOMMARGIN, 246
    END

    IDD_DIALOG_PORTFOLIO_MANAGEMENT, DIALOG
    BEGIN
        LEFTMARGIN, 1
        RIGHTMARGIN, 750
        TOPMARGIN, 7
    END

    IDD_DIALOG_SYSTEM_LOG, DIALOG
    BEGIN
        TOPMARGIN, 6
    END

    IDD_DIALOG_ACCOUNT_INFORMATION, DIALOG
    BEGIN
        RIGHTMARGIN, 763
        TOPMARGIN, 7
    END

    IDD_DIALOG_TREND_CHART, DIALOG
    BEGIN
    END

    IDD_DIALOG_OPTION, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 758
        TOPMARGIN, 7
        BOTTOMMARGIN, 204
    END

    IDD_DIALOG_BACKTEST_REPORT, DIALOG
    BEGIN
        TOPMARGIN, 1
    END

    IDD_DIALOG_BROWSER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 412
        TOPMARGIN, 7
        BOTTOMMARGIN, 268
    END

    IDD_DIALOG_SET_RANGE_VALUE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 222
        TOPMARGIN, 7
        BOTTOMMARGIN, 73
    END

    IDD_DIALOG_CEF, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 580
        TOPMARGIN, 7
        BOTTOMMARGIN, 384
    END

    IDD_DIALOG_EXPRESS_PAGE0, DIALOG
    BEGIN
        RIGHTMARGIN, 583
        BOTTOMMARGIN, 212
    END

    IDD_DIALOG_EXPRESS_PAGE1, DIALOG
    BEGIN
        RIGHTMARGIN, 584
        BOTTOMMARGIN, 208
    END

    IDD_DIALOG_INSTRUMENT, DIALOG
    BEGIN
        RIGHTMARGIN, 739
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// AFX_DIALOG_LAYOUT
//

IDD_LABTOOLS_DIALOG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_SIMULATION AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_PORTFOLIO_MANAGEMENT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_ORDER_MANAGMENT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_ACCOUNT_INFORMATION AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_SYSTEM_LOG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_OPTION AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_TREND_CHART AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_BROWSER AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_SET_RANGE_VALUE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_CEF AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_EXPRESS_PAGE0 AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_EXPRESS_PAGE1 AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_INSTRUMENT AFX_DIALOG_LAYOUT
BEGIN
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// Dialog Info
//

IDD_LABTOOLS_DIALOG DLGINIT
BEGIN
    IDC_COMBO_USES, 0x403, 5, 0
0xa1d1, 0xc9b9, "\000" 
    IDC_COMBO_USES, 0x403, 5, 0
0xdfb2, 0xd4c2, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 5, 0
0xd6b7, 0xcab1, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 6, 0
0xb731, 0xd6d6, 0x00d3, 
    IDC_COMBO_BAR_SIZE, 0x403, 6, 0
0xb735, 0xd6d6, 0x00d3, 
    IDC_COMBO_BAR_SIZE, 0x403, 7, 0
0x3531, 0xd6b7, 0xd3d6, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 7, 0
0x3033, 0xd6b7, 0xd3d6, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 7, 0
0x3036, 0xd6b7, 0xd3d6, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 9, 0
0x6152, 0x676e, 0x4265, 0x7261, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 3, 0
0xd5c8, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 3, 0
0xdcd6, "\000" 
    IDC_COMBO_BAR_SIZE, 0x403, 3, 0
0xc2d4, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xebc8, 0xd0ca, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xf7c7, 0xc6ca, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xb9d6, 0xafd3, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xb9d6, 0xf0cb, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xf6d4, 0xd6b2, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xf5bc, 0xd6b2, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xebc0, 0xd0ca, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xa8b2, 0xafb6, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xa1d1, 0xc9b9, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xe4c6, 0xfccb, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0x2d2d, 0x2d2d, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xdac6, 0xf5bb, "\000" 
    IDC_COMBO_CATEGORY, 0x403, 5, 0
0xc9b9, 0xb1c6, "\000" 
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//

IDB_BITMAP_FILTER_TREE  BITMAP                  "res\\bitmapftree.bmp"


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

IDR_MENU_TREND_CHART MENU
BEGIN
    POPUP "TRENDCHART"
    BEGIN
        MENUITEM "重载指标参数",                      ID_TRENDCHART_RELOAD_PARAMTER
        MENUITEM "重置RangeBar参数",                ID_TRENDCHART_SET_RANGE
        MENUITEM "检查历史清理当前历史数据",                ID_TRENDCHART_CHECK_HISTORYDATA
        MENUITEM SEPARATOR
        MENUITEM "1分钟",                         ID_TRENDCHART_MIN1
        MENUITEM "5分钟",                         ID_TRENDCHART_MIN5
        MENUITEM "15分钟",                        ID_TRENDCHART_MIN15
        MENUITEM "30分钟",                        ID_TRENDCHART_MIN30
        MENUITEM "60分钟",                        ID_TRENDCHART_MIN60
        MENUITEM "RangeBar",                    ID_TRENDCHART_RANGEBAR
        MENUITEM "日",                           ID_TRENDCHART_DAY
        MENUITEM "周",                           ID_TRENDCHART_WEEK
        MENUITEM "月",                           ID_TRENDCHART_MONTH
        MENUITEM "季",                           ID_TRENDCHART_QARTER
        MENUITEM "年",                           ID_TRENDCHART_YEAR
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// RT_MANIFEST
//

1                       RT_MANIFEST             "res\\rt_manif2.bin"


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_ABOUTBOX            "关于 LabTools(&A)..."
END

#endif    // 中文(简体，中国) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
LANGUAGE 4, 2
#include "res\LabTools.rc2"  // 闂?Microsoft Visual C++ 缂傚倸鍊搁崐鎼佸磹瑜版帗鍋嬮柣鎰仛椤愯姤銇勯幇鍫曟闁稿骸绉归弻娑㈠即閵娿儲鐏撻梺闈涚墱閸嬪懐鎹?
#include "afxres.rc"      // 闂傚倷绀侀幖顐ょ矓閺夋嚚娲Χ婢跺﹪妫峰銈嗙墬缁海澹曟總鍛婄厱闁哄洢鍔嬬花鍏笺亜?
#if !defined(_AFXDLL)
#include "afxribbon.rc"   // MFC 闂傚倷绀侀幉鈥愁潖婵犳艾绐楅柡鍥ュ灩缁€鍌涙叏濡炶浜鹃悗瑙勬礈婵炩偓鐎规洦鍋婂畷鐔碱敆閳ь剟顢旈鐐粹拺缁绢厼鎳愰悞浠嬫煕閹扳晛濡块柛姗嗗墴濮婃椽宕崟鍨紖闂侀潧鐗嗗ú銏ゅ磻閹烘垟鏀?
#endif
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

