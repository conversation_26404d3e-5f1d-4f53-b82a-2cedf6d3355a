#pragma once

/**
 * @file Event.h
 * @brief Event system for trading algorithms
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, He<PERSON>z<PERSON>. All rights reserved.
 * @license Apache 2.0
 */

#include <functional>
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <type_traits>

namespace Spectre {
namespace Trading {

// Forward declarations
class EventManager;
class EventReceiver;
class Calendar;

/**
 * @brief Base class for all events in the trading system
 */
class Event {
public:
    using Callback = std::function<void(EventReceiver*)>;
    using TimePoint = std::chrono::system_clock::time_point;

    /**
     * @brief Constructor
     * @param callback Function to call when event triggers
     */
    explicit Event(Callback callback) : callback_(std::move(callback)) {}

    virtual ~Event() = default;

    /**
     * @brief Called when event is scheduled with an event manager
     * @param evt_mgr Pointer to the event manager
     */
    virtual void on_schedule(EventManager* evt_mgr) {}

    /**
     * @brief Check if event should trigger
     * @return True if event should trigger
     */
    virtual bool should_trigger() = 0;

    /**
     * @brief Execute the event callback
     * @param source Event source (usually the receiver that scheduled it)
     */
    void execute(EventReceiver* source) {
        if (callback_) {
            callback_(source);
        }
    }

protected:
    Callback callback_;
};

/**
 * @brief Event that triggers on every bar of data
 */
class EveryBarData : public Event {
public:
    explicit EveryBarData(Callback callback) : Event(std::move(callback)) {}
    
    bool should_trigger() override {
        return false; // Triggered passively by data updates
    }
};

/**
 * @brief Event that always triggers (useful for live data I/O)
 */
class Always : public Event {
public:
    explicit Always(Callback callback) : Event(std::move(callback)) {}
    
    bool should_trigger() override {
        return true;
    }
};

/**
 * @brief Base class for calendar-based events
 */
class CalendarEvent : public Event {
public:
    /**
     * @brief Constructor
     * @param calendar_event_name Name of the calendar event
     * @param callback Function to call when event triggers
     * @param offset_ns Offset in nanoseconds from the calendar event
     */
    CalendarEvent(const std::string& calendar_event_name, Callback callback, 
                  int64_t offset_ns = 0);

    void on_schedule(EventManager* evt_mgr) override;
    bool should_trigger() override;

private:
    std::string event_name_;
    int64_t offset_;
    std::shared_ptr<Calendar> calendar_;
    TimePoint trigger_time_;

    void calculate_range();
};

/**
 * @brief Market open event
 */
class MarketOpen : public CalendarEvent {
public:
    MarketOpen(Callback callback, int64_t offset_ns = 0)
        : CalendarEvent("Open", std::move(callback), offset_ns) {}
};

/**
 * @brief Market close event
 */
class MarketClose : public CalendarEvent {
public:
    MarketClose(Callback callback, int64_t offset_ns = 0)
        : CalendarEvent("Close", std::move(callback), offset_ns) {}
};

/**
 * @brief Base class for objects that can receive events
 */
class EventReceiver {
public:
    EventReceiver() : event_manager_(nullptr) {}
    virtual ~EventReceiver() = default;

    /**
     * @brief Unsubscribe from event manager
     */
    void unsubscribe();

    /**
     * @brief Schedule an event
     * @param evt Event to schedule
     */
    void schedule(std::shared_ptr<Event> evt);

    /**
     * @brief Stop the event manager
     */
    void stop_event_manager();

    /**
     * @brief Fire an event of specific type
     * @tparam EventType Type of event to fire
     */
    template<typename EventType>
    void fire_event();

    /**
     * @brief Called when event manager starts running
     */
    virtual void on_run() {}

    /**
     * @brief Called when event manager finishes running
     */
    virtual void on_end_of_run() {}

private:
    friend class EventManager;
    EventManager* event_manager_;
};

/**
 * @brief Manages event scheduling and execution
 */
class EventManager {
public:
    EventManager() : stop_(false) {}
    virtual ~EventManager() = default;

    /**
     * @brief Subscribe an event receiver
     * @param receiver Receiver to subscribe
     */
    void subscribe(std::shared_ptr<EventReceiver> receiver);

    /**
     * @brief Unsubscribe an event receiver
     * @param receiver Receiver to unsubscribe
     */
    void unsubscribe(EventReceiver* receiver);

    /**
     * @brief Schedule an event for a receiver
     * @param receiver Receiver that scheduled the event
     * @param event Event to schedule
     */
    void schedule(EventReceiver* receiver, std::shared_ptr<Event> event);

    /**
     * @brief Fire an event of specific type
     * @tparam EventType Type of event to fire
     * @param source Source of the event
     */
    template<typename EventType>
    void fire_event(EventReceiver* source);

    /**
     * @brief Stop the event manager
     */
    void stop() { stop_ = true; }

    /**
     * @brief Check if event manager should stop
     * @return True if should stop
     */
    bool should_stop() const { return stop_; }

protected:
    using ReceiverPtr = std::shared_ptr<EventReceiver>;
    using EventList = std::vector<std::shared_ptr<Event>>;
    
    std::unordered_map<EventReceiver*, ReceiverPtr> subscribers_;
    std::unordered_map<EventReceiver*, EventList> events_;
    bool stop_;

    /**
     * @brief Process all scheduled events
     */
    virtual void process_events();

    /**
     * @brief Check and trigger events that should fire
     */
    virtual void check_triggers();
};

/**
 * @brief Event manager for market-based events with calendar support
 */
class MarketEventManager : public EventManager {
public:
    /**
     * @brief Constructor
     * @param calendar Market calendar for event scheduling
     */
    explicit MarketEventManager(std::shared_ptr<Calendar> calendar);

    /**
     * @brief Run the event manager for live trading
     */
    void run();

    /**
     * @brief Get the market calendar
     * @return Shared pointer to calendar
     */
    std::shared_ptr<Calendar> calendar() const { return calendar_; }

private:
    std::shared_ptr<Calendar> calendar_;
};

} // namespace Trading
} // namespace Spectre
