#pragma once
#include "afxcmn.h"
#include <quicklist/QuickList.h>
#include <boost/thread.hpp>
#include <DataDef.h>
#include <vector>
#include <Instrument.h>
const int nCols = 19;
const int nNames = 9;

class DlgStockPool : public CDialogEx
{
	DECLARE_DYNAMIC(DlgStockPool)

public:
	DlgStockPool(CWnd* pParent = nullptr); 
	virtual ~DlgStockPool();

	enum { IDD = IDD_DIALOG_STOCK_POOL };

protected:
	CQuickList m_listCtrlSec;
	BlockDataPtr _ag_bd_ptr;
	BlockDataPtr _bd_ptr;
	BlockDataPtr _bd_bak_ptr;
	BlockDataPtr _filter_ptr0;
	BlockDataPtr _filter_ptr1;

	std::map<std::string, int> _scanRecords;

	int m_nCurSel;
	std::vector<std::array<std::string, nCols+1>> _listText;
	static char* _szHeaders[nCols];
	static char* _szMarkStar[6];
	static char* _szStrategyName[nNames];

	int _nHoldBlockCategory;
	int _nScanTimeCount;
	CMenu _blockMenu;

	FactorDataEx _fd;

	void InitList();
	void FillList();
	void SortColumn();
	void UpdateFilterStocks();
	void CreateBlockMenu();
	void UpdateNotify(int id, const std::string& label);

	virtual void DoDataExchange(CDataExchange* pDX);

	DECLARE_MESSAGE_MAP()

public:
	std::string _blockCustom;
	static CString m_sCustomBlock;
	static int nCurCol;
	static bool bFlag;
	static COLORREF _szMarkColor[6];
	static std::string _sSortName;
	int m_nFilterType;
	int m_nIndex;
	void UpdateCombox();
	void UpdateFilterDataList();
	void SelectPrev();
	void SelectNext();
	std::string GetPrevLabel();
	std::string GetNextLabel();

	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnCbnSelendokComboCustomBlock();
	afx_msg void OnMenuNewOrder();
	afx_msg void OnMenuNewTradingStrategy();
	afx_msg void OnNMRClickListSec(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg LRESULT OnGetListItem(WPARAM wParam, LPARAM lParam);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnBnClickedButtonFilter();
	afx_msg void OnBnClickedButtonSave();
	afx_msg void OnNMDblclkListSec(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnLvnColumnclickListSec(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnSplAddToCustomBlock();
	afx_msg void OnSplDeleteFromCustomBlock();
	afx_msg void OnBnClickedButtonExport();
	afx_msg void OnSplMarkOne();
	afx_msg void OnSplMarkTwo();
	afx_msg void OnSplMarkThree();
	afx_msg void OnSplMarkFour();
	afx_msg void OnSplMarkFive();
	afx_msg void OnSplMarkClear();
	afx_msg void OnSplRed();
	afx_msg void OnSplBlue();
	afx_msg void OnSplGreen();
	afx_msg void OnSplYellow();
	afx_msg void OnSplOrange();
	afx_msg void OnSplClearColor();
	afx_msg void OnBnClickedButtonUpdate();
	afx_msg void OnBnClickedButtonFilterSetting();
	afx_msg void OnSelendokComboFilterdata();
	afx_msg void OnClickedRadioFilterType();
	afx_msg LRESULT OnHotKey(WPARAM wParam,LPARAM lParam);
	afx_msg void OnDestroy();
	afx_msg void OnBnClickedButtonBlockSelect();
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
};
