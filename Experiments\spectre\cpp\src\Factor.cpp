#include "Factor.h"
#include "Engine.h"
#include "Utils.h"
#include <algorithm>
#include <stdexcept>

namespace Spectre {

// ============================================================================
// CustomFactor Implementation
// ============================================================================

CustomFactor::CustomFactor(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs)
    : m_win(win), m_inputs(inputs) {
    // Validation similar to Python's __init__
    if (m_win < m_min_win) {
        throw std::runtime_error("Factor win (" + std::to_string(m_win) +
                                ") must be >= min_win (" + std::to_string(m_min_win) + ")");
    }
}

int CustomFactor::get_total_backwards() const {
    int backwards = 0;
    if (!m_inputs.empty()) {
        for (const auto& up : m_inputs) {
            if (up) { // Ensure pointer is valid
                backwards = std::max(backwards, up->get_total_backwards());
            }
        }
    }
    backwards = backwards + m_win - 1;

    if (m_mask) {
        return std::max(m_mask->get_total_backwards(), backwards);
    } else {
        return backwards;
    }
}

bool CustomFactor::should_delay() const {
    bool ret = BaseFactor::should_delay();
    if (m_force_delay) {
        return true;
    }

    if (!m_inputs.empty()) {
        for (const auto& upstream : m_inputs) {
            if (upstream && upstream->should_delay()) {
                return true;
            }
        }
    }

    if (m_mask && m_mask->should_delay()) {
        return true;
    }

    return ret;
}

void CustomFactor::pre_compute(FactorEngine* engine) {
    BaseFactor::pre_compute(engine);

    m_ref_count += 1;
    if (m_ref_count > 1) { // Already pre-computed, skip children
        return;
    }

    if (!m_inputs.empty()) {
        for (auto& upstream : m_inputs) {
            if (upstream) {
                upstream->pre_compute(engine);
            }
        }
    }

    if (m_mask) {
        m_mask->pre_compute(engine);
    }

    if (m_keep_cache) {
        // Increase ref count so this factor's cache will not be cleaned up
        m_ref_count += 1;
    }
}

void CustomFactor::clean_up() {
    BaseFactor::clean_up();

    if (!m_keep_cache) {
        m_cache = torch::Tensor();
    }
    m_ref_count = 0;

    if (!m_inputs.empty()) {
        for (auto& upstream : m_inputs) {
            if (upstream) {
                upstream->clean_up();
            }
        }
    }

    if (m_mask) {
        m_mask->clean_up();
    }
}

torch::Tensor CustomFactor::compute_() {
    // Return cached result
    m_ref_count -= 1;
    if (m_ref_count < 0) {
        throw std::runtime_error("Reference count error: possible circular reference!");
    }

    if (m_cache.defined()) {
        torch::Tensor ret = m_cache;
        if (m_ref_count == 0) {
            m_cache = torch::Tensor();
        }
        return ret;
    }

    // Calculate mask
    torch::Tensor mask_out;
    if (m_mask) {
        if (auto custom_mask = std::dynamic_pointer_cast<CustomFactor>(m_mask)) {
            mask_out = custom_mask->compute_();
        }
    }

    // Calculate inputs
    std::vector<torch::Tensor> inputs;
    if (!m_inputs.empty()) {
        for (auto& upstream : m_inputs) {
            if (upstream) {
                torch::Tensor out;
                if (auto custom_upstream = std::dynamic_pointer_cast<CustomFactor>(upstream)) {
                    out = custom_upstream->compute_();
                } else {
                    // For DataFactor or other types, need special handling
                    out = upstream->compute({});
                }

                out = format_input(upstream, out, m_mask, mask_out);
                inputs.push_back(out);
            }
        }
    }

    // Execute computation
    torch::Tensor result = compute(inputs);

    if (m_ref_count > 0) {
        m_cache = result;
    }

    return result;
}

torch::Tensor CustomFactor::format_input(std::shared_ptr<BaseFactor> upstream,
                                        const torch::Tensor& upstream_out,
                                        std::shared_ptr<BaseFactor> mask_factor,
                                        const torch::Tensor& mask_out) {
    torch::Tensor ret = upstream_out;

    // Apply mask if provided
    if (mask_out.defined()) {
        if (ret.dtype() == torch::kBool) {
            ret = ret.masked_fill(~mask_out, false);
        } else {
            ret = ret.masked_fill(~mask_out, std::numeric_limits<float>::quiet_NaN());
        }
    }

    // Apply rolling window if needed
    if (m_win > 1) {
        if (ret.dim() >= 3) {
            throw std::runtime_error("Upstream factor has multiple outputs, "
                                   "rolling win > 1 only supports one output");
        }
        // Create Rolling object - this would need to be implemented
        // For now, we'll use unfold as a simple approximation
        ret = ret.unfold(1, m_win, 1);
    }

    return ret;
}

// ============================================================================
// CrossSectionFactor Implementation
// ============================================================================

CrossSectionFactor::CrossSectionFactor(int win, const std::vector<std::shared_ptr<BaseFactor>>& inputs,
                                      std::shared_ptr<BaseFactor> mask)
    : CustomFactor(win, inputs) {
    set_mask(mask);
    initialize_groupby();
    if (m_win != 1) {
        throw std::runtime_error("CrossSectionFactor.win can only be 1");
    }
}

} // namespace Spectre
