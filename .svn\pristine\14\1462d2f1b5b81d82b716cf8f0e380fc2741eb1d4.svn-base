#pragma once
#include <DataHub.h>
//#include <leveldb/db.h>
//#include <leveldb/comparator.h>
#include <Instrument.h>
#include "Settings.h"
#include "../Utils/core/db.h"
#include <common.hpp>
#include <patterns/singleton.hpp>
#include <threadsafe_queue.hpp>

namespace dal {
// void ParseKey(const std::string& key, BarSize& barsize, std::string&
// label, int& year, int& month) { 	std::vector<std::string> vs; 	boost::split(vs,
//key, boost::is_any_of("_")); 	if (vs.size() == 3) { 		barsize = BarSize::day;
//		label = vs[1];
//		year = std::atoi(vs[2].c_str());
//		month = 0;
//	}
//	else if (vs.size() == 4) {
//		barsize = BarSize::min5;
//		label = vs[1];
//		year = std::atoi(vs[2].c_str());
//		month = std::atoi(vs[3].c_str());
//	}
//}

// class CustomComparator : public leveldb::Comparator {
// public:
//	// Three-way comparison function:
//	//   if a < b: negative result
//	//   if a > b: positive result
//	//   else: zero result
//	int Compare(const leveldb::Slice& a, const leveldb::Slice& b) const {
//		BarSize barsize1; std::string label1; int year1; int month1;
//		BarSize barsize2; std::string label2; int year2; int month2;
//		ParseKey(a.ToString(), barsize1, label1, year1, month1);
//		ParseKey(b.ToString(), barsize2, label2, year2, month2);
//		if (barsize1 < barsize2) return -1;
//		if (barsize1 > barsize2) return +1;
//		int ret = label1.compare(label2);
//		if (ret < 0) return -1;
//		if (ret > 0) return +1;
//		if (year1 < year2) return -1;
//		if (year1 > year2) return +1;
//		if (month1 < month2) return -1;
//		if (month1 > month2) return +1;
//		return 0;
//	}
//	// Ignore the following methods for now:
//	const char* Name() const { return "CustomComparator"; }
//	void FindShortestSeparator(std::string*, const leveldb::Slice&) const {
//} 	void FindShortSuccessor(std::string*) const { }
//};
typedef boost::function<void(int, const std::string &)> LogCallback;

class BackupDb {
private:
  // leveldb::DB* _db;
  LogCallback _logCallback;
  std::unique_ptr<db::DB> _db;
  std::string _db_type;
  std::unique_ptr<db::Cursor> _cursor;
  db::Mode _mode;
  std::string _dbname;

public:
  BackupDb(const std::string &dbname, const std::string &db_type, db::Mode mode = db::READ)
      : _db(nullptr), _db_type(db_type), _mode(mode), _dbname(dbname) {
    OpenDb();
  }

  ~BackupDb() { CloseDb(); }

  void RegisterLogCallback(const LogCallback &logCallback) {
    _logCallback = logCallback;
  }

  bool IsOpen() {
    if (_db == nullptr) {
      return false;
    }
    return true;
  }

  // todo: 当目录不存在的时候会crash
  bool OpenDb() {
    std::string db_path;
    if (dal::settings()._localDataPath.empty()) {
      char buff[256] = {0};
      utils::get_running_dir("DataHub.dll", buff);
      dal::settings()._localDataPath.assign(buff);
    }
    if (_db_type.compare("lmdb") == 0) {
      db_path.assign(fmt::format("{}/store/{}", dal::settings()._localDataPath, _dbname));
    } else if (_db_type.compare("leveldb") == 0) {
      db_path.assign(fmt::format("{}/store/{}.db", dal::settings()._localDataPath, _dbname));
    } else {
      if (!_logCallback.empty())
        _logCallback(
            MSG_ERROR,
            fmt::format("don't support history data db type: {}", _db_type));
      return false;
    }

    if (!utils::is_exists_file_path(db_path)) {
      if (utils::create_file_path(db_path) != 0) {
      if (!_logCallback.empty())
        _logCallback(
            MSG_ERROR,
            fmt::format("don't exists file path and create failed: {}", db_path));
        return false;
      }
    }

    // AICM_INFO("open backup db file: {}", db_path);

    _db = db::CreateDB(_db_type, db_path, _mode);
    if (!_db.get()) {
      _db = db::CreateDB(_db_type, db_path, db::NEW);
    }

    if (!_db.get()) {
      if (!_logCallback.empty())
        _logCallback(MSG_ERROR,
                     fmt::format("open history data db error: {}", db_path));
      return false;
    }

    _cursor = _db->NewCursor();
    if (_cursor.get() == nullptr) {
      return false;
    }

    // leveldb::Options options;
    // options.create_if_missing = true;

    // leveldb::Status status = leveldb::DB::Open(options,
    // fmt::format("{}/store/historydata.db", buff), &_db);

    // assert(status.ok());
    // if (!status.ok()) {
    //	return false;
    //}

    return true;
  }

  void CloseDb() {
    _cursor.reset();
    _db.reset();
    // delete _db;
    //_db = nullptr;
  }

  // 期货主连历史行情
  // Day Key: day_label_yyyy
  // Min5 Key: min5_label_yyyy_mm
  std::string GetHistoryDataKey(const std::string &label, BarSize barsize,
                                ExtDate dt) {
    if (dt.Year == 0 || dt.Month == 0) {
      if (!_logCallback.empty()) {
        _logCallback(MSG_ERROR, fmt::format("GetHistoryDataKey Date Error: {:d}", dt.yyyymmdd()));
      }
      return "";
    }

    if (barsize == BarSize::day) {
      return utils::string_format("day_%s_%04d", label.c_str(), dt.Year);
    } else if (barsize == BarSize::min5) {
      return utils::string_format("min5_%s_%04d_%02d", label.c_str(), dt.Year,
                                  dt.Month);
    }
    return "";
  }

  size_t GetValueSize(const std::string &key) {
    // std::unique_ptr<db::Cursor> cursor(_db->NewCursor());
    _cursor->Seek(key);
    if (_cursor->Valid() && _cursor->key() == key) {
      return _cursor->value().size();
    }
    // leveldb::Iterator* it = _db->NewIterator(leveldb::ReadOptions());
    // for (it->SeekToFirst(); it->Valid(); it->Next()) {
    //	if (it->key().ToString().compare(key) == 0) {
    //		return it->value().size();
    //	}
    //}
    return 0;
  }

  void WriteHistoryData(const std::string &label, HistoryDataSeqPtr hds_ptr,
                        BarSize barsize) {
    if (hds_ptr.get() == nullptr || hds_ptr->empty() || _mode != db::WRITE) {
      if (!_logCallback.empty()) {
        _logCallback(MSG_INFO,
                     fmt::format("backup write history data error: {} - {}",
                                 label, barsize));
      }
      return;
    }

    if (barsize != BarSize::day && barsize != BarSize::min5) {
      if (!_logCallback.empty()) {
        _logCallback(
            MSG_INFO,
            fmt::format("backup write history data barsize error: {} - {}",
                        label, barsize));
      }
      return;
    }
    std::unique_ptr<db::Transaction> trans(_db->NewTransaction());

    int pos0 = 0;
    int pos1 = 0;
    while (pos1 < hds_ptr->size()) {
      if ((barsize == BarSize::day &&
           hds_ptr->at(pos0)._date.Year == hds_ptr->at(pos1)._date.Year) ||
          (barsize == BarSize::min5 &&
           hds_ptr->at(pos0)._date.yyyymmdd() / 100 ==
               hds_ptr->at(pos1)._date.yyyymmdd() / 100)) {
        pos1++;
      } else {
        std::string key =
            GetHistoryDataKey(label, barsize, hds_ptr->at(pos0)._date);
        if (key.empty()) {
          pos0 = pos1;
          continue;
        }
        size_t size = GetValueSize(key);
        size_t len = (pos1 - pos0) * sizeof(HistoryData);
        if (size < len) {
          if (size > 0) {
            trans->Delete(key);
            trans->Commit();
          }
          std::string value;
          value.resize(len);
          std::memcpy(&value[0], (char *)&hds_ptr->at(pos0), len);
          trans->Put(key, value);
          trans->Commit();
          if (!_logCallback.empty()) {
            _logCallback(MSG_INFO, fmt::format("backup write data: {}", key));
          }
        }
        pos0 = pos1;
      }
    }

    if (pos0 < pos1) {
      std::string key =
          GetHistoryDataKey(label, barsize, hds_ptr->at(pos0)._date);
      if (key.empty()) {
        return;
      }
      size_t size = GetValueSize(key);
      size_t len = (pos1 - pos0) * sizeof(HistoryData);
      if (size < len) {
        if (size > 0) {
          trans->Delete(key);
          trans->Commit();
        }
        std::string value;
        value.resize(len);
        std::memcpy(&value[0], (char *)&hds_ptr->at(pos0), len);
        trans->Put(key, value);
        trans->Commit();
        if (!_logCallback.empty()) {
          _logCallback(MSG_INFO, fmt::format("backup write history data: {}", key));
        }
      }
    }
  }

  bool Find(const std::string &key) {
    // std::unique_ptr<db::Cursor> cursor(_db->NewCursor());
    _cursor->Seek(key);
    if (_cursor->Valid() && _cursor->key() == key) {
      return true;
    }

    // leveldb::Iterator* it = _db->NewIterator(leveldb::ReadOptions());
    // for (it->SeekToFirst(); it->Valid(); it->Next()) {
    //	if (it->key().ToString().compare(key) == 0) {
    //		return true;
    //	}
    //}
    return false;
  }

  bool Delete(const std::string &key) {
    // std::unique_ptr<db::Cursor> cursor(_db->NewCursor());
    _cursor->Seek(key);

    if (_cursor->Valid() && _cursor->key() == key) {
      std::unique_ptr<db::Transaction> trans(_db->NewTransaction());
      trans->Delete(key);
      trans->Commit();
      return true;
    }

    // leveldb::Iterator* it = _db->NewIterator(leveldb::ReadOptions());
    // for (it->SeekToFirst(); it->Valid(); it->Next()) {
    //	if (it->key().ToString().compare(key) == 0) {
    //		leveldb::Status s = _db->Delete(leveldb::WriteOptions(), key);
    //		if (s.ok()) {
    //			return true;
    //		}
    //	}
    //}
    return false;
  }

  std::vector<std::string> GetKeys() {
    std::vector<std::string> keys;
    // std::unique_ptr<db::Cursor> cursor(_db->NewCursor());
    _cursor->SeekToFirst();
    for (_cursor->SeekToFirst(); _cursor->Valid(); _cursor->Next()) {
      keys.push_back(_cursor->key());
    }

    // leveldb::Iterator* it = _db->NewIterator(leveldb::ReadOptions());
    // for (it->SeekToFirst(); it->Valid(); it->Next()) {
    //	keys.push_back(it->key().ToString());
    //}
    return keys;
  }

  HistoryDataSeqPtr GetHistoryData(const std::string &key) {
    HistoryDataSeqPtr hds_ptr = HistoryDataSeqPtr(new HistoryDataSeq);
    if (key.empty()) {
      return hds_ptr;
    }

    // std::unique_ptr<db::Cursor> cursor(_db->NewCursor());
    _cursor->Seek(key);

    if (_cursor->Valid() && _cursor->key() == key) {
      int len = _cursor->value().size() / sizeof(HistoryData);
      hds_ptr->resize(len);
      memcpy_s(&hds_ptr->at(0), _cursor->value().size(),
               _cursor->value().data(), _cursor->value().size());
    }
    // leveldb::Iterator* it = _db->NewIterator(leveldb::ReadOptions());
    // for (it->SeekToFirst(); it->Valid(); it->Next()) {
    //	if (it->key().ToString().compare(key) == 0) {
    //		int len = it->value().size() / sizeof(HistoryData);
    //		hds_ptr->resize(len);
    //		memcpy_s(&hds_ptr->at(0), it->value().size(), it->value().data(),
    //it->value().size());
    //	}
    //}
    return hds_ptr;
  }

  /*
   增加期货tickdata部分
   tickdata Key 设计:
    期货行情是每秒更新两次，每分钟120个数据，每五分钟作为一组保存一个记录5*120
    time_id: time_t / (5*60)，即5分钟索引
    tick_lxlabel_time_id tickdata每5分钟一个记录
  */
  std::string GetTickDataKey(const std::string &label, time_t dt) {
    if (dt == 0) {
      if (!_logCallback.empty()) {
        _logCallback(MSG_ERROR, fmt::format("GetTickDataKey Date Error: {:d}", dt));
      }
      return "";
    }

    return utils::string_format("tick:%s:%d", label.c_str(), dt/300);
  }

  TickDataSeqPtr GetTickData(const std::string &key) {
    TickDataSeqPtr tds_ptr = boost::make_shared<TickDataSeq>();
    if (key.empty()) {
      return tds_ptr;
    }

    // std::unique_ptr<db::Cursor> cursor(_db->NewCursor());
    _cursor->Seek(key);

    if (_cursor->Valid() && _cursor->key() == key) {
      int len = _cursor->value().size() / sizeof(TickData);
      tds_ptr->resize(len);
      memcpy_s(&tds_ptr->at(0), _cursor->value().size(),
               _cursor->value().data(), _cursor->value().size());
    }
    return tds_ptr;
  }

  void PushTickData(const std::string& label, std::vector<TickData>& tds) {
    // 将盘口数据推送给机器学习API 
    Event event;
    event._type = RL_PUSH_ORDER_BOOK_DATA;
    event._dict.resize(9 + tds.size() * sizeof(TickData));
    memcpy(&event._dict.at(0), label.data(), label.length());
    if (label.length() == 8) {
      event._dict.at(8) = ' ';
    }
    memcpy(&event._dict.at(0) + 9, (char*)&tds.at(0), tds.size() * sizeof(TickData));
    dal::dh().GetEventBus()(event);
    std::cout << fmt::format("Push tick data: {}", label) << std::endl;
  }

  void WriteTickData(const std::string &label, threadsafe_queue<TickData>& tds) {
    if (tds.empty() || _mode != db::WRITE) {
      //std::cout << fmt::format("backup write tick data error: {}, size: {:d}", label, tds.size()) << std::endl;
      if (!_logCallback.empty()) {
        _logCallback(MSG_INFO, fmt::format("backup write tick data error: {}, size: {:d}", label, tds.size()));
      }
      return;
    }

    if (_db == nullptr) {
      return;
    }

    std::unique_ptr<db::Transaction> trans(_db->NewTransaction());
    TickData tdf = tds.wait_front_value();
    std::string key = GetTickDataKey(label, tdf._timestamp);
    size_t len = 0;
    std::vector<TickData> tds1;
    TickData tdb = tds.wait_back_value();
    while (!tds.empty() && key != GetTickDataKey(label, tdb._timestamp)) {
      TickData td;
      tds.wait_and_pop(td);
      tds1.push_back(td);

      if (!tds.empty()) {
        //TODO: crash tds.front()
        TickData tdf0 = tds.wait_front_value();
        if (key != GetTickDataKey(label, tdf0._timestamp)) {
          if (tds1.size() <= 1) {
            tds1.clear();
              continue;
          }
          // 1.写库 2.PushTickData 3.写库&PushTickData
          if (dal::settings()._tick_data_job == 2 || dal::settings()._tick_data_job == 3) {
            PushTickData(label, tds1);
          }

          if (dal::settings()._tick_data_job == 1 || dal::settings()._tick_data_job == 3) {
            std::string value;
            len = tds1.size() * sizeof(TickData);
            value.resize(len);
            std::memcpy(&value[0], (char*)&tds1.at(0), len);
            trans->Put(key, value);
            trans->Commit();
            // std::cout << fmt::format("Write db tick data: {}", label) << std::endl;
          }

          TickData tdf1 = tds.wait_front_value();
          key = GetTickDataKey(label, tdf1._timestamp);
          tds1.clear();
        }
      }
    }

    TickData tdb1 = tds.wait_back_value();
    if (tds.size() > 100 &&
      ExtDate(tdb1._timestamp).hhmm() >= 1459 &&
        ExtDate(tdb1._timestamp).hhmm() <= 1500) {
      TickData tdf2 = tds.wait_front_value();
      key = GetTickDataKey(label, tdf2._timestamp);
        while (!tds.empty()) {
          TickData td;
          tds.wait_and_pop(td);
          tds1.push_back(td);
        }

        // 1.写库 2.PushTickData 3.写库&PushTickData
        if (dal::settings()._tick_data_job == 2 || dal::settings()._tick_data_job == 3) {
          PushTickData(label, tds1);
        }

        // 1.写库 2.PushTickData 3.写库&PushTickData
        if (dal::settings()._tick_data_job == 1 || dal::settings()._tick_data_job == 3) {
          std::string value;
          len = tds1.size() * sizeof(TickData);
          value.resize(len);
          std::memcpy(&value[0], (char*)&tds1.at(0), len);
          trans->Put(key, value);
          trans->Commit();
        }
        tds1.clear();
    }
    //utils::debug_out("wtick append %s: [%d + %d -> %d]\n",
    //                 key.c_str(), size, tds_ptr->size()*sizeof(TickData), len);

  }

  void WriteFactorData(const std::string& label) {
    InstrumentPtr itm_ptr = dal::getInstrument(label);
    if (itm_ptr != nullptr && _db != nullptr) {
      std::string lf = itm_ptr->_long_fd_ptr->to_json();
      std::string sf = itm_ptr->_range_fd_ptr->to_json();
      if (!lf.empty() && !sf.empty()) {
        std::unique_ptr<db::Transaction> trans(_db->NewTransaction());
        std::string lf_key = fmt::format("factor:{}:{:d}:lf", label, ExtDate::Now().to_time_t() / 300);
        std::string sf_key = fmt::format("factor:{}:{:d}:sf", label, ExtDate::Now().to_time_t()/300);
        trans->Put(lf_key, lf);
        trans->Put(sf_key, sf);
        trans->Commit();
      }
    }
  }
};

} // namespace dal
