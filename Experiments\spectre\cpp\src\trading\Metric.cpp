/**
 * @file Metric.cpp
 * @brief Implementation of performance metrics calculation functions
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Metric.h"
#include <torch/torch.h>
#include <cmath>
#include <algorithm>
#include <unordered_map>

namespace Spectre {
namespace Trading {
namespace Metric {

std::tuple<torch::Tensor, torch::Tensor> drawdown(const torch::Tensor& cumulative_returns) {
    auto max_ret = torch::cummax(cumulative_returns, 0);
    auto dd = cumulative_returns / std::get<0>(max_ret) - 1.0;
    
    // Calculate drawdown duration
    auto dd_cpu = dd.cpu();
    auto dd_accessor = dd_cpu.accessor<double, 1>();
    
    std::vector<int64_t> duration;
    int64_t current_duration = 0;
    
    for (int64_t i = 0; i < dd.size(0); ++i) {
        if (std::abs(dd_accessor[i]) < 1e-10) { // Close to zero
            current_duration = 0;
        } else {
            current_duration++;
        }
        duration.push_back(current_duration);
    }
    
    auto dd_duration = torch::from_blob(duration.data(), {static_cast<int64_t>(duration.size())}, 
                                       torch::kLong).clone();
    
    return std::make_tuple(dd, dd_duration);
}

double sharpe_ratio(const torch::Tensor& daily_returns, double annual_risk_free_rate) {
    auto risk_adj_ret = daily_returns - (annual_risk_free_rate / 252.0);
    double annual_factor = std::sqrt(252.0);
    
    auto mean_ret = torch::mean(risk_adj_ret).item<double>();
    auto std_ret = torch::std(risk_adj_ret, /*unbiased=*/true).item<double>();
    
    if (std_ret == 0.0) {
        return 0.0;
    }
    
    return annual_factor * mean_ret / std_ret;
}

torch::Tensor turnover(const torch::Tensor& positions, const torch::Tensor& transactions) {
    if (transactions.size(0) == 0) {
        return torch::zeros({0});
    }
    
    // This is a simplified implementation
    // In practice, you'd need to implement proper turnover calculation
    // based on transaction amounts and portfolio values
    
    // Placeholder implementation
    auto value_trades = torch::abs(transactions);
    return value_trades / torch::sum(positions, 1, true);
}

double annual_volatility(const torch::Tensor& daily_returns) {
    double annual_factor = std::sqrt(252.0);
    auto volatility = torch::std(daily_returns, /*unbiased=*/true).item<double>();
    return annual_factor * volatility;
}

double max_drawdown(const torch::Tensor& cumulative_returns) {
    auto [dd, _] = drawdown(cumulative_returns);
    return torch::min(dd).item<double>();
}

double calmar_ratio(const torch::Tensor& daily_returns) {
    auto cumulative_returns = torch::cumsum(daily_returns, 0);
    double annual_return = torch::mean(daily_returns).item<double>() * 252.0;
    double max_dd = std::abs(max_drawdown(cumulative_returns));
    
    if (max_dd == 0.0) {
        return 0.0;
    }
    
    return annual_return / max_dd;
}

double sortino_ratio(const torch::Tensor& daily_returns, double annual_risk_free_rate) {
    auto risk_adj_ret = daily_returns - (annual_risk_free_rate / 252.0);
    double annual_factor = std::sqrt(252.0);
    
    auto mean_ret = torch::mean(risk_adj_ret).item<double>();
    
    // Calculate downside deviation
    auto negative_returns = torch::where(risk_adj_ret < 0, risk_adj_ret, torch::zeros_like(risk_adj_ret));
    auto downside_std = torch::std(negative_returns, /*unbiased=*/true).item<double>();
    
    if (downside_std == 0.0) {
        return 0.0;
    }
    
    return annual_factor * mean_ret / downside_std;
}

double information_ratio(const torch::Tensor& returns, const torch::Tensor& benchmark_returns) {
    if (returns.size(0) != benchmark_returns.size(0)) {
        throw std::invalid_argument("Returns and benchmark must have same length");
    }
    
    auto excess_returns = returns - benchmark_returns;
    auto mean_excess = torch::mean(excess_returns).item<double>();
    auto std_excess = torch::std(excess_returns, /*unbiased=*/true).item<double>();
    
    if (std_excess == 0.0) {
        return 0.0;
    }
    
    return mean_excess / std_excess * std::sqrt(252.0);
}

double beta(const torch::Tensor& returns, const torch::Tensor& benchmark_returns) {
    if (returns.size(0) != benchmark_returns.size(0)) {
        throw std::invalid_argument("Returns and benchmark must have same length");
    }
    
    auto covariance = torch::mean((returns - torch::mean(returns)) * 
                                 (benchmark_returns - torch::mean(benchmark_returns))).item<double>();
    auto benchmark_var = torch::var(benchmark_returns, /*unbiased=*/true).item<double>();
    
    if (benchmark_var == 0.0) {
        return 0.0;
    }
    
    return covariance / benchmark_var;
}

double alpha(const torch::Tensor& returns, const torch::Tensor& benchmark_returns, 
             double risk_free_rate) {
    double portfolio_return = torch::mean(returns).item<double>() * 252.0;
    double benchmark_return = torch::mean(benchmark_returns).item<double>() * 252.0;
    double beta_coeff = beta(returns, benchmark_returns);
    
    return portfolio_return - (risk_free_rate + beta_coeff * (benchmark_return - risk_free_rate));
}

double value_at_risk(const torch::Tensor& returns, double confidence_level) {
    auto sorted_result = torch::sort(returns);
    auto sorted_returns = std::get<0>(sorted_result);
    int64_t index = static_cast<int64_t>(confidence_level * returns.size(0));
    return sorted_returns[index].item<double>();
}

double conditional_value_at_risk(const torch::Tensor& returns, double confidence_level) {
    auto sorted_result = torch::sort(returns);
    auto sorted_returns = std::get<0>(sorted_result);
    int64_t index = static_cast<int64_t>(confidence_level * returns.size(0));
    auto tail_returns = sorted_returns.slice(0, 0, index);
    return torch::mean(tail_returns).item<double>();
}

double win_rate(const torch::Tensor& returns) {
    auto positive_returns = returns > 0;
    return torch::mean(positive_returns.to(torch::kDouble)).item<double>();
}

double profit_factor(const torch::Tensor& returns) {
    auto positive_returns = torch::where(returns > 0, returns, torch::zeros_like(returns));
    auto negative_returns = torch::where(returns < 0, returns, torch::zeros_like(returns));
    
    double gross_profit = torch::sum(positive_returns).item<double>();
    double gross_loss = std::abs(torch::sum(negative_returns).item<double>());
    
    if (gross_loss == 0.0) {
        return gross_profit > 0 ? std::numeric_limits<double>::infinity() : 0.0;
    }
    
    return gross_profit / gross_loss;
}

std::tuple<double, double> win_loss_ratio(const torch::Tensor& returns) {
    auto positive_returns = torch::where(returns > 0, returns, torch::zeros_like(returns));
    auto negative_returns = torch::where(returns < 0, returns, torch::zeros_like(returns));
    
    auto positive_mask = returns > 0;
    auto negative_mask = returns < 0;
    
    double avg_win = 0.0;
    double avg_loss = 0.0;
    
    if (torch::sum(positive_mask).item<int64_t>() > 0) {
        avg_win = torch::sum(positive_returns).item<double>() / torch::sum(positive_mask).item<int64_t>();
    }
    
    if (torch::sum(negative_mask).item<int64_t>() > 0) {
        avg_loss = torch::sum(negative_returns).item<double>() / torch::sum(negative_mask).item<int64_t>();
    }
    
    return std::make_tuple(avg_win, std::abs(avg_loss));
}

std::unordered_map<std::string, double> calculate_performance_metrics(
    const torch::Tensor& returns,
    const torch::Tensor& benchmark_returns,
    double risk_free_rate) {
    
    std::unordered_map<std::string, double> metrics;
    
    // Basic metrics
    metrics["total_return"] = torch::sum(returns).item<double>();
    metrics["annual_return"] = torch::mean(returns).item<double>() * 252.0;
    metrics["annual_volatility"] = annual_volatility(returns);
    metrics["sharpe_ratio"] = sharpe_ratio(returns, risk_free_rate);
    metrics["sortino_ratio"] = sortino_ratio(returns, risk_free_rate);
    metrics["calmar_ratio"] = calmar_ratio(returns);
    
    // Drawdown metrics
    auto cumulative_returns = torch::cumsum(returns, 0);
    metrics["max_drawdown"] = max_drawdown(cumulative_returns);
    
    // Risk metrics
    metrics["var_95"] = value_at_risk(returns, 0.05);
    metrics["cvar_95"] = conditional_value_at_risk(returns, 0.05);
    
    // Win/Loss metrics
    metrics["win_rate"] = win_rate(returns);
    metrics["profit_factor"] = profit_factor(returns);
    
    auto [avg_win, avg_loss] = win_loss_ratio(returns);
    metrics["average_win"] = avg_win;
    metrics["average_loss"] = avg_loss;
    
    // Benchmark-relative metrics (if benchmark provided)
    if (benchmark_returns.defined() && benchmark_returns.size(0) > 0) {
        metrics["beta"] = beta(returns, benchmark_returns);
        metrics["alpha"] = alpha(returns, benchmark_returns, risk_free_rate);
        metrics["information_ratio"] = information_ratio(returns, benchmark_returns);
    }
    
    return metrics;
}

} // namespace Metric
} // namespace Trading
} // namespace Spectre
