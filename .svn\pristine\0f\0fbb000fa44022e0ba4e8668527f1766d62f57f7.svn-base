#pragma once
#include <string>
#include <vector>
#include <map>
#include <LightGBM/c_api.h>
#include <torch/torch.h>
#include <torch/script.h>
#include <TradeSystem.h>

namespace bll {

using TorchModule = torch::jit::script::Module;
class ModelHelper
{
public:
	ModelHelper ();
	~ModelHelper();

	double Predict(const std::string& model_id, std::vector<float>& data, int embedding = -1);
	double Gbdt(const std::string& model_id, std::vector<float>& data, int embedding = -1);
	ModelType GetModelType(const std::string& model_id);
	void Release();

private:
	
	int Init();
	TorchModule CreateTorchModel(const std::string& model_file);

	BoosterHandle CreateLgbmModel(const std::string& model_file);
	int PredictForMatSingleRow(BoosterHandle handle, const std::vector<float>& data, int64_t& out_len, double& out_result);
	int PredictForMatSingleRowFastInit(BoosterHandle handle, int ncol, FastConfigHandle& out_fastConfig);
	int PredictForMatSingleRowFast(FastConfigHandle handle, const std::vector<float>& data, int64_t& out_len, double& out_result);

	//int PredictForCSRSingleRow();
	//int PredictForCSR();
	//int PredictForMat();
	//int PredictForFile();
	//ModuleType _mt;
	std::map<std::string, ModelType> _mts;
	std::map<std::string, BoosterHandle> _boosters;
	std::map<std::string, FastConfigHandle> _fast_configs;
	std::map<std::string, torch::jit::script::Module> _torchmds;
};

}

